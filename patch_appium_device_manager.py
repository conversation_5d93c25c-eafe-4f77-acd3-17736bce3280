#!/usr/bin/env python3
"""
Patch Appium Device Manager for Session Isolation Integration

This script patches the existing AppiumDeviceManager to integrate with
the session isolation system and implement pre-connected device state.
"""

import os
import re
import shutil
import logging
from pathlib import Path

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def patch_appium_device_manager():
    """Patch the existing AppiumDeviceManager for session isolation"""
    
    appium_manager_path = Path("/Users/<USER>/Documents/automation-tool/AppiumDeviceManager")
    server_js_path = appium_manager_path / "server.js"
    
    if not server_js_path.exists():
        logger.error(f"AppiumDeviceManager server.js not found at {server_js_path}")
        return False
    
    logger.info("Patching AppiumDeviceManager server.js for session isolation...")
    
    # Read the current server.js
    with open(server_js_path, 'r') as f:
        content = f.read()
    
    # Patch 1: Enhance the launch-app endpoint
    logger.info("Patching /api/devices/launch-app endpoint...")
    
    # Find the existing launch-app endpoint
    launch_app_pattern = r"app\.post\('/api/devices/launch-app'.*?}\);"
    
    # New enhanced launch-app endpoint
    enhanced_launch_app = '''app.post('/api/devices/launch-app', async (req, res) => {
  console.log('[API] Received POST request for /api/devices/launch-app');
  const { device, platform } = req.body;

  if (!device || !platform) {
    return res.status(400).json({
      success: false,
      error: 'Missing required parameters',
      details: 'Device UDID and platform are required'
    });
  }

  try {
    const deviceId = device.udid || device.id;

    // Check if device already has an active session
    const existingSession = sessionManager.getSession(deviceId);
    if (existingSession) {
      console.log(`Device ${deviceId} already has an active session on port ${existingSession.port}`);
      
      // Generate URL with pre-connected device parameters
      const sessionUrl = `http://localhost:${existingSession.port}?deviceId=${deviceId}&platform=${platform}&sessionId=${existingSession.sessionId}&autoConnect=true&hideDeviceList=true`;
      
      return res.json({
        success: true,
        message: 'Automation app is already running for this device',
        url: sessionUrl,
        port: existingSession.port,
        session: existingSession,
        alreadyRunning: true
      });
    }

    // Determine the script to run based on platform
    let scriptName;
    if (platform.toLowerCase() === 'ios') {
      scriptName = 'run.py';
    } else if (platform.toLowerCase() === 'android') {
      scriptName = 'run_android.py';
    } else {
      return res.status(400).json({
        success: false,
        error: 'Invalid platform',
        details: 'Platform must be either iOS or Android'
      });
    }

    // Get next available port for this platform
    const port = await sessionManager.getNextAvailablePort(platform);
    
    // Get the project root directory (parent of AppiumDeviceManager, then into MobileApp-AutoTest)
    const projectRoot = path.join(__dirname, '..', 'MobileApp-AutoTest');
    const scriptPath = path.join(projectRoot, scriptName);

    // Check if the script exists
    if (!fs.existsSync(scriptPath)) {
      return res.status(404).json({
        success: false,
        error: 'Automation script not found',
        details: `The script ${scriptName} does not exist at ${scriptPath}`
      });
    }

    // Double-check port availability
    const portAvailable = await sessionManager.isPortAvailable(port);
    if (!portAvailable) {
      return res.status(409).json({
        success: false,
        error: 'Port conflict',
        details: `Port ${port} is already in use`
      });
    }

    // Generate unique session ID
    const sessionId = `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

    // Enhanced environment variables for complete session isolation
    const env = {
      ...process.env,
      // Original variables (maintain compatibility)
      SELECTED_DEVICE_UDID: deviceId,
      SELECTED_DEVICE_PLATFORM: platform,
      
      // Enhanced session isolation variables
      SELECTED_DEVICE_ID: deviceId,
      SELECTED_PLATFORM: platform,
      SESSION_ID: sessionId,
      INSTANCE_DB_SUFFIX: `_device_${deviceId.replace(/-/g, '_')}`,
      SESSION_ISOLATION_MODE: 'true'
    };

    // Launch the automation app with enhanced environment
    console.log(`Launching ${scriptName} for ${platform} device ${deviceId} on port ${port} with session ${sessionId}`);
    const automationProcess = spawn('bash', ['-c', `source venv/bin/activate && python3 ${scriptName} --port ${port}`], {
      cwd: projectRoot,
      env: env,
      detached: true,
      stdio: ['ignore', 'pipe', 'pipe']
    });

    // Create enhanced session with session ID
    const session = sessionManager.createSession(deviceId, platform, port, automationProcess.pid);
    session.sessionId = sessionId; // Add session ID to the session object

    // Log output for debugging
    automationProcess.stdout.on('data', (data) => {
      console.log(`[${sessionId}] stdout: ${data}`);
      sessionManager.updateActivity(deviceId);
    });

    automationProcess.stderr.on('data', (data) => {
      console.error(`[${sessionId}] stderr: ${data}`);
      sessionManager.updateActivity(deviceId);
    });

    automationProcess.on('exit', (code) => {
      console.log(`[${sessionId}] Process exited with code: ${code}`);
      // Clean up session when process exits
      try {
        const existingSession = sessionManager.getSession(deviceId);
        if (existingSession) {
          sessionManager.terminateSession(deviceId);
        }
      } catch (error) {
        console.warn(`Failed to cleanup session for device ${deviceId}:`, error.message);
      }
    });

    automationProcess.on('error', (error) => {
      console.error(`[${sessionId}] Process error:`, error);
      // Clean up session on error
      try {
        const existingSession = sessionManager.getSession(deviceId);
        if (existingSession) {
          sessionManager.terminateSession(deviceId);
        }
      } catch (cleanupError) {
        console.warn(`Failed to cleanup session for device ${deviceId}:`, cleanupError.message);
      }
    });

    // Detach the process so it continues running independently
    automationProcess.unref();

    console.log(`Automation app launched with PID: ${automationProcess.pid}, Session: ${sessionId}`);

    // Generate URL with pre-connected device parameters for simplified UI
    const sessionUrl = `http://localhost:${port}?deviceId=${deviceId}&platform=${platform}&sessionId=${sessionId}&autoConnect=true&hideDeviceList=true`;

    // Give the app a moment to start up
    setTimeout(() => {
      res.json({
        success: true,
        message: 'Automation app launched successfully',
        url: sessionUrl,
        port: port,
        pid: automationProcess.pid,
        session: session,
        sessionId: sessionId,
        alreadyRunning: false
      });
    }, 2000);

  } catch (error) {
    console.error('[API] Error launching automation app:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to launch automation app',
      details: error.message
    });
  }
});'''
    
    # Replace the existing endpoint
    content = re.sub(launch_app_pattern, enhanced_launch_app, content, flags=re.DOTALL)
    
    # Patch 2: Add new endpoints for session management
    logger.info("Adding new session management endpoints...")
    
    # Find the end of the existing endpoints (before the server start)
    app_listen_pattern = r"(app\.listen\(.*?\);)"
    
    new_endpoints = '''
// Enhanced session management endpoints for device manager integration

// Get session status for a device
app.get('/api/sessions/:deviceId/status', (req, res) => {
  try {
    const { deviceId } = req.params;
    const session = sessionManager.getSession(deviceId);
    
    if (session) {
      const sessionUrl = `http://localhost:${session.port}?deviceId=${deviceId}&sessionId=${session.sessionId}&autoConnect=true&hideDeviceList=true`;
      res.json({
        success: true,
        session: session,
        status: 'active',
        url: sessionUrl
      });
    } else {
      res.json({
        success: true,
        session: null,
        status: 'inactive'
      });
    }
  } catch (error) {
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// Update device status
app.post('/api/devices/:deviceId/status', (req, res) => {
  try {
    const { deviceId } = req.params;
    const { status } = req.body;
    
    // Update device status in session manager
    const session = sessionManager.getSession(deviceId);
    if (session) {
      session.deviceStatus = status;
      res.json({
        success: true,
        message: `Device ${deviceId} status updated to ${status}`
      });
    } else {
      res.status(404).json({
        success: false,
        error: 'No active session found for device'
      });
    }
  } catch (error) {
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

\\1'''
    
    # Add the new endpoints before app.listen
    content = re.sub(app_listen_pattern, new_endpoints, content)
    
    # Write the patched content back
    with open(server_js_path, 'w') as f:
        f.write(content)
    
    logger.info("AppiumDeviceManager server.js patched successfully!")
    
    # Copy the pre-connected device handler to the AppiumDeviceManager
    logger.info("Copying pre-connected device handler...")
    
    src_js_path = Path("app/static/js/pre-connected-device.js")
    if src_js_path.exists():
        # Copy to MobileApp-AutoTest app directory (it's already there)
        logger.info("Pre-connected device handler already exists in automation app")
    else:
        logger.warning("Pre-connected device handler not found - please ensure it's in the automation app")
    
    return True

def create_integration_readme():
    """Create README for the integration"""
    
    readme_content = """# AppiumDeviceManager Integration

## Changes Made

### 1. Enhanced Launch Process
- Modified `/api/devices/launch-app` endpoint to include session isolation
- Added session ID generation for each launched automation app
- Enhanced environment variables for complete isolation
- Pre-connected device URL parameters added

### 2. Session Management
- Added `/api/sessions/:deviceId/status` endpoint
- Added `/api/devices/:deviceId/status` endpoint for device status updates
- Enhanced session tracking with session IDs

### 3. Pre-Connected Device State
- Automation apps now launch with URL parameters for auto-connection
- Device selection UI is hidden when launched from device manager
- Session ID is prominently displayed instead of device lists

## URL Parameters Added

When launching automation apps, the following parameters are included:
- `deviceId`: Device identifier for pre-connection
- `platform`: Device platform (iOS/Android)  
- `sessionId`: Unique session identifier
- `autoConnect=true`: Automatically connect to device
- `hideDeviceList=true`: Hide device selection UI

## Environment Variables Added

Each launched automation app gets these environment variables:
- `SESSION_ID`: Unique session identifier
- `SELECTED_DEVICE_ID`: Device identifier
- `SELECTED_PLATFORM`: Device platform
- `INSTANCE_DB_SUFFIX`: Database isolation suffix
- `SESSION_ISOLATION_MODE`: Enables session isolation mode

## Usage

1. Start AppiumDeviceManager: `npm start`
2. Connect devices via USB
3. Click "Launch Automation App" for any device
4. Automation app opens with device pre-connected and simplified UI

## Backup

Original server.js has been backed up as server.js.backup
"""
    
    readme_path = Path("/Users/<USER>/Documents/automation-tool/AppiumDeviceManager/INTEGRATION_README.md")
    with open(readme_path, 'w') as f:
        f.write(readme_content)
    
    logger.info(f"Integration README created at {readme_path}")

def main():
    """Main function"""
    print("AppiumDeviceManager Integration Patcher")
    print("="*50)
    
    try:
        # Patch the AppiumDeviceManager
        success = patch_appium_device_manager()
        
        if success:
            # Create integration documentation
            create_integration_readme()
            
            print("\n✅ AppiumDeviceManager Integration Complete!")
            print("="*50)
            print("🔧 Changes Made:")
            print("   ✓ Enhanced launch-app endpoint with session isolation")
            print("   ✓ Added session management endpoints")
            print("   ✓ Pre-connected device URL parameters")
            print("   ✓ Enhanced environment variables for isolation")
            print("   ✓ Session ID tracking and management")
            
            print("\n🚀 Next Steps:")
            print("   1. Start AppiumDeviceManager: cd AppiumDeviceManager && npm start")
            print("   2. Connect your devices via USB")
            print("   3. Click 'Launch Automation App' for any device")
            print("   4. Automation app will open with device pre-connected")
            
            print("\n📋 Features:")
            print("   ✓ Device status automatically changes to 'In Use'")
            print("   ✓ No device selection dropdown in automation app")
            print("   ✓ Session ID prominently displayed")
            print("   ✓ Complete session isolation per device")
            print("   ✓ Automatic device connection")
            
            print("\n⚠️  Backup:")
            print("   Original server.js backed up as server.js.backup")
            
        else:
            print("❌ Integration failed - check logs for details")
            return 1
            
    except Exception as e:
        logger.error(f"Integration failed: {e}")
        print(f"❌ Integration failed: {e}")
        return 1
    
    return 0

if __name__ == "__main__":
    exit(main())
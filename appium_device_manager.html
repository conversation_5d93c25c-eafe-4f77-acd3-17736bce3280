<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Appium Device Manager</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.css">
    <style>
        .device-manager-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem 0;
            margin-bottom: 2rem;
        }
        
        .device-card {
            border: none;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            transition: transform 0.2s, box-shadow 0.2s;
            margin-bottom: 1.5rem;
        }
        
        .device-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }
        
        .device-status {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            display: inline-block;
            margin-right: 8px;
        }
        
        .status-available {
            background-color: #28a745;
            animation: pulse 2s infinite;
        }
        
        .status-in-use {
            background-color: #ffc107;
        }
        
        .status-offline {
            background-color: #dc3545;
        }
        
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }
        
        .device-info {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 1rem;
            margin-bottom: 1rem;
        }
        
        .device-actions {
            display: flex;
            gap: 0.5rem;
            flex-wrap: wrap;
        }
        
        .launch-btn {
            background: linear-gradient(135deg, #28a745, #20c997);
            border: none;
            color: white;
            padding: 0.75rem 1.5rem;
            border-radius: 8px;
            font-weight: 600;
            transition: all 0.3s;
        }
        
        .launch-btn:hover {
            background: linear-gradient(135deg, #218838, #1ea085);
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(40, 167, 69, 0.3);
        }
        
        .launch-btn:disabled {
            background: #6c757d;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }
        
        .refresh-btn {
            position: fixed;
            bottom: 2rem;
            right: 2rem;
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background: linear-gradient(135deg, #007bff, #0056b3);
            border: none;
            color: white;
            box-shadow: 0 4px 15px rgba(0,123,255,0.3);
            z-index: 1000;
        }
        
        .refresh-btn:hover {
            background: linear-gradient(135deg, #0056b3, #004085);
            transform: scale(1.1);
        }
        
        .session-indicator {
            position: absolute;
            top: 10px;
            right: 10px;
            background: rgba(0,123,255,0.9);
            color: white;
            padding: 0.25rem 0.75rem;
            border-radius: 15px;
            font-size: 0.75rem;
            font-weight: bold;
        }
        
        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 9999;
        }
        
        .loading-content {
            background: white;
            padding: 2rem;
            border-radius: 12px;
            text-align: center;
            max-width: 400px;
        }
        
        .progress-steps {
            margin-top: 1rem;
        }
        
        .progress-step {
            padding: 0.5rem 0;
            border-bottom: 1px solid #dee2e6;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }
        
        .progress-step:last-child {
            border-bottom: none;
        }
        
        .step-status {
            width: 20px;
            height: 20px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.75rem;
        }
        
        .step-pending {
            background: #e9ecef;
            color: #6c757d;
        }
        
        .step-running {
            background: #007bff;
            color: white;
            animation: spin 1s linear infinite;
        }
        
        .step-complete {
            background: #28a745;
            color: white;
        }
        
        .step-error {
            background: #dc3545;
            color: white;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .device-platform-badge {
            position: absolute;
            top: 10px;
            left: 10px;
            padding: 0.25rem 0.5rem;
            border-radius: 12px;
            font-size: 0.75rem;
            font-weight: bold;
        }
        
        .platform-ios {
            background: #007bff;
            color: white;
        }
        
        .platform-android {
            background: #28a745;
            color: white;
        }
    </style>
</head>
<body>
    <!-- Loading Overlay -->
    <div id="loadingOverlay" class="loading-overlay" style="display: none;">
        <div class="loading-content">
            <div class="spinner-border text-primary mb-3" role="status">
                <span class="visually-hidden">Loading...</span>
            </div>
            <h5 id="loadingTitle">Launching Automation App</h5>
            <p id="loadingMessage">Initializing session...</p>
            
            <div class="progress-steps" id="progressSteps">
                <div class="progress-step">
                    <span>Creating isolated session</span>
                    <div class="step-status step-pending" id="step1">1</div>
                </div>
                <div class="progress-step">
                    <span>Allocating ports</span>
                    <div class="step-status step-pending" id="step2">2</div>
                </div>
                <div class="progress-step">
                    <span>Starting Appium server</span>
                    <div class="step-status step-pending" id="step3">3</div>
                </div>
                <div class="progress-step">
                    <span>Initializing database</span>
                    <div class="step-status step-pending" id="step4">4</div>
                </div>
                <div class="progress-step">
                    <span>Starting Flask server</span>
                    <div class="step-status step-pending" id="step5">5</div>
                </div>
                <div class="progress-step">
                    <span>Opening automation interface</span>
                    <div class="step-status step-pending" id="step6">6</div>
                </div>
            </div>
            
            <button class="btn btn-outline-secondary mt-3" onclick="cancelLaunch()" id="cancelBtn">
                Cancel
            </button>
        </div>
    </div>

    <div class="device-manager-header">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h1 class="mb-0">
                        <i class="bi bi-phone me-2"></i>
                        Appium Device Manager
                    </h1>
                    <p class="mb-0 mt-2 opacity-75">Manage and launch automation sessions for connected devices</p>
                </div>
                <div class="col-md-4 text-end">
                    <button class="btn btn-light btn-lg" onclick="refreshDevices()">
                        <i class="bi bi-arrow-clockwise me-2"></i>
                        Refresh Devices
                    </button>
                </div>
            </div>
        </div>
    </div>

    <div class="container">
        <!-- Device Grid -->
        <div class="row" id="deviceGrid">
            <!-- Devices will be populated here -->
        </div>
        
        <!-- No Devices Message -->
        <div id="noDevicesMessage" class="text-center py-5" style="display: none;">
            <i class="bi bi-phone-x display-1 text-muted mb-3"></i>
            <h3 class="text-muted">No Devices Found</h3>
            <p class="text-muted">Please connect your iOS or Android devices and click refresh.</p>
            <button class="btn btn-primary btn-lg" onclick="refreshDevices()">
                <i class="bi bi-arrow-clockwise me-2"></i>
                Refresh Devices
            </button>
        </div>
    </div>

    <!-- Floating Refresh Button -->
    <button class="refresh-btn" onclick="refreshDevices()" title="Refresh Devices">
        <i class="bi bi-arrow-clockwise"></i>
    </button>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Global variables
        let devices = [];
        let activeSessions = {};
        let launchInProgress = false;
        
        // Initialize on page load
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Appium Device Manager initialized');
            refreshDevices();
            
            // Auto-refresh every 30 seconds
            setInterval(refreshDevices, 30000);
        });
        
        // Refresh devices from the unified discovery system
        async function refreshDevices() {
            console.log('Refreshing devices...');
            
            try {
                const response = await fetch('/api/unified/devices');
                if (response.ok) {
                    const data = await response.json();
                    devices = data.devices || [];
                    updateDeviceGrid();
                } else {
                    console.error('Failed to fetch devices:', response.statusText);
                    showError('Failed to fetch devices. Please check your connection.');
                }
            } catch (error) {
                console.error('Error fetching devices:', error);
                showError('Error connecting to device discovery service.');
            }
        }
        
        // Update the device grid display
        function updateDeviceGrid() {
            const grid = document.getElementById('deviceGrid');
            const noDevicesMsg = document.getElementById('noDevicesMessage');
            
            if (devices.length === 0) {
                grid.innerHTML = '';
                noDevicesMsg.style.display = 'block';
                return;
            }
            
            noDevicesMsg.style.display = 'none';
            
            grid.innerHTML = devices.map(device => createDeviceCard(device)).join('');
        }
        
        // Create device card HTML
        function createDeviceCard(device) {
            const deviceId = device.id;
            const deviceName = device.name || `${device.platform} Device`;
            const platform = device.platform || 'Unknown';
            const osVersion = device.osVersion || 'Unknown';
            const model = device.model || 'Unknown';
            
            // Determine device status
            const sessionId = activeSessions[deviceId];
            const status = sessionId ? 'in-use' : 'available';
            const statusText = sessionId ? 'In Use' : 'Available';
            const statusClass = sessionId ? 'status-in-use' : 'status-available';
            
            return `
                <div class="col-lg-4 col-md-6">
                    <div class="card device-card">
                        <div class="card-body position-relative">
                            <div class="device-platform-badge platform-${platform.toLowerCase()}">
                                ${platform}
                            </div>
                            
                            ${sessionId ? `<div class="session-indicator">${sessionId.substring(0, 12)}...</div>` : ''}
                            
                            <h5 class="card-title mt-4">
                                <span class="device-status ${statusClass}"></span>
                                ${deviceName}
                            </h5>
                            
                            <div class="device-info">
                                <div class="row">
                                    <div class="col-6">
                                        <small class="text-muted">Device ID:</small><br>
                                        <code class="small">${deviceId.substring(0, 12)}...</code>
                                    </div>
                                    <div class="col-6">
                                        <small class="text-muted">OS Version:</small><br>
                                        <span class="small">${osVersion}</span>
                                    </div>
                                </div>
                                <div class="row mt-2">
                                    <div class="col-6">
                                        <small class="text-muted">Model:</small><br>
                                        <span class="small">${model}</span>
                                    </div>
                                    <div class="col-6">
                                        <small class="text-muted">Status:</small><br>
                                        <span class="badge bg-${status === 'in-use' ? 'warning' : 'success'}">${statusText}</span>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="device-actions">
                                ${status === 'available' ? 
                                    `<button class="btn launch-btn flex-fill" onclick="launchAutomationApp('${deviceId}', '${platform}')">
                                        <i class="bi bi-rocket-takeoff me-2"></i>
                                        Launch Automation App
                                    </button>` :
                                    `<button class="btn btn-outline-primary flex-fill" onclick="openSession('${sessionId}')">
                                        <i class="bi bi-box-arrow-up-right me-2"></i>
                                        Open Session
                                    </button>
                                    <button class="btn btn-outline-danger" onclick="terminateSession('${sessionId}')">
                                        <i class="bi bi-stop-circle me-1"></i>
                                        Stop
                                    </button>`
                                }
                            </div>
                        </div>
                    </div>
                </div>
            `;
        }
        
        // Launch automation app for a device
        async function launchAutomationApp(deviceId, platform) {
            if (launchInProgress) {
                showError('Another launch is already in progress. Please wait.');
                return;
            }
            
            launchInProgress = true;
            showLoadingOverlay();
            
            try {
                console.log(`Launching automation app for device: ${deviceId} (${platform})`);
                
                // Step 1: Create isolated session
                updateProgressStep(1, 'running');
                updateLoadingMessage('Creating isolated session...');
                
                const sessionResponse = await fetch('/api/device-manager/launch', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        device_id: deviceId,
                        platform: platform
                    })
                });
                
                if (!sessionResponse.ok) {
                    throw new Error(`Failed to create session: ${sessionResponse.statusText}`);
                }
                
                const sessionData = await sessionResponse.json();
                updateProgressStep(1, 'complete');
                
                if (!sessionData.success) {
                    throw new Error(sessionData.error || 'Failed to create session');
                }
                
                const sessionId = sessionData.session_id;
                const sessionUrl = sessionData.session_url;
                
                // Step 2: Port allocation (already done in session creation)
                updateProgressStep(2, 'complete');
                
                // Step 3-5: Monitor startup progress
                await monitorStartupProgress(sessionId);
                
                // Step 6: Open automation interface
                updateProgressStep(6, 'running');
                updateLoadingMessage('Opening automation interface...');
                
                // Update device status
                activeSessions[deviceId] = sessionId;
                
                // Open the session URL in a new window
                window.open(sessionUrl, '_blank');
                
                updateProgressStep(6, 'complete');
                updateLoadingMessage('Automation app launched successfully!');
                
                // Hide loading overlay after a short delay
                setTimeout(() => {
                    hideLoadingOverlay();
                    updateDeviceGrid(); // Refresh the grid to show updated status
                }, 2000);
                
            } catch (error) {
                console.error('Error launching automation app:', error);
                showError(`Failed to launch automation app: ${error.message}`);
                hideLoadingOverlay();
            } finally {
                launchInProgress = false;
            }
        }
        
        // Monitor startup progress
        async function monitorStartupProgress(sessionId) {
            const steps = [
                { step: 3, message: 'Starting Appium server...' },
                { step: 4, message: 'Initializing database...' },
                { step: 5, message: 'Starting Flask server...' }
            ];
            
            for (const { step, message } of steps) {
                updateProgressStep(step, 'running');
                updateLoadingMessage(message);
                
                // Poll session status
                let attempts = 0;
                const maxAttempts = 30; // 30 seconds timeout
                
                while (attempts < maxAttempts) {
                    try {
                        const response = await fetch(`/api/session/${sessionId}/info`);
                        if (response.ok) {
                            const data = await response.json();
                            if (data.success && data.status === 'running') {
                                updateProgressStep(step, 'complete');
                                break;
                            }
                        }
                    } catch (error) {
                        console.log(`Polling attempt ${attempts + 1} failed:`, error.message);
                    }
                    
                    await new Promise(resolve => setTimeout(resolve, 1000));
                    attempts++;
                }
                
                if (attempts >= maxAttempts) {
                    updateProgressStep(step, 'error');
                    throw new Error(`Timeout waiting for step: ${message}`);
                }
            }
        }
        
        // Open existing session
        function openSession(sessionId) {
            // Get session info and open URL
            fetch(`/api/session/${sessionId}/info`)
                .then(response => response.json())
                .then(data => {
                    if (data.success && data.url) {
                        window.open(data.url, '_blank');
                    } else {
                        showError('Session URL not available');
                    }
                })
                .catch(error => {
                    console.error('Error opening session:', error);
                    showError('Failed to open session');
                });
        }
        
        // Terminate session
        async function terminateSession(sessionId) {
            if (!confirm('Are you sure you want to terminate this session?')) {
                return;
            }
            
            try {
                const response = await fetch(`/api/session/${sessionId}/stop`, {
                    method: 'POST'
                });
                
                if (response.ok) {
                    // Remove from active sessions
                    for (const [deviceId, sid] of Object.entries(activeSessions)) {
                        if (sid === sessionId) {
                            delete activeSessions[deviceId];
                            break;
                        }
                    }
                    
                    updateDeviceGrid();
                    showSuccess('Session terminated successfully');
                } else {
                    showError('Failed to terminate session');
                }
            } catch (error) {
                console.error('Error terminating session:', error);
                showError('Error terminating session');
            }
        }
        
        // Loading overlay functions
        function showLoadingOverlay() {
            document.getElementById('loadingOverlay').style.display = 'flex';
            resetProgressSteps();
        }
        
        function hideLoadingOverlay() {
            document.getElementById('loadingOverlay').style.display = 'none';
        }
        
        function updateLoadingMessage(message) {
            document.getElementById('loadingMessage').textContent = message;
        }
        
        function resetProgressSteps() {
            for (let i = 1; i <= 6; i++) {
                const step = document.getElementById(`step${i}`);
                step.className = 'step-status step-pending';
                step.textContent = i;
            }
        }
        
        function updateProgressStep(stepNumber, status) {
            const step = document.getElementById(`step${stepNumber}`);
            step.className = `step-status step-${status}`;
            
            if (status === 'complete') {
                step.innerHTML = '<i class="bi bi-check"></i>';
            } else if (status === 'error') {
                step.innerHTML = '<i class="bi bi-x"></i>';
            } else if (status === 'running') {
                step.innerHTML = '<i class="bi bi-arrow-clockwise"></i>';
            } else {
                step.textContent = stepNumber;
            }
        }
        
        function cancelLaunch() {
            if (confirm('Are you sure you want to cancel the launch?')) {
                launchInProgress = false;
                hideLoadingOverlay();
            }
        }
        
        // Utility functions
        function showError(message) {
            // Create and show error toast
            const toast = document.createElement('div');
            toast.className = 'toast align-items-center text-white bg-danger border-0 position-fixed';
            toast.style.cssText = 'top: 20px; right: 20px; z-index: 9999;';
            toast.innerHTML = `
                <div class="d-flex">
                    <div class="toast-body">
                        <i class="bi bi-exclamation-triangle me-2"></i>
                        ${message}
                    </div>
                    <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
                </div>
            `;
            
            document.body.appendChild(toast);
            const bsToast = new bootstrap.Toast(toast);
            bsToast.show();
            
            // Remove from DOM after hiding
            toast.addEventListener('hidden.bs.toast', () => {
                document.body.removeChild(toast);
            });
        }
        
        function showSuccess(message) {
            // Create and show success toast
            const toast = document.createElement('div');
            toast.className = 'toast align-items-center text-white bg-success border-0 position-fixed';
            toast.style.cssText = 'top: 20px; right: 20px; z-index: 9999;';
            toast.innerHTML = `
                <div class="d-flex">
                    <div class="toast-body">
                        <i class="bi bi-check-circle me-2"></i>
                        ${message}
                    </div>
                    <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
                </div>
            `;
            
            document.body.appendChild(toast);
            const bsToast = new bootstrap.Toast(toast);
            bsToast.show();
            
            // Remove from DOM after hiding
            toast.addEventListener('hidden.bs.toast', () => {
                document.body.removeChild(toast);
            });
        }
    </script>
</body>
</html>
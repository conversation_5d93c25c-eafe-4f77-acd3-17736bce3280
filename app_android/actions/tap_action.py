from .base_action import BaseAction
from airtest.core.api import Template, wait, touch, exists
from airtest.core.error import TargetNotFoundError
from airtest.core.helper import G
import os
import base64
import traceback

class TapAction(BaseAction):
    """Handler for tap actions"""

    def scale_ios_coordinates(self, coordinates):
        """
        Scale coordinates for iOS devices if needed

        Args:
            coordinates: Tuple of (x, y) coordinates

        Returns:
            tuple: Scaled coordinates
        """
        try:
            x, y = coordinates

            # Get device dimensions
            if hasattr(self.controller, 'device_dimensions') and self.controller.device_dimensions:
                device_width = self.controller.device_dimensions.get('width')
                device_height = self.controller.device_dimensions.get('height')

                if device_width and device_height:
                    # Check if we need to scale (if image dimensions don't match device dimensions)
                    if hasattr(self.controller, 'airtest_device') and self.controller.airtest_device:
                        try:
                            # Get the screen resolution from Airtest device
                            airtest_resolution = self.controller.airtest_device.get_current_resolution()
                            if airtest_resolution and len(airtest_resolution) == 2:
                                airtest_width, airtest_height = airtest_resolution

                                # Only scale if dimensions are different
                                if airtest_width != device_width or airtest_height != device_height:
                                    scale_x = device_width / airtest_width
                                    scale_y = device_height / airtest_height

                                    self.logger.info(f"Scaling coordinates by factors: x={scale_x}, y={scale_y}")
                                    return (int(x * scale_x), int(y * scale_y))
                        except Exception as e:
                            self.logger.warning(f"Error getting Airtest resolution: {e}")

            # Return original coordinates if no scaling needed or if scaling failed
            return coordinates
        except Exception as e:
            self.logger.error(f"Error scaling iOS coordinates: {e}")
            return coordinates

    def execute(self, params):
        """
        Execute tap action

        Args:
            params: Dictionary containing:
                - x: The x coordinate to tap
                - y: The y coordinate to tap
                - image_filename: (Optional) Reference image to tap on
                - method: (Optional) Method to use for tapping ('coordinates' or 'image')

        Returns:
            dict: Result with status and message
        """
        if not self.controller:
            return {"status": "error", "message": "No device controller available"}

        # Check if we're using image-based tapping
        method = params.get('method', 'coordinates')
        if method == 'image' or 'image_filename' in params:
            # Get image filename and parameters
            image_filename = params.get('image_filename')
            if not image_filename:
                return {"status": "error", "message": "Image filename is required for image-based tapping"}

            # Get threshold and timeout parameters
            threshold = float(params.get('threshold', 0.7))
            timeout = int(params.get('timeout', 20))

            try:
                # Resolve the image path properly
                if not os.path.exists(image_filename):
                    # Try to resolve from reference_images directory
                    try:
                        from config import DIRECTORIES
                        reference_dir = DIRECTORIES.get('REFERENCE_IMAGES', '')
                        if reference_dir:
                            full_path = os.path.join(reference_dir, os.path.basename(image_filename))
                            if os.path.exists(full_path):
                                image_filename = full_path
                                self.logger.info(f"Resolved image path to: {image_filename}")
                            else:
                                # Try directly in reference_images folder
                                ref_path = os.path.join('reference_images', os.path.basename(image_filename))
                                if os.path.exists(ref_path):
                                    image_filename = ref_path
                                    self.logger.info(f"Resolved image path to: {image_filename}")
                    except (ImportError, Exception) as e:
                        self.logger.warning(f"Could not resolve reference image directory: {e}")

                # If still doesn't exist, return error
                if not os.path.exists(image_filename):
                    return {"status": "error", "message": f"Image file not found: {image_filename}"}

                # Get absolute path for more reliable loading
                abs_path = os.path.abspath(image_filename)
                self.logger.info(f"Using absolute image path: {abs_path}")

                # --- Direct Airtest API approach (similar to working method) ---
                try:
                    from airtest.core.api import connect_device, wait, touch, Template, exists
                    from airtest.core.error import TargetNotFoundError
                    from airtest.core.helper import G

                    # Ensure Airtest device is initialized
                    if hasattr(self.controller, '_ensure_airtest_connected'):
                        self.logger.info("Ensuring Airtest device is connected...")
                        airtest_connected = self.controller._ensure_airtest_connected()
                        if not airtest_connected:
                            self.logger.error("Failed to connect Airtest device, trying fallback methods")
                            # Don't return error here, continue to fallback methods
                    else:
                        self.logger.warning("Controller doesn't have _ensure_airtest_connected method")

                    # Try to initialize Airtest device if not already initialized
                    if hasattr(self.controller, '_init_airtest'):
                        self.logger.info("Initializing Airtest device...")
                        airtest_initialized = self.controller._init_airtest()
                        if not airtest_initialized:
                            self.logger.error("Failed to initialize Airtest device, trying fallback methods")
                            # Don't return error here, continue to fallback methods

                    # Log the device info to help with debugging
                    if hasattr(G, 'DEVICE') and G.DEVICE:
                        self.logger.info(f"Current Airtest device: {G.DEVICE}")
                    else:
                        self.logger.warning("No Airtest device available in G.DEVICE")

                    # Use Airtest ONLY for finding the image coordinates (per method selection strategy)
                    self.log_method_selection('tap', 'image_recognition', 'airtest', 'Image recognition only')
                    self.logger.info(f"Using Airtest only for image recognition, not for tapping")

                    # Create template and find its position
                    template_image = Template(abs_path, threshold=threshold)
                    match_pos = wait(template_image, timeout=timeout)
                    self.logger.info(f"Found image at position: {match_pos}, now using Appium to tap")

                    # Skip all Airtest touch methods and go directly to UIAutomator/Appium (per method selection strategy)
                    self.log_method_selection('tap', 'coordinate_tap', 'uiautomator/appium', 'Avoiding AirTest touch to prevent unwanted keyboards')
                    if hasattr(self.controller, 'driver') and self.controller.driver:
                        # For iOS, apply scaling if needed
                        if hasattr(self.controller, 'platform_name') and self.controller.platform_name == 'iOS':
                            original_pos = match_pos
                            match_pos = self.scale_ios_coordinates(match_pos)
                            self.logger.info(f"Applied iOS scaling: {original_pos} -> {match_pos}")

                        x, y = match_pos
                        try:
                            # Use Appium's tap method
                            self.logger.info(f"Tapping at coordinates {match_pos} using Appium driver")
                            self.controller.driver.tap([(int(x), int(y))])
                            return {"status": "success", "message": f"Tapped at {match_pos} using Appium after finding image with Airtest"}
                        except Exception as tap_err:
                            self.logger.warning(f"Appium tap failed: {tap_err}, trying touch actions")

                            try:
                                # Try using Appium's TouchAction
                                from appium.webdriver.common.touch_action import TouchAction
                                actions = TouchAction(self.controller.driver)
                                actions.tap(x=int(x), y=int(y)).perform()
                                return {"status": "success", "message": f"Tapped at {match_pos} using Appium TouchAction after finding image with Airtest"}
                            except Exception as touch_action_err:
                                self.logger.warning(f"Appium TouchAction failed: {touch_action_err}")

                                # One last attempt with W3C actions if available
                                try:
                                    self.logger.info("Trying W3C Actions API")
                                    from selenium.webdriver.common.action_chains import ActionChains
                                    from selenium.webdriver.common.actions.pointer_input import PointerInput
                                    from selenium.webdriver.common.actions.action_builder import ActionBuilder

                                    # Create pointer input
                                    pointer = PointerInput(PointerInput.POINTER_TOUCH, "touch")
                                    # Create action chains
                                    actions = ActionBuilder(self.controller.driver, mouse=pointer)
                                    # Add pointer move and pointer down (tap)
                                    actions.pointer_action.move_to_location(int(x), int(y))
                                    actions.pointer_action.click()
                                    # Perform the action
                                    actions.perform()
                                    return {"status": "success", "message": f"Tapped at {match_pos} using W3C Actions after finding image with Airtest"}
                                except Exception as w3c_err:
                                    self.logger.error(f"All Appium tap methods failed: {w3c_err}")

                                    # Only now try the Airtest touch as a last resort
                                    self.logger.warning("All Appium methods failed, falling back to Airtest touch as last resort")
                                    touch(template_image)
                                    return {"status": "success", "message": f"Tapped on image using Airtest touch as fallback"}
                    else:
                        self.logger.warning("No Appium driver available, using Airtest touch")
                        touch(template_image)
                        return {"status": "success", "message": f"Tapped on image using Airtest touch (no Appium driver)"}

                except TargetNotFoundError:
                    self.logger.error(f"Image '{os.path.basename(abs_path)}' not found within {timeout} seconds with threshold {threshold}")
                    # Try exists() to see if we can find it with a lower threshold
                    try:
                        match_result = exists(template_image)
                        if match_result:
                            self.logger.info(f"Found with exists() but below threshold: {match_result}")
                            # If we found it with exists but not wait, try to tap on it anyway
                            try:
                                self.logger.info(f"Attempting to tap on image found with exists(): {match_result}")
                                touch(match_result)
                                return {"status": "success", "message": f"Tapped on image found with exists() at {match_result}"}
                            except Exception as touch_err:
                                self.logger.warning(f"Failed to tap on image found with exists(): {touch_err}")
                        else:
                            self.logger.info("Image not found with exists() either")
                    except Exception as e_exists:
                        self.logger.warning(f"Error checking with exists(): {e_exists}")

                    # Don't return error here, continue to fallback methods
                    self.logger.info("Image not found with Airtest, trying fallback methods")

                except Exception as e:
                    self.logger.error(f"Error using direct Airtest API: {e}")
                    if "No devices added" in str(e):
                        self.logger.warning("Airtest error: No devices added. This is expected if Airtest is not properly initialized.")
                    else:
                        traceback.print_exc()
                    # Continue with fallback methods

                # --- Fallback methods if direct approach fails ---

                # Try to use the controller's touch method if available
                if hasattr(self.controller, 'touch'):
                    try:
                        # Create a Template object again for the fallback
                        template_obj = Template(abs_path, threshold=threshold)
                        self.logger.info(f"Falling back to controller's touch method with Template")
                        result = self.controller.touch(template_obj)
                        if result:
                            return {"status": "success", "message": f"Tapped on image: {image_filename} using controller's touch method"}
                        else:
                            return {"status": "error", "message": f"Failed to tap on image: {image_filename} using controller's touch method"}
                    except Exception as template_err:
                        self.logger.error(f"Error with controller's touch method: {template_err}")

                # Try to use the controller's tap image method if available
                if hasattr(self.controller, 'tap_image'):
                    result = self.controller.tap_image(abs_path, threshold, timeout)
                    return result

                # Try direct Appium image recognition if available
                if hasattr(self.controller, 'driver') and self.controller.driver:
                    try:
                        self.logger.info(f"Trying direct Appium image recognition for {abs_path}")

                        # Check if the driver supports image recognition
                        if hasattr(self.controller.driver, 'find_image_occurrence'):
                            self.logger.info("Using Appium's find_image_occurrence method")

                            # Read the image file as base64
                            with open(abs_path, 'rb') as img_file:
                                img_base64 = base64.b64encode(img_file.read()).decode('utf-8')

                            # Take a screenshot to use as the base image
                            screenshot = self.controller.driver.get_screenshot_as_base64()

                            # Find the image on screen
                            match = self.controller.driver.find_image_occurrence(
                                screenshot,
                                img_base64,
                                threshold=threshold,
                                visualize=False
                            )

                            if match:
                                # Extract center coordinates
                                x = match['rect']['x'] + match['rect']['width'] // 2
                                y = match['rect']['y'] + match['rect']['height'] // 2

                                self.logger.info(f"Found image at ({x}, {y}) using Appium image recognition")

                                # Tap at the center
                                self.controller.driver.tap([(int(x), int(y))])
                                return {"status": "success", "message": f"Tapped at ({x}, {y}) using Appium image recognition"}
                            else:
                                self.logger.warning("Image not found using Appium image recognition")
                        else:
                            self.logger.warning("Appium driver does not support image recognition")
                    except Exception as e:
                        self.logger.error(f"Error using Appium image recognition: {e}")

                # Try using OpenCV directly for image recognition
                try:
                    self.logger.info(f"Trying OpenCV directly for image recognition: {abs_path}")
                    import cv2
                    import numpy as np
                    from PIL import Image
                    import io

                    # Take a screenshot using Appium
                    if hasattr(self.controller, 'driver') and self.controller.driver:
                        # Get device dimensions first
                        device_width = None
                        device_height = None
                        if hasattr(self.controller, 'device_dimensions') and self.controller.device_dimensions:
                            device_width = self.controller.device_dimensions.get('width')
                            device_height = self.controller.device_dimensions.get('height')
                            self.logger.info(f"Device dimensions: {device_width}x{device_height}")

                        # Get screenshot as base64
                        screenshot_base64 = self.controller.driver.get_screenshot_as_base64()
                        screenshot_data = base64.b64decode(screenshot_base64)

                        # Convert to PIL Image first
                        screenshot_pil = Image.open(io.BytesIO(screenshot_data))
                        original_size = screenshot_pil.size
                        self.logger.info(f"Original screenshot size: {original_size[0]}x{original_size[1]}")

                        # DO NOT resize the screenshot - use original dimensions
                        self.logger.info(f"Using original screenshot dimensions: {original_size[0]}x{original_size[1]}")
                        # Update device dimensions to match the actual screenshot
                        device_width = original_size[0]
                        device_height = original_size[1]

                        # Convert to OpenCV format
                        screenshot_cv = cv2.cvtColor(np.array(screenshot_pil), cv2.COLOR_RGB2BGR)

                        # Save the processed screenshot for debugging
                        debug_screenshot_path = os.path.join(os.path.dirname(abs_path), 'debug_screenshot.png')
                        cv2.imwrite(debug_screenshot_path, screenshot_cv)
                        self.logger.info(f"Saved debug screenshot to {debug_screenshot_path}")

                        # Load the template image
                        template = cv2.imread(abs_path)
                        if template is None:
                            self.logger.error(f"Failed to load template image: {abs_path}")
                        else:
                            # Get template dimensions
                            h, w = template.shape[:2]
                            self.logger.info(f"Template dimensions: {w}x{h}")

                            # Try multiple template matching methods
                            methods = [
                                (cv2.TM_CCOEFF_NORMED, "TM_CCOEFF_NORMED"),
                                (cv2.TM_CCORR_NORMED, "TM_CCORR_NORMED"),
                                (cv2.TM_SQDIFF_NORMED, "TM_SQDIFF_NORMED")
                            ]

                            best_val = 0
                            best_loc = None
                            best_method = None

                            # Use a lower threshold for OpenCV matching
                            opencv_threshold = max(0.5, threshold - 0.2)  # Lower threshold by 0.2 but not below 0.5
                            self.logger.info(f"Using OpenCV threshold: {opencv_threshold} (original: {threshold})")

                            for method, method_name in methods:
                                # Perform template matching
                                result = cv2.matchTemplate(screenshot_cv, template, method)

                                # Different handling for SQDIFF (lower is better) vs others (higher is better)
                                if method == cv2.TM_SQDIFF_NORMED:
                                    min_val, max_val, min_loc, max_loc = cv2.minMaxLoc(result)
                                    curr_val = 1.0 - min_val  # Convert to same scale as other methods
                                    curr_loc = min_loc
                                else:
                                    min_val, max_val, min_loc, max_loc = cv2.minMaxLoc(result)
                                    curr_val = max_val
                                    curr_loc = max_loc

                                self.logger.info(f"Template matching with {method_name}: {curr_val}")

                                if curr_val > best_val:
                                    best_val = curr_val
                                    best_loc = curr_loc
                                    best_method = method_name

                            self.logger.info(f"Best template matching result: {best_val} with method {best_method} (threshold: {opencv_threshold})")

                            if best_val >= opencv_threshold:
                                # Match found, calculate center coordinates
                                x = best_loc[0] + w // 2
                                y = best_loc[1] + h // 2

                                self.logger.info(f"Found image at ({x}, {y}) in original screenshot")

                                # Validate coordinates against device dimensions
                                if hasattr(self.controller, 'device_dimensions') and self.controller.device_dimensions:
                                    device_width = self.controller.device_dimensions.get('width')
                                    device_height = self.controller.device_dimensions.get('height')

                                    # Check if coordinates are within device bounds
                                    if device_width and device_height:
                                        if x >= original_size[0] or y >= original_size[1]:
                                            self.logger.warning(f"Coordinates ({x}, {y}) are outside screenshot bounds {original_size[0]}x{original_size[1]}")

                                            # Clamp coordinates to screenshot bounds
                                            x = min(x, original_size[0] - 1)
                                            y = min(y, original_size[1] - 1)
                                            self.logger.info(f"Clamped coordinates to ({x}, {y})")

                                        # Only scale if dimensions are different and scaling is needed
                                        if device_width != original_size[0] or device_height != original_size[1]:
                                            # Calculate scaling factors
                                            scale_x = device_width / original_size[0]
                                            scale_y = device_height / original_size[1]

                                            # Apply scaling
                                            original_x, original_y = x, y
                                            x = int(x * scale_x)
                                            y = int(y * scale_y)

                                            # Ensure coordinates are within device bounds after scaling
                                            x = min(x, device_width - 1)
                                            y = min(y, device_height - 1)

                                            self.logger.info(f"Scaled coordinates from ({original_x}, {original_y}) to ({x}, {y}) for device dimensions {device_width}x{device_height}")

                                # Create a debug image showing the match
                                debug_match_path = os.path.join(os.path.dirname(abs_path), 'debug_match.png')
                                debug_img = screenshot_cv.copy()
                                cv2.rectangle(debug_img, max_loc, (max_loc[0] + w, max_loc[1] + h), (0, 255, 0), 2)
                                cv2.circle(debug_img, (x, y), 5, (0, 0, 255), -1)
                                cv2.imwrite(debug_match_path, debug_img)
                                self.logger.info(f"Saved debug match image to {debug_match_path}")

                                self.logger.info(f"Tapping at ({x}, {y}) using OpenCV image recognition")

                                # Tap at the center using Appium
                                self.controller.driver.tap([(int(x), int(y))])
                                return {"status": "success", "message": f"Tapped at ({x}, {y}) using OpenCV image recognition"}
                            else:
                                self.logger.warning(f"Image not found with OpenCV (max_val: {max_val}, threshold: {threshold})")
                except ImportError as e:
                    self.logger.warning(f"OpenCV not available: {e}")
                except Exception as e:
                    self.logger.error(f"Error using OpenCV for image recognition: {e}")

                # Use the action factory's clickImage handler as a last resort
                from action_factory import ActionFactory
                action_factory = ActionFactory(self.controller)

                image_params = {
                    'image_path': abs_path,  # Use absolute path for more reliable loading
                    'threshold': threshold,
                    'timeout': timeout
                }

                # Execute using clickImage action which has better image matching
                self.logger.info(f"Using clickImage action for image-based tapping: {abs_path}")
                return action_factory.execute_action('clickImage', image_params)

            except Exception as e:
                self.logger.error(f"Error executing tap on image: {e}")
                traceback.print_exc()
                return {"status": "error", "message": f"Tap on image failed: {str(e)}"}

        # Check if we're using locator-based tapping
        if method == 'locator':
            locator_type = params.get('locator_type')
            locator_value = params.get('locator_value')
            timeout = int(params.get('timeout', 60))  # Default timeout increased to 60 seconds
            interval = float(params.get('interval', 0.5))
            fallback_locators = params.get('fallback_locators', [])

            # Check for new fallback mechanism
            fallback_type = params.get('fallback_type')

            # If we have no primary locator and no fallbacks, return error
            if not locator_type or not locator_value:
                return {"status": "error", "message": "Missing locator type or value"}

            # Try primary locator first
            try:
                self.logger.info(f"Attempting to tap using primary locator: {locator_type}={locator_value}")
                result = self.controller.tap_element(locator_type, locator_value, timeout, interval)

                # If successful, return the result
                if isinstance(result, dict) and result.get('status') == 'success':
                    return result

                # If we have a result dict with error, log it
                if isinstance(result, dict) and result.get('status') == 'error':
                    self.logger.warning(f"Primary locator failed: {result.get('message')}")

                # If we have no fallbacks, return the result
                if not fallback_type and not fallback_locators:
                    return result

                # Otherwise, continue to fallbacks
                self.logger.info("Primary locator failed, trying fallbacks")

            except Exception as e:
                self.logger.warning(f"Error with primary locator: {e}")
                # Continue to fallbacks if we have them
                if not fallback_type and not fallback_locators:
                    return {"status": "error", "message": f"Tap with locator failed: {str(e)}"}

            # Try new fallback mechanism first if available
            if fallback_type:
                self.logger.info(f"Trying fallback with type: {fallback_type}")

                try:
                    if fallback_type == 'coordinates':
                        # Get fallback coordinates
                        fallback_x = params.get('fallback_x')
                        fallback_y = params.get('fallback_y')

                        if fallback_x is None or fallback_y is None:
                            self.logger.warning("Missing fallback coordinates")
                        else:
                            self.logger.info(f"Using fallback coordinates: ({fallback_x}, {fallback_y})")
                            result = self.controller.tap(fallback_x, fallback_y)

                            if isinstance(result, dict) and result.get('status') == 'success':
                                result['message'] = f"Tapped using fallback coordinates: ({fallback_x}, {fallback_y})"
                                return result
                            elif result is True:
                                return {"status": "success", "message": f"Tapped using fallback coordinates: ({fallback_x}, {fallback_y})"}

                    elif fallback_type == 'image':
                        # Get fallback image
                        fallback_image = params.get('fallback_image_filename')
                        fallback_threshold = float(params.get('fallback_threshold', 0.7))

                        if not fallback_image:
                            self.logger.warning("Missing fallback image filename")
                        else:
                            self.logger.info(f"Using fallback image: {fallback_image}")

                            # Create image params
                            image_params = {
                                'image_filename': fallback_image,
                                'threshold': fallback_threshold,
                                'timeout': timeout,
                                'method': 'image'
                            }

                            # Use the same execute method with image params
                            result = self.execute(image_params)

                            if isinstance(result, dict) and result.get('status') == 'success':
                                result['message'] = f"Tapped using fallback image: {fallback_image}"
                                return result

                    elif fallback_type == 'text':
                        # Get fallback text
                        fallback_text = params.get('fallback_text')

                        if not fallback_text:
                            self.logger.warning("Missing fallback text")
                        else:
                            self.logger.info(f"Using fallback text: {fallback_text}")

                            # Use the TapOnTextAction to handle text detection with OCR
                            try:
                                from .tap_on_text_action import TapOnTextAction
                            except ImportError:
                                from tap_on_text_action import TapOnTextAction
                            tap_on_text_action = TapOnTextAction(self.controller)

                            # Create parameters for the tap on text action
                            text_params = {
                                'text_to_find': fallback_text,
                                'timeout': timeout
                            }

                            # Execute the tap on text action
                            self.logger.info(f"Using TapOnTextAction for fallback text: {fallback_text}")
                            result = tap_on_text_action.execute(text_params)

                            if isinstance(result, dict) and result.get('status') == 'success':
                                result['message'] = f"Tapped using fallback text: {fallback_text}"
                                return result

                    elif fallback_type == 'locator':
                        # Get fallback locator
                        fallback_locator_type = params.get('fallback_locator_type')
                        fallback_locator_value = params.get('fallback_locator_value')

                        if not fallback_locator_type or not fallback_locator_value:
                            self.logger.warning("Missing fallback locator type or value")
                        else:
                            self.logger.info(f"Using fallback locator: {fallback_locator_type}={fallback_locator_value}")
                            result = self.controller.tap_element(fallback_locator_type, fallback_locator_value, timeout, interval)

                            if isinstance(result, dict) and result.get('status') == 'success':
                                result['message'] = f"Tapped using fallback locator: {fallback_locator_type}={fallback_locator_value}"
                                return result

                except Exception as e:
                    self.logger.warning(f"Error with fallback: {e}")
                    self.logger.warning(traceback.format_exc())

            # Try legacy fallback locators if available and new fallback failed
            if fallback_locators:
                self.logger.info(f"Trying {len(fallback_locators)} legacy fallback locators")

                # Calculate timeout per locator
                locator_count = len(fallback_locators) + 1  # +1 for primary locator
                timeout_per_locator = max(5, timeout // locator_count)  # Minimum 5 seconds per locator

                for i, fallback in enumerate(fallback_locators):
                    fallback_type = fallback.get('locator_type')
                    fallback_value = fallback.get('locator_value')

                    if not fallback_type or not fallback_value:
                        self.logger.warning(f"Skipping invalid fallback locator at index {i}")
                        continue

                    try:
                        self.logger.info(f"Trying fallback locator {i+1}/{len(fallback_locators)}: {fallback_type}={fallback_value}")

                        # Handle different locator types
                        if fallback_type == 'image':
                            # Use image-based tap
                            self.logger.info(f"Using image-based tap for fallback locator {i+1}")
                            result = self.controller.tap_on_image(fallback_value, timeout=timeout_per_locator)

                            # If successful, return the result
                            if isinstance(result, dict) and result.get('status') == 'success':
                                result['message'] = f"Tapped using image fallback locator {i+1}: {fallback_value}"
                                return result

                            # Log the failure and continue to next fallback
                            if isinstance(result, dict):
                                self.logger.warning(f"Image fallback locator {i+1} failed: {result.get('message')}")

                        elif fallback_type == 'text':
                            # Use text-based tap
                            self.logger.info(f"Using text-based tap for fallback locator {i+1}")
                            result = self.controller.tap_on_text(fallback_value, timeout=timeout_per_locator)

                            # If successful, return the result
                            if isinstance(result, dict) and result.get('status') == 'success':
                                result['message'] = f"Tapped using text fallback locator {i+1}: {fallback_value}"
                                return result

                            # Log the failure and continue to next fallback
                            if isinstance(result, dict):
                                self.logger.warning(f"Text fallback locator {i+1} failed: {result.get('message')}")

                        else:
                            # Use element-based tap
                            result = self.controller.tap_element(fallback_type, fallback_value, timeout_per_locator, interval)

                            # If successful, return the result
                            if isinstance(result, dict) and result.get('status') == 'success':
                                result['message'] = f"Tapped using fallback locator {i+1}: {fallback_type}={fallback_value}"
                                return result

                            # Log the failure and continue to next fallback
                            if isinstance(result, dict):
                                self.logger.warning(f"Fallback locator {i+1} failed: {result.get('message')}")

                    except Exception as e:
                        self.logger.warning(f"Error with fallback locator {i+1}: {e}")
                        self.logger.warning(traceback.format_exc())

                # If we get here, all fallbacks failed
                return {"status": "error", "message": f"All locators failed (primary + {len(fallback_locators)} fallbacks)"}

            # If we get here with no fallbacks, the primary locator failed
            return {"status": "error", "message": "Primary locator failed and no fallbacks provided"}

        # Regular coordinate-based tap
        x = params.get('x')
        y = params.get('y')

        if x is None or y is None:
            return {"status": "error", "message": "Missing x or y coordinates"}

        # Use method selection strategy for coordinate-based taps
        preferred_method = self.get_preferred_automation_method('tap', 'coordinate_tap')
        self.log_method_selection('tap', 'coordinate_tap', preferred_method, 'Coordinate-based tap - avoiding AirTest')

        try:
            # Execute the tap using the controller
            result = self.controller.tap(x, y)

            # Check if the result is a dict or a boolean
            if isinstance(result, dict):
                # Controller returned a dict with status and message
                return result
            elif result is True:
                # Controller returned a boolean success
                return {"status": "success", "message": f"Tapped at ({x}, {y})"}
            else:
                # Controller returned a boolean failure
                return {"status": "error", "message": f"Failed to tap at ({x}, {y})"}

        except Exception as e:
            self.logger.error(f"Error executing tap action: {e}")
            return {"status": "error", "message": f"Tap action failed: {str(e)}"}
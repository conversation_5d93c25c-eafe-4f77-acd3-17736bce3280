import os
import time
import base64
import logging
import json
import subprocess
import tempfile
import threading
import re  # Ensure re is imported at the top level
from io import BytesIO
from PIL import Image
import numpy as np
import cv2
from datetime import datetime
import random
import traceback
import pytesseract
import string  # Added for potential iOS bundle ID generation if needed
import PIL.Image
import PIL.ImageDraw
import io
import glob

from appium import webdriver
from appium.webdriver.common.appiumby import AppiumBy
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import WebDriverException, NoSuchElementException, TimeoutException
from selenium.webdriver.common.action_chains import Action<PERSON>hai<PERSON>
from selenium.webdriver.common.keys import Keys

try:
    from airtest.core.ios.ios import IOS
except ImportError:
    # Fallback if airtest is not available
    IOS = None

import sys
import time
import logging
import uuid
import json
import threading

# Setup logger
logging.basicConfig(level=logging.INFO)

try:
    # For Appium Python Client 2.x
    from appium.options.common.base import AppiumOptions
except ImportError:
    # For Appium Python Client 4.x
    from appium.options.android import UiAutomator2Options as AppiumOptions
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import WebDriverException, TimeoutException
from selenium.webdriver import ActionChains
from selenium.webdriver.common.actions.mouse_button import MouseButton
import sys

# Handle TouchAction import for both old and new Appium Python Client versions
try:
    # For Appium Python Client 1.x
    from appium.webdriver.common.touch_action import TouchAction
    logging.getLogger("appium_device_controller").info("Using TouchAction from Appium Python Client 1.x")
except ImportError:
    try:
        # For alternative import location in some versions
        from appium.webdriver.touchchaction import TouchAction
        logging.getLogger("appium_device_controller").info("Using TouchAction from alternative location")
    except ImportError:
        # For Appium Python Client 2.x/5.x, create fallback implementation
        logging.getLogger("appium_device_controller").warning("TouchAction not available in this Appium Python Client version - using W3C Actions fallback")
        class TouchAction:
            def __init__(self, driver):
                self.driver = driver
                self.actions = []
                logging.getLogger("appium_device_controller").info("Using emulated TouchAction implementation")

            def tap(self, x=None, y=None, element=None, count=1):
                self.actions.append(('tap', {'x': x, 'y': y, 'element': element, 'count': count}))
                return self

            def press(self, x=None, y=None, element=None, pressure=None):
                self.actions.append(('press', {'x': x, 'y': y, 'element': element, 'pressure': pressure}))
                return self

            def long_press(self, x=None, y=None, element=None, duration=None):
                self.actions.append(('long_press', {'x': x, 'y': y, 'element': element, 'duration': duration}))
                return self

            def wait(self, ms=0):
                self.actions.append(('wait', {'ms': ms}))
                return self

            def move_to(self, x=None, y=None, element=None):
                self.actions.append(('move_to', {'x': x, 'y': y, 'element': element}))
                return self

            def release(self):
                self.actions.append(('release', {}))
                return self

            def perform(self):
                # Use Selenium's W3C Actions API
                from selenium.webdriver.common.actions.pointer_input import PointerInput
                from selenium.webdriver.common.actions.action_builder import ActionBuilder

                pointer = PointerInput(PointerInput.POINTER_TOUCH, "touch")
                actions = ActionBuilder(self.driver, mouse=pointer)

                for action_name, params in self.actions:
                    if action_name == 'tap':
                        x, y = params.get('x'), params.get('y')
                        if x is not None and y is not None:
                            actions.pointer_action.move_to_location(x, y)
                            actions.pointer_action.click()
                    elif action_name == 'press' or action_name == 'long_press':
                        x, y = params.get('x'), params.get('y')
                        if x is not None and y is not None:
                            actions.pointer_action.move_to_location(x, y)
                            actions.pointer_action.click_and_hold()
                    elif action_name == 'wait':
                        ms = params.get('ms', 0)
                        if ms > 0:
                            # Convert ms to seconds for W3C pause
                            actions.pointer_action.pause(ms / 1000.0)
                    elif action_name == 'move_to':
                        x, y = params.get('x'), params.get('y')
                        if x is not None and y is not None:
                            actions.pointer_action.move_to_location(x, y)
                    elif action_name == 'release':
                        actions.pointer_action.release()

# Check for AirTest availability once at module level
AIRTEST_AVAILABLE = False
try:
    import airtest
    from airtest.core.api import (
        connect_device,
        set_current,
        Template,
        touch,
        wait,
        exists,
        device as current_device,
        shell,
        start_app,
        stop_app,
        swipe,
        text,
        keyevent,
        sleep,
        snapshot,
        init_device,
        auto_setup,
        double_click,
    )
    from airtest.core.android.android import Android as AirtestAndroid
    from airtest.core.settings import Settings as AirtestSettings
    from airtest.core.error import TargetNotFoundError  # Correct exception name

    AIRTEST_AVAILABLE = True
    # Add a log message upon successful import
    import logging

    logging.getLogger("AppiumDeviceController").info(
        "Successfully imported Airtest library."
    )
except ImportError as e:
    import logging

    # Log the full traceback to understand why the import failed
    logger = logging.getLogger("AppiumDeviceController")
    logger.error(
        f"Failed to import Airtest library. Image recognition features will be disabled. Error: {e}"
    )
    import traceback

    logger.error(traceback.format_exc())  # Log the full traceback
    AIRTEST_AVAILABLE = False  # Ensure it's False on any import error

# Get the parent directory to import from app package
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))


class AppiumDeviceController:
    """Controller for all device operations using Appium"""

    # Default values for iOS code signing
    DEFAULT_XCODE_ORG_ID = ""  # Leave empty for default XCode behavior
    DEFAULT_XCODE_SIGNING_ID = "iPhone Developer"

    def __init__(self, appium_port=None, wda_port=None):
        """Initialize the Appium device controller

        Args:
            appium_port (int, optional): Port for Appium server. Defaults to config value or 4723.
            wda_port (int, optional): Port for WebDriverAgent. Defaults to config value or 8100.
        """
        self.logger = logging.getLogger("appium_device_controller")
        self.driver = None
        self.device_id = None
        self.platform_name = None  # To store 'iOS' or 'Android'

        # Set ports from parameters or config
        try:
            import sys
            import os
            # Add parent directory to path to import config
            parent_dir = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
            if parent_dir not in sys.path:
                sys.path.insert(0, parent_dir)
            import config
            self.appium_port = appium_port or getattr(config, 'APPIUM_PORT', 4723)
            self.wda_port = wda_port or getattr(config, 'WDA_PORT', 8100)
        except ImportError:
            self.appium_port = appium_port or 4723
            self.wda_port = wda_port or 8100

        self.logger.info(f"Initialized AppiumDeviceController with Appium port: {self.appium_port}, WDA port: {self.wda_port}")

        self.element_cache = {}
        self.device_dimensions = None  # Store device screen dimensions
        # Use temp directory for screenshots
        from utils.file_utils import get_temp_subdirectory
        self.screenshot_dir = get_temp_subdirectory('screenshots')

        # AirTest integration
        self.airtest_device = None

        # Image matcher for screenshot and image recognition
        self.image_matcher = None

        # Connection stability improvements
        self.last_activity_time = time.time()
        self.connection_timeout = 300  # 5 minutes timeout
        self.heartbeat_interval = 30  # 30 seconds heartbeat
        self.heartbeat_thread = None
        self.heartbeat_running = False
        self.connection_lock = threading.Lock()
        self.max_reconnect_attempts = 3
        self.reconnect_delay = 5  # seconds

        # Initialize appium server management
        self._ensure_appium_server()
        
        # Start connection monitoring
        self._start_connection_monitoring()

    def _start_connection_monitoring(self):
        """Start the connection monitoring thread"""
        if not self.heartbeat_running:
            self.heartbeat_running = True
            self.heartbeat_thread = threading.Thread(target=self._connection_monitor, daemon=True)
            self.heartbeat_thread.start()
            self.logger.info("Connection monitoring started")

    def _connection_monitor(self):
        """Monitor connection health and perform automatic recovery"""
        while self.heartbeat_running:
            try:
                time.sleep(self.heartbeat_interval)
                
                if self.driver and self.device_id:
                    current_time = time.time()
                    
                    # Check if connection has been idle too long
                    if current_time - self.last_activity_time > self.connection_timeout:
                        self.logger.warning("Connection has been idle for too long, checking health...")
                        
                        # Perform health check
                        if not self._perform_health_check():
                            self.logger.warning("Health check failed, attempting recovery...")
                            self._attempt_connection_recovery()
                    
                    # Perform periodic health check
                    elif current_time - self.last_activity_time > self.heartbeat_interval * 2:
                        if not self._perform_health_check():
                            self.logger.warning("Periodic health check failed, attempting recovery...")
                            self._attempt_connection_recovery()
                            
            except Exception as e:
                self.logger.error(f"Error in connection monitor: {e}")
                time.sleep(5)  # Wait before retrying

    def _perform_health_check(self):
        """Perform a quick health check on the connection"""
        try:
            with self.connection_lock:
                if not self.driver:
                    return False
                
                # Try a simple operation with timeout
                import socket
                original_timeout = socket.getdefaulttimeout()
                socket.setdefaulttimeout(10)
                
                try:
                    # For Android, check current activity
                    if self.platform_name and self.platform_name.lower() == 'android':
                        activity = self.driver.current_activity
                        if activity:
                            self.last_activity_time = time.time()
                            return True
                    # For iOS, check current bundle
                    elif self.platform_name and self.platform_name.lower() == 'ios':
                        bundle = self.driver.current_package
                        if bundle:
                            self.last_activity_time = time.time()
                            return True
                    
                    return False
                    
                finally:
                    socket.setdefaulttimeout(original_timeout)
                    
        except Exception as e:
            self.logger.debug(f"Health check failed: {e}")
            return False

    def _attempt_connection_recovery(self):
        """Attempt to recover a failed connection"""
        try:
            with self.connection_lock:
                self.logger.info("Attempting connection recovery...")
                
                for attempt in range(self.max_reconnect_attempts):
                    try:
                        self.logger.info(f"Recovery attempt {attempt + 1}/{self.max_reconnect_attempts}")
                        
                        # Try to reconnect
                        if self.reconnect_device():
                            self.logger.info("Connection recovery successful")
                            self.last_activity_time = time.time()
                            return True
                        
                        time.sleep(self.reconnect_delay)
                        
                    except Exception as e:
                        self.logger.error(f"Recovery attempt {attempt + 1} failed: {e}")
                        time.sleep(self.reconnect_delay)
                
                self.logger.error("All recovery attempts failed")
                return False
                
        except Exception as e:
            self.logger.error(f"Error during connection recovery: {e}")
            return False

    def _update_activity_time(self):
        """Update the last activity time"""
        self.last_activity_time = time.time()

    def test_airtest_connection(self, device_id, platform_name="iOS"):
        """Test direct Airtest connection to a device and report diagnostics

        Args:
            device_id (str): The device ID to connect to
            platform_name (str): Platform name ('iOS' or 'Android')

        Returns:
            dict: Connection status and diagnostic information
        """
        if not AIRTEST_AVAILABLE:
            return {
                "status": "error",
                "message": "Airtest library not available",
                "available": False
            }

        result = {
            "status": "unknown",
            "device_id": device_id,
            "platform": platform_name,
            "available": True,
            "details": {}
        }

        self.logger.info(f"Testing direct Airtest connection to {platform_name} device {device_id}")

        # Store current state to restore later
        original_device_id = self.device_id
        original_platform = self.platform_name

        try:
            # Set device info for Airtest initialization
            self.device_id = device_id
            self.platform_name = platform_name

            # Clear any existing Airtest connection
            self.airtest_device = None

            # Try to import Airtest modules to check availability
            try:
                from airtest.core.api import connect_device, init_device
                from airtest.core.helper import G

                # Record initial G state
                result["details"]["initial_G_state"] = {
                    "device_exists": G.DEVICE is not None,
                    "device_list_count": len(G.DEVICE_LIST) if hasattr(G, "DEVICE_LIST") else 0,
                }
            except Exception as import_err:
                result["status"] = "error"
                result["message"] = f"Failed to import Airtest modules: {str(import_err)}"
                return result

            # Try our actual production method
            try:
                self.logger.info("Testing connection using _init_airtest method...")
                start_time = time.time()
                success = self._init_airtest(device_id)
                elapsed = time.time() - start_time

                result["details"]["_init_airtest_result"] = {
                    "success": success,
                    "time_taken": f"{elapsed:.2f}s",
                    "airtest_device_type": str(type(self.airtest_device)) if self.airtest_device else None
                }

                # If we have a device, try a basic operation
                if self.airtest_device:
                    try:
                        # Try to get screen resolution as verification
                        screen_resolution = self.airtest_device.get_current_resolution()
                        result["details"]["verification"] = {
                            "success": True,
                            "screen_resolution": screen_resolution
                        }
                    except Exception as verify_err:
                        result["details"]["verification"] = {
                            "success": False,
                            "error": str(verify_err)
                        }

                # Set overall status based on _init_airtest result
                if success:
                    result["status"] = "success"
                    result["message"] = f"Successfully connected to {platform_name} device {device_id}"
                else:
                    result["status"] = "error"
                    result["message"] = f"Failed to connect to {platform_name} device {device_id}"
            except Exception as e:
                self.logger.error(f"Error during _init_airtest test: {e}")
                result["details"]["_init_airtest_error"] = str(e)
                result["status"] = "error"
                result["message"] = f"Error testing _init_airtest: {str(e)}"

        except Exception as overall_err:
            result["status"] = "error"
            result["message"] = f"Overall test error: {str(overall_err)}"
            result["details"]["exception"] = traceback.format_exc()
        finally:
            # Clean up the test connection
            if self.airtest_device:
                try:
                    self.airtest_device.disconnect()
                except Exception as disconnect_err:
                    self.logger.warning(f"Error disconnecting Airtest device: {disconnect_err}")
                self.airtest_device = None

            # Restore original state
            self.device_id = original_device_id
            self.platform_name = original_platform

        return result

    def _kill_existing_processes(self, force_kill=False):
        """
        Kill any existing Appium and iproxy processes

        Args:
            force_kill (bool): If True, kill processes regardless of port configuration.
                              If False, only kill if using default ports.
        """
        try:
            # Check if we're using default ports
            using_default_ports = (self.appium_port == 4723 and self.wda_port == 8100)

            if not force_kill and not using_default_ports:
                self.logger.info(f"Using custom ports (Appium: {self.appium_port}, WDA: {self.wda_port}) - preserving existing processes for multi-instance support")
                return True

            self.logger.info("Checking for existing Appium and iproxy processes...")

            # Kill existing Appium processes
            if sys.platform == 'win32':
                # Windows
                try:
                    subprocess.run(['taskkill', '/F', '/IM', 'node.exe'],
                                  stdout=subprocess.PIPE, stderr=subprocess.PIPE, check=False)
                    self.logger.info("Attempted to kill Node.js processes on Windows")
                except Exception as e:
                    self.logger.warning(f"Error killing Node.js processes on Windows: {e}")
            else:
                # macOS/Linux
                try:
                    # Kill Appium processes
                    subprocess.run(['pkill', '-f', 'appium'],
                                  stdout=subprocess.PIPE, stderr=subprocess.PIPE, check=False)
                    self.logger.info("Attempted to kill Appium processes")

                    # Only kill iproxy processes when using default ports to avoid breaking multi-device support
                    if using_default_ports:
                        subprocess.run(['pkill', '-f', 'iproxy'],
                                      stdout=subprocess.PIPE, stderr=subprocess.PIPE, check=False)
                        self.logger.info("Attempted to kill iproxy processes (default ports only)")
                    else:
                        self.logger.info("Preserving iproxy processes for multi-instance support")
                except Exception as e:
                    self.logger.warning(f"Error killing processes on Unix: {e}")

            # Wait a moment for processes to terminate
            time.sleep(2)
            return True
        except Exception as e:
            self.logger.error(f"Error in _kill_existing_processes: {e}")
            return False

    def check_appium_server(self):
        """Check if the Appium server is running and responsive"""
        try:
            import requests
            response = requests.get(f"http://127.0.0.1:{self.appium_port}/wd/hub/status", timeout=2)
            if response.status_code == 200:
                data = response.json()
                if data.get('value', {}).get('ready', False):
                    self.logger.info("Appium server is running and ready")
                    return True
                else:
                    self.logger.warning("Appium server is running but not ready")
                    return False
            else:
                self.logger.warning(f"Appium server returned unexpected status code: {response.status_code}")
                return False
        except Exception as e:
            self.logger.warning(f"Appium server check failed: {e}")
            return False

    def restart_appium_server(self):
        """Restart the Appium server"""
        self.logger.info("Restarting Appium server...")
        self._kill_existing_processes(force_kill=True)  # Force kill when explicitly restarting
        return self._ensure_appium_server()

    def restart_device_session(self):
        """Restart the device session without restarting the Appium server"""
        try:
            self.logger.info(f"Restarting session for device: {self.device_id}")
            
            if not self.device_id:
                self.logger.error("No device connected to restart session")
                return False
            
            # Store current device information
            device_id = self.device_id
            platform_name = self.platform_name
            options = getattr(self, 'options', {})
            
            # Disconnect current session
            self.disconnect()
            
            # Wait a moment for cleanup
            time.sleep(2)
            
            # Reconnect to the same device
            success = self.connect_to_device(device_id, options, platform_name)
            
            if success:
                self.logger.info(f"Successfully restarted session for device: {device_id}")
                return True
            else:
                self.logger.error(f"Failed to restart session for device: {device_id}")
                return False
                
        except Exception as e:
            self.logger.error(f"Error restarting device session: {e}")
            return False

    def _ensure_appium_server(self):
        """Ensure Appium server is running by killing existing instances and starting a new one"""
        try:
            import requests

            # First check if Appium is already running and responsive
            if self.check_appium_server():
                self.logger.info("Appium server is already running and responsive")
                return True

            # If not, kill any existing Appium and iproxy processes (only if using default ports)
            self._kill_existing_processes()

            # Check if server is still running after killing processes
            try:
                response = requests.get(
                    f"http://127.0.0.1:{self.appium_port}/wd/hub/status", timeout=2
                )
                if response.status_code == 200:
                    self.logger.info("Appium server is still running after kill attempt. Will use existing server.")
                    return True
            except:
                self.logger.info("No Appium server detected. Starting a new one...")

            # Get the project root directory
            project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

            # Path to local node_modules/.bin directory
            node_bin_path = os.path.join(project_root, 'node_modules', '.bin')

            # Check if local Appium exists
            local_appium_path = os.path.join(node_bin_path, 'appium')
            if os.path.exists(local_appium_path):
                appium_cmd = local_appium_path
                self.logger.info(f"Using local Appium installation at: {local_appium_path}")

                # Ensure drivers are installed locally
                try:
                    # Check if drivers are installed
                    driver_check = subprocess.run(
                        [local_appium_path, "driver", "list", "--installed"],
                        capture_output=True,
                        text=True,
                        check=False
                    )
                    self.logger.info(f"Installed Appium drivers: {driver_check.stdout}")

                    # If XCUITest driver is not installed, install it
                    if 'xcuitest' not in driver_check.stdout.lower():
                        self.logger.info("Installing XCUITest driver...")
                        subprocess.run(
                            [local_appium_path, "driver", "install", "xcuitest"],
                            check=True
                        )

                    # If UiAutomator2 driver is not installed, install it
                    if 'uiautomator2' not in driver_check.stdout.lower():
                        self.logger.info("Installing UiAutomator2 driver...")
                        subprocess.run(
                            [local_appium_path, "driver", "install", "uiautomator2"],
                            check=True
                        )
                except Exception as e:
                    self.logger.error(f"Error checking/installing drivers: {e}")
            else:
                # Fall back to global installation if local not found
                appium_cmd = "appium"
                self.logger.info("Local Appium not found, using global installation")

                # Ensure drivers are installed globally
                try:
                    # Check if drivers are installed
                    driver_check = subprocess.run(
                        ["appium", "driver", "list", "--installed"],
                        capture_output=True,
                        text=True,
                        check=False
                    )
                    self.logger.info(f"Installed Appium drivers: {driver_check.stdout}")

                    # If XCUITest driver is not installed, install it
                    if 'xcuitest' not in driver_check.stdout.lower():
                        self.logger.info("Installing XCUITest driver globally...")
                        subprocess.run(
                            ["appium", "driver", "install", "xcuitest"],
                            check=True
                        )

                    # If UiAutomator2 driver is not installed, install it
                    if 'uiautomator2' not in driver_check.stdout.lower():
                        self.logger.info("Installing UiAutomator2 driver globally...")
                        subprocess.run(
                            ["appium", "driver", "install", "uiautomator2"],
                            check=True
                        )
                except Exception as e:
                    self.logger.error(f"Error checking/installing global drivers: {e}")

            # Always try to use the inspector plugin
            # If it's not installed, Appium will just ignore it
            self.logger.info("Enabling inspector plugin if available")

            # Prepare command arguments
            cmd_args = [
                appium_cmd,
                "--base-path", "/wd/hub",
                "--port", str(self.appium_port),
                "--relaxed-security",
                "--use-drivers", "xcuitest,uiautomator2",
                "--use-plugins=inspector",  # Use equals sign format
                "--session-override",
                "--allow-cors",
                "--allow-insecure", "chromedriver_autodownload"
            ]

            # Start Appium server with appropriate configuration
            # Create log file path
            log_file_path = os.path.join(project_root, 'appium_server.log')
            self.logger.info(f"Appium server output will be logged to: {log_file_path}")

            # Open log file for writing
            try:
                appium_log_file = open(log_file_path, 'w')
            except Exception as e:
                self.logger.error(f"Failed to open Appium log file {log_file_path}: {e}")
                appium_log_file = subprocess.PIPE # Fallback to pipe if file opening fails

            appium_process = subprocess.Popen(
                cmd_args,
                stdout=appium_log_file, # Redirect stdout to file
                stderr=appium_log_file, # Redirect stderr to file
                text=True,
                # Ensure the process runs independently in the background
                start_new_session=True
            )

            self.logger.info(f"Started Appium server using command: {' '.join(cmd_args)}")
            # Store the process and log file handle
            self.appium_process = appium_process
            self.appium_log_file = appium_log_file


            # Wait for server to start
            max_retries = 15
            for i in range(max_retries):
                try:
                    time.sleep(2)  # Wait between retries
                    response = requests.get(
                        f"http://127.0.0.1:{self.appium_port}/wd/hub/status", timeout=2
                    )
                    if response.status_code == 200:
                        self.logger.info("Appium server started successfully")
                        # Check if drivers are available
                        try:
                            driver_data = response.json()
                            self.logger.info(f"Appium server status: {driver_data}")

                            # Check if the required drivers are available
                            if 'build' in driver_data:
                                self.logger.info("Appium server is ready")
                                return True
                        except Exception as driver_err:
                            self.logger.warning(f"Could not parse Appium status: {driver_err}")
                            # Continue anyway as the server is responding
                            return True
                except Exception as e:
                    self.logger.warning(f"Waiting for Appium server to start (attempt {i+1}/{max_retries}): {e}")

                    # Check if the process is still running
                    if appium_process.poll() is not None:
                        stdout, stderr = appium_process.communicate()
                        self.logger.error(f"Appium process exited with code {appium_process.returncode}")
                        self.logger.error(f"Stdout: {stdout}")
                        self.logger.error(f"Stderr: {stderr}")
                        break

                    if i == max_retries - 1:
                        self.logger.error("Failed to start Appium server after multiple attempts")
                        return False

            return False
        except Exception as e:
            self.logger.error(f"Error ensuring Appium server: {e}")
            self.logger.error(traceback.format_exc())
            return False

    def get_devices(self):
        """
        Get a list of connected devices

        Returns:
            list: List of dictionaries containing device information
        """
        devices = []

        # Get Android devices using ADB
        try:
            adb_output = subprocess.run(
                ['adb', 'devices', '-l'],
                capture_output=True,
                text=True,
                check=True
            ).stdout

            # Parse ADB output
            for line in adb_output.strip().split('\n')[1:]:  # Skip the first line (header)
                if line.strip() and 'device' in line:
                    parts = line.strip().split()
                    device_id = parts[0]

                    # Get additional info
                    try:
                        model = next((p.split(':')[1] for p in parts if p.startswith('model:')), None)
                        device_name = model or device_id

                        # Get Android version
                        android_version = subprocess.run(
                            ['adb', '-s', device_id, 'shell', 'getprop', 'ro.build.version.release'],
                            capture_output=True,
                            text=True
                        ).stdout.strip()

                        devices.append({
                            'id': device_id,
                            'name': device_name,
                            'platform': 'Android',
                            'version': android_version,
                            'type': 'real'
                        })
                    except Exception as e:
                        self.logger.error(f"Error getting Android device info for {device_id}: {e}")
                        # Add with limited info
                        devices.append({
                            'id': device_id,
                            'name': device_id,
                            'platform': 'Android',
                            'version': 'Unknown',
                            'type': 'real'
                        })
        except Exception as e:
            self.logger.error(f"Error getting Android devices: {e}")

        # Get iOS devices using idevice_id
        try:
            idevice_output = subprocess.run(
                ['idevice_id', '-l'],
                capture_output=True,
                text=True,
                check=True
            ).stdout

            # Parse idevice_id output
            for line in idevice_output.strip().split('\n'):
                if line.strip():
                    device_id = line.strip()

                    # Get device name
                    try:
                        device_name = subprocess.run(
                            ['idevicename', '-u', device_id],
                            capture_output=True,
                            text=True
                        ).stdout.strip()

                        # Get iOS version
                        ios_version = subprocess.run(
                            ['ideviceinfo', '-u', device_id, '-k', 'ProductVersion'],
                            capture_output=True,
                            text=True
                        ).stdout.strip()

                        devices.append({
                            'id': device_id,
                            'name': device_name or device_id,
                            'platform': 'iOS',
                            'version': ios_version,
                            'type': 'real',
                            'wda_url': f'http://localhost:{self.wda_port}'  # Use configured WebDriverAgent URL
                        })
                    except Exception as e:
                        self.logger.error(f"Error getting iOS device info for {device_id}: {e}")
                        # Add with limited info
                        devices.append({
                            'id': device_id,
                            'name': device_id,
                            'platform': 'iOS',
                            'version': 'Unknown',
                            'type': 'real',
                            'wda_url': f'http://localhost:{self.wda_port}'  # Use configured WebDriverAgent URL
                        })
        except Exception as e:
            self.logger.error(f"Error getting iOS devices: {e}")

        # Check if tidevice is available for iOS devices
        try:
            tidevice_output = subprocess.run(
                ['tidevice', 'list'],
                capture_output=True,
                text=True,
                check=True
            ).stdout

            # Log tidevice output for debugging
            self.logger.debug(f"tidevice output: {tidevice_output}")

            # Parse tidevice output if no iOS devices found yet
            if not any(device['platform'] == 'iOS' for device in devices):
                # Format is typically: UDID Model iOS
                for line in tidevice_output.strip().split('\n'):
                    if line.strip() and not line.startswith('List of devices'):
                        parts = line.strip().split()
                        if len(parts) >= 2:
                            device_id = parts[0]
                            # Check if this device is already in our list
                            if not any(d['id'] == device_id for d in devices):
                                device_name = parts[1] if len(parts) > 1 else device_id
                                ios_version = parts[2] if len(parts) > 2 else 'Unknown'

                                devices.append({
                                    'id': device_id,
                                    'name': device_name,
                                    'platform': 'iOS',
                                    'version': ios_version,
                                    'type': 'real',
                                    'wda_url': f'http://localhost:{self.wda_port}'  # Use configured WebDriverAgent URL
                                })
        except Exception as e:
            self.logger.debug(f"Error getting devices via tidevice (this is not critical): {e}")

        # Check for WebDriverAgent availability on iOS devices
        # First, read the WDA port mappings if available
        wda_port_mappings = {}
        try:
            if os.path.exists('wda_ports.txt'):
                with open('wda_ports.txt', 'r') as f:
                    for line in f:
                        parts = line.strip().split(':')
                        if len(parts) == 2:
                            wda_port_mappings[parts[0]] = int(parts[1])
                self.logger.info(f"Found WDA port mappings: {wda_port_mappings}")
        except Exception as e:
            self.logger.warning(f"Error reading WDA port mappings: {e}")
            
        for device in devices:
            if device['platform'] == 'iOS' and device['type'] == 'real':
                # Get device-specific port from mappings or use default
                device_id = device['id']
                wda_port = wda_port_mappings.get(device_id, self.wda_port)
                wda_url = f"http://localhost:{wda_port}"
                device['wda_url'] = wda_url
                
                try:
                    # Try to connect to WDA status endpoint
                    import requests
                    response = requests.get(f"{wda_url}/status", timeout=2)
                    if response.status_code == 200:
                        wda_data = response.json()
                        if 'value' in wda_data and 'sessionId' in wda_data['value']:
                            # Update device with actual WDA session ID and status
                            device['wda_session_id'] = wda_data['value']['sessionId']
                            device['wda_state'] = 'Ready'
                            self.logger.info(f"WebDriverAgent is running for device {device['id']} on port {wda_port}")
                        else:
                            device['wda_state'] = 'Running'
                            self.logger.info(f"WebDriverAgent is running but no session available for device {device['id']} on port {wda_port}")
                    else:
                        device['wda_state'] = 'Error'
                        self.logger.warning(f"WebDriverAgent responded with error for device {device['id']} on port {wda_port}: {response.status_code}")
                except Exception as e:
                    device['wda_state'] = 'Not Available'
                    self.logger.warning(f"WebDriverAgent is not available for device {device['id']} on port {wda_port}: {e}")

        # Log all found devices
        self.logger.info(f"Found {len(devices)} device(s): {devices}")
        return devices

    def connect_to_device(self, device_id, options=None, platform=None):
        """
        Connect to a device with the specified ID

        Args:
            device_id (str): The device ID to connect to
            options (dict, optional): Additional options for the connection
            platform (str, optional): Platform hint ('iOS' or 'Android')

        Returns:
            bool: True if connection successful, False otherwise
        """
        self.logger.info(f"Connecting to device: {device_id} with options: {options}, platform hint: {platform}")

        if not options:
            options = {}

        self.device_id = device_id
        self.options = options
        self.browser_name = options.get('browser_name', 'chrome')

        # Maximum number of connection attempts
        max_connection_attempts = 3

        # Try to connect multiple times if needed
        for attempt in range(max_connection_attempts):
            try:
                self.logger.info(f"Connection attempt {attempt+1}/{max_connection_attempts}")

                # Attempt the connection
                if self._attempt_device_connection(device_id, options, platform):
                    self.logger.info(f"Successfully connected to device on attempt {attempt+1}")
                    # Update activity time and restart monitoring if needed
                    self._update_activity_time()
                    if not self.heartbeat_running:
                        self._start_connection_monitoring()
                    return True

                # If connection failed but not due to exception, wait before retry
                self.logger.warning(f"Connection attempt {attempt+1} failed, will retry")
                time.sleep(2)

            except Exception as e:
                self.logger.error(f"Error during connection attempt {attempt+1}: {e}")
                if attempt == max_connection_attempts - 1:
                    self.logger.error("Maximum connection attempts reached, giving up")
                    return False
                time.sleep(2)  # Wait before retry

        return False

    def _attempt_device_connection(self, device_id, options=None, platform=None):
        """
        Internal method to attempt a single device connection

        Args:
            device_id (str): The device ID to connect to
            options (dict, optional): Additional options for the connection
            platform (str, optional): Platform hint ('iOS' or 'Android')

        Returns:
            bool: True if connection successful, False otherwise
        """

        # Determine platform based on platform hint, device ID, and available connected devices
        if platform:
            self.platform_name = platform
            self.logger.info(f"Using provided platform hint: {self.platform_name}")
        else:
            # Try to determine from connected devices
            devices = self.get_devices()
            device_info = next((d for d in devices if d['id'] == device_id), None)

            if device_info:
                self.platform_name = device_info['platform']
                self.logger.info(f"Device platform determined from device list: {self.platform_name}")
            else:
                # Fallback to determining platform from device ID format if device not found in list
                if re.match(r'^[0-9a-f]{40}$', device_id, re.I) or device_id.count('-') >= 4:
                    self.platform_name = 'iOS'
                    self.logger.info(f"Device platform determined from ID format: iOS (physical)")
                else:
                    self.platform_name = 'Android'
                    self.logger.info(f"Device platform determined from ID format: Android")

        # Set up Appium capabilities with enhanced settings
        self.desired_capabilities = {
            'platformName': self.platform_name,
            'deviceName': device_id,
            'udid': device_id,
            'newCommandTimeout': 300,  # 5 minutes timeout for commands
            'noReset': True           # Don't reset app state between sessions
        }

        # Add additional capabilities from options
        if options.get('app'):
            self.desired_capabilities['app'] = options['app']

        if options.get('bundle_id') or options.get('appPackage'):
            if self.platform_name == 'iOS':
                self.desired_capabilities['bundleId'] = options.get('bundle_id')
            else:
                self.desired_capabilities['appPackage'] = options.get('appPackage')
                self.desired_capabilities['appActivity'] = options.get('appActivity', '.MainActivity')

        if options.get('browser_name'):
            self.desired_capabilities['browserName'] = options['browser_name']

            # Browser specific settings
            if options['browser_name'].lower() == 'safari':
                self.desired_capabilities['safariAllowPopups'] = True
                self.desired_capabilities['safariIgnoreFraudWarning'] = True
            elif options['browser_name'].lower() == 'chrome':
                self.desired_capabilities['chromeOptions'] = {
                    'args': ['--disable-popup-blocking']
                }

        # Add Appium settings
        self.desired_capabilities['newCommandTimeout'] = options.get('new_command_timeout', 300)
        self.desired_capabilities['automationName'] = options.get('automation_name',
                                                               'XCUITest' if self.platform_name == 'iOS' else 'UiAutomator2')

        # Android-specific capabilities
        if self.platform_name.lower() == 'android':
            # UiAutomator2 specific settings
            self.desired_capabilities['uiautomator2ServerLaunchTimeout'] = 60000
            self.desired_capabilities['uiautomator2ServerInstallTimeout'] = 60000
            self.desired_capabilities['adbExecTimeout'] = 60000

            # Performance and stability settings
            self.desired_capabilities['skipServerInstallation'] = False
            self.desired_capabilities['skipDeviceInitialization'] = False
            self.desired_capabilities['ignoreHiddenApiPolicyError'] = True

            # Disable animations for better test stability
            self.desired_capabilities['disableWindowAnimation'] = True

            # Auto-grant permissions
            self.desired_capabilities['autoGrantPermissions'] = True

            # Don't stop app on reset
            self.desired_capabilities['dontStopAppOnReset'] = True

            self.logger.info("Added Android-specific UiAutomator2 capabilities")

        # For iOS physical devices
        if self.platform_name == 'iOS':
            self.desired_capabilities['xcodeOrgId'] = options.get('xcodeOrgId', self.DEFAULT_XCODE_ORG_ID)
            self.desired_capabilities['xcodeSigningId'] = options.get('xcodeSigningId', self.DEFAULT_XCODE_SIGNING_ID)

            # If WebDriverAgent URL is provided, use it
            wda_url = options.get('wda_url', f'http://localhost:{self.wda_port}')
            if wda_url:
                self.desired_capabilities['webDriverAgentUrl'] = wda_url
                self.logger.info(f"Using custom WebDriverAgent URL: {wda_url}")

            # Add showIOSLog capability for debugging
            self.desired_capabilities['showIOSLog'] = options.get('show_ios_log', True)

            # If WebDriverAgent local port is provided, use it
            wda_local_port = options.get('wda_local_port')
            if wda_local_port:
                self.desired_capabilities['wdaLocalPort'] = wda_local_port
                self.logger.info(f"Using WDA local port: {wda_local_port}")

        self.logger.info(f"Desired capabilities: {self.desired_capabilities}")

        try:
            # Connect using native device controller first
            # This will be our primary connection method
            if self._connect_with_timeout():
                # Initialize platform-specific helpers based on what's connected
                self._init_platform_helpers()
                return True

            # If Appium connection fails, try to connect with Airtest
            # This is our fallback for iOS devices
            if self.platform_name.lower() == 'ios':
                self.logger.info("Attempting to connect with Airtest as fallback for iOS")
                if self._init_airtest():
                    # We have an Airtest-only connection
                    self.airtest_only_mode = True
                    self.logger.info("Successfully connected with Airtest in fallback mode")
                    return True
                else:
                    self.logger.error("Failed to connect with both Appium and Airtest")
                    return False
            else:
                self.logger.error("Failed to connect with Appium and no fallback available")
                return False

        except Exception as e:
            self.logger.error(f"Error connecting to device: {e}")
            if self.driver:
                self.disconnect()
            return False

    def _connect_with_timeout(self):
        """
        Connect to the device with a timeout

        Returns:
            bool: True if connection successful, False otherwise
        """
        try:
            # Create AppiumOptions object
            options = AppiumOptions()

            # Transfer capabilities dictionary to options
            for key, value in self.desired_capabilities.items():
                options.set_capability(key, value)

            self.logger.info(f"Connecting to Appium server with options: {options.capabilities}")

            # For iOS physical devices, ensure WebDriverAgent is running first
            if self.platform_name.lower() == 'ios':
                return self._connect_ios_device(options)

            # For other devices (Android, etc.), use standard connection
            max_retries = 3
            for attempt in range(max_retries):
                try:
                    self.logger.info(f"Connection attempt {attempt+1}/{max_retries}")

                    # Set a shorter command timeout to prevent hanging sessions
                    options.set_capability('newCommandTimeout', 120)  # 2 minutes instead of 5

                    # Use a connection timeout to prevent hanging during connection
                    import socket
                    original_timeout = socket.getdefaulttimeout()
                    socket.setdefaulttimeout(30)  # 30 second socket timeout

                    try:
                        # Create the driver with explicit timeout
                        self.driver = webdriver.Remote(
                            command_executor=f'http://127.0.0.1:{self.appium_port}/wd/hub',
                            options=options
                        )
                    finally:
                        # Restore original socket timeout
                        socket.setdefaulttimeout(original_timeout)

                    # Verify connection is working by getting a simple property
                    if self.driver:
                        try:
                            # Try to get session capabilities as a quick test
                            caps = self.driver.capabilities
                            self.logger.info(f"Connection verified with capabilities: {caps.get('platformName', 'unknown')}")

                            # Store session capabilities for later reference
                            self.session_capabilities = caps

                            return True
                        except Exception as verify_err:
                            self.logger.warning(f"Driver created but verification failed: {verify_err}")
                            # Try to close the potentially bad session
                            try:
                                self.driver.quit()
                            except:
                                pass
                            self.driver = None
                            # Continue to next retry
                    else:
                        self.logger.warning("Driver creation returned None")

                except Exception as e:
                    self.logger.error(f"Error connecting to device (attempt {attempt+1}/{max_retries}): {e}")
                    if attempt == max_retries - 1:
                        self.logger.error("Maximum retries reached, connection failed")
                        return False

                    # Check if this is a timeout error and handle accordingly
                    if "timeout" in str(e).lower():
                        self.logger.warning("Connection timeout detected, checking Appium server health")
                        self._ensure_appium_server()  # Restart Appium if needed

                    time.sleep(2)  # Short delay before retrying

            return False
        except Exception as e:
            self.logger.error(f"Error in _connect_with_timeout: {e}")
            return False

    def _init_platform_helpers(self):
        """
        Initialize platform-specific helper objects and settings
        based on the connected device platform (iOS or Android)
        """
        self.logger.info(f"Initializing platform helpers for {self.platform_name}")

        try:
            # Common initialization for all platforms
            self.device_dimensions = None

            # Get device dimensions
            try:
                dimensions = self.get_device_dimensions()
                if dimensions:
                    self.logger.info(f"Device dimensions: {dimensions}")
            except Exception as dim_err:
                self.logger.warning(f"Could not get device dimensions: {dim_err}")

            # Initialize the ImageMatcher with platform information
            try:
                from .image_matcher import ImageMatcher
                self.image_matcher = ImageMatcher(
                    device_id=self.device_id,
                    platform=self.platform_name
                )

                # Set the Airtest device if available
                if self.airtest_device:
                    self.image_matcher.set_airtest_device(self.airtest_device)

                self.logger.info(f"Initialized ImageMatcher for {self.platform_name} device: {self.device_id}")
            except Exception as img_err:
                self.logger.warning(f"Failed to initialize ImageMatcher: {img_err}")

            # Platform-specific initialization
            if self.platform_name.lower() == 'ios':
                # iOS-specific helpers
                self.logger.info("Initializing iOS-specific helpers")

                # Check if we have a driver
                if self.driver:
                    try:
                        # Initialize iOS predicates and class chains for improved element finding
                        self.logger.info("Setting up iOS predicate string and class chain support")

                        # Check iOS version to determine available methods
                        try:
                            ios_version_str = self.driver.capabilities.get('platformVersion', '')
                            self.ios_version = float(ios_version_str.split('.')[0])
                            self.logger.info(f"iOS version: {self.ios_version}")
                        except:
                            self.ios_version = 14.0  # Default to a reasonable version
                            self.logger.warning(f"Could not determine iOS version, defaulting to {self.ios_version}")

                        # Set up keyboard handling based on iOS version
                        if self.ios_version >= 15:
                            self.logger.info("Using modern keyboard handling for iOS 15+")
                            self.keyboard_method = "modern"
                        else:
                            self.logger.info("Using legacy keyboard handling for iOS <15")
                            self.keyboard_method = "legacy"
                    except Exception as ios_err:
                        self.logger.warning(f"Error initializing iOS helpers: {ios_err}")

                # Set up Airtest helpers for iOS if available
                if self.airtest_device:
                    try:
                        self.logger.info("Setting up Airtest helpers for iOS")
                        # Any specific Airtest iOS setup would go here
                    except Exception as airtest_err:
                        self.logger.warning(f"Error initializing Airtest helpers for iOS: {airtest_err}")

            elif self.platform_name.lower() == 'android':
                # Android-specific helpers
                self.logger.info("Initializing Android-specific helpers")

                if self.driver:
                    try:
                        # Get Android version
                        try:
                            android_version_str = self.driver.capabilities.get('platformVersion', '')
                            self.android_version = float(android_version_str.split('.')[0])
                            self.logger.info(f"Android version: {self.android_version}")
                        except:
                            self.android_version = 10.0  # Default to a reasonable version
                            self.logger.warning(f"Could not determine Android version, defaulting to {self.android_version}")

                        # Initialize UiAutomator2 helpers
                        self.logger.info("Setting up UiAutomator2 support")

                        # Check if we need ADB shell for certain operations
                        try:
                            self.adb_path = "adb"  # Default ADB path
                            self.device_serial = self.device_id

                            # Test ADB connection
                            result = self._run_adb_command("shell echo Connected")
                            if "Connected" in result:
                                self.logger.info("ADB shell access confirmed")
                                self.adb_available = True
                            else:
                                self.logger.warning("ADB shell access not available")
                                self.adb_available = False
                        except Exception as adb_err:
                            self.logger.warning(f"Error setting up ADB: {adb_err}")
                            self.adb_available = False
                    except Exception as android_err:
                        self.logger.warning(f"Error initializing Android helpers: {android_err}")

                # Set up Airtest helpers for Android if available
                if self.airtest_device:
                    try:
                        self.logger.info("Setting up Airtest helpers for Android")
                        # Any specific Airtest Android setup would go here
                    except Exception as airtest_err:
                        self.logger.warning(f"Error initializing Airtest helpers for Android: {airtest_err}")

            self.logger.info("Platform helpers initialization completed")
            return True

        except Exception as e:
            self.logger.error(f"Error in _init_platform_helpers: {e}")
            self.logger.error(traceback.format_exc())
            return False

    def _is_port_in_use(self, port):
        """
        Check if a port is already in use

        Args:
            port (int): The port to check

        Returns:
            bool: True if port is in use, False otherwise
        """
        import socket
        with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
            return s.connect_ex(('localhost', port)) == 0

    def _start_iproxy(self, device_id, local_port=8100, device_port=8100):
        """
        Start iproxy to forward WebDriverAgent port from the iOS device

        Args:
            device_id (str): The iOS device UDID
            local_port (int): Local port to forward to
            device_port (int): Device port to forward from

        Returns:
            subprocess.Popen: The iproxy process object or None if failed
        """
        try:
            # Check if port is already in use
            if self._is_port_in_use(local_port):
                self.logger.info(f"Port {local_port} is already in use, assuming WebDriverAgent is already running")
                return None

            # Only kill existing iproxy processes for this specific device/port combination
            # instead of killing ALL iproxy processes (which breaks multi-device support)
            if sys.platform != 'win32':
                try:
                    # Find and kill only iproxy processes for this specific port
                    ps_output = subprocess.run(['ps', 'aux'], capture_output=True, text=True, check=False)
                    if ps_output.returncode == 0:
                        for line in ps_output.stdout.split('\n'):
                            if 'iproxy' in line and str(local_port) in line:
                                # Extract PID (second column in ps aux output)
                                parts = line.split()
                                if len(parts) > 1:
                                    try:
                                        pid = int(parts[1])
                                        self.logger.info(f"Killing existing iproxy process for port {local_port} (PID: {pid})")
                                        subprocess.run(['kill', str(pid)], check=False)
                                    except (ValueError, IndexError):
                                        continue
                    time.sleep(1)
                except Exception as e:
                    self.logger.warning(f"Error checking for existing iproxy processes: {e}")
                    # Continue anyway - this is just cleanup

            # Check if tidevice is available (preferred method)
            try:
                import tidevice
                self.logger.info(f"Using tidevice for port forwarding: {local_port} -> {device_port}")
                # Start tidevice for port forwarding
                tidevice_cmd = subprocess.Popen(
                    ['tidevice', '-u', device_id, 'relay', str(local_port), str(device_port)],
                    stdout=subprocess.PIPE,
                    stderr=subprocess.PIPE,
                    text=True
                )
                time.sleep(2)
                if tidevice_cmd.poll() is None:
                    self.logger.info(f"tidevice port forwarding started successfully")
                    return tidevice_cmd
                else:
                    stdout, stderr = tidevice_cmd.communicate()
                    self.logger.warning(f"tidevice failed: {stderr}")
                    # Fall back to iproxy
            except ImportError:
                self.logger.warning("tidevice not available, falling back to iproxy")
            except Exception as e:
                self.logger.warning(f"Error with tidevice: {e}, falling back to iproxy")

            # Fall back to iproxy
            # Start iproxy process with correct argument order
            self.logger.info(f"Starting iproxy for device {device_id} (local:{local_port} -> device:{device_port})")

            # The correct order for iproxy is: iproxy [local_port] [device_port] [udid]
            iproxy_process = subprocess.Popen(
                ['iproxy', str(local_port), str(device_port), device_id],
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True
            )

            # Wait a moment for iproxy to start
            time.sleep(2)

            # Check if process is still running
            if iproxy_process.poll() is not None:
                stdout, stderr = iproxy_process.communicate()
                self.logger.error(f"iproxy process exited with code {iproxy_process.returncode}")
                self.logger.error(f"Stdout: {stdout}")
                self.logger.error(f"Stderr: {stderr}")

                # Try one more time with libimobiledevice's iproxy
                self.logger.info("Trying with libimobiledevice's iproxy...")
                iproxy_alt = subprocess.Popen(
                    ['iproxy', str(local_port), str(device_port)],  # Some versions don't need UDID
                    stdout=subprocess.PIPE,
                    stderr=subprocess.PIPE,
                    text=True
                )
                time.sleep(2)
                if iproxy_alt.poll() is None:
                    self.logger.info("Alternative iproxy command succeeded")
                    return iproxy_alt
                else:
                    stdout, stderr = iproxy_alt.communicate()
                    self.logger.error(f"Alternative iproxy also failed: {stderr}")
                    return None

            self.logger.info(f"iproxy started successfully for device {device_id}")
            return iproxy_process
        except Exception as e:
            self.logger.error(f"Error starting iproxy: {e}")
            self.logger.error(traceback.format_exc())
            return None

    def _connect_ios_device(self, options):
        """
        Connect to an iOS physical device ensuring WebDriverAgent is running

        Args:
            options (AppiumOptions): Appium options for the connection

        Returns:
            bool: True if connection successful, False otherwise
        """
        self.logger.info("Connecting to iOS device via WebDriverAgent")

        # Get device ID from options
        device_id = options.capabilities.get('udid', self.device_id)
        if not device_id:
            self.logger.error("No device ID provided for iOS connection")
            return False

        # Check for device-specific WDA port from wda_ports.txt
        wda_port = self.wda_port  # Use configured default port
        try:
            if os.path.exists('wda_ports.txt'):
                with open('wda_ports.txt', 'r') as f:
                    for line in f:
                        parts = line.strip().split(':')
                        if len(parts) == 2 and parts[0] == device_id:
                            wda_port = int(parts[1])
                            self.logger.info(f"Found port {wda_port} for device {device_id} in wda_ports.txt")
                            break
        except Exception as e:
            self.logger.warning(f"Error reading WDA port mapping: {e}, using default port {self.wda_port}")

        # Use the device-specific port for WDA URL
        wda_url = options.capabilities.get('webDriverAgentUrl', f'http://localhost:{wda_port}')
        # Update the capabilities with the correct WDA URL
        options.capabilities['webDriverAgentUrl'] = wda_url
        self.logger.info(f"Using WebDriverAgent URL: {wda_url} for device {device_id}")
        
        wda_running = False

        try:
            import requests
            response = requests.get(f"{wda_url}/status", timeout=2)
            if response.status_code == 200:
                self.logger.info(f"WebDriverAgent is already running at {wda_url}")
                try:
                    wda_status = response.json()
                    self.logger.info(f"WebDriverAgent status: {wda_status}")
                    wda_running = True

                    # Store screen dimensions if available in WDA status
                    if 'value' in wda_status and 'screen' in wda_status['value']:
                        screen_info = wda_status['value']['screen']
                        if 'width' in screen_info and 'height' in screen_info:
                            self.device_dimensions = {
                                'width': screen_info['width'],
                                'height': screen_info['height']
                            }
                            self.logger.info(f"Got device dimensions from WDA: {self.device_dimensions}")
                except Exception as json_err:
                    self.logger.warning(f"Could not parse WebDriverAgent status: {json_err}")
        except Exception as e:
            self.logger.info(f"WebDriverAgent not detected at {wda_url}, will try to start port forwarding: {e}")

        # Only start iproxy if WebDriverAgent is not already running
        if not wda_running:
            # Start iproxy for port forwarding using the device-specific port
            iproxy_process = self._start_iproxy(device_id, local_port=wda_port, device_port=8100)

            # Check if WebDriverAgent is running after starting iproxy
            try:
                response = requests.get(f"{wda_url}/status", timeout=2)
                if response.status_code != 200:
                    self.logger.error(f"WebDriverAgent is not running at {wda_url}")
                    self.logger.error(f"Response: {response.status_code} - {response.text}")
                    return False

                self.logger.info(f"WebDriverAgent is running at {wda_url}")
                # Log the WebDriverAgent status to help with debugging
                try:
                    wda_status = response.json()
                    self.logger.info(f"WebDriverAgent status: {wda_status}")

                    # Store screen dimensions if available in WDA status
                    if 'value' in wda_status and 'screen' in wda_status['value']:
                        screen_info = wda_status['value']['screen']
                        if 'width' in screen_info and 'height' in screen_info:
                            self.device_dimensions = {
                                'width': screen_info['width'],
                                'height': screen_info['height']
                            }
                            self.logger.info(f"Got device dimensions from WDA: {self.device_dimensions}")
                except Exception as json_err:
                    self.logger.warning(f"Could not parse WebDriverAgent status: {json_err}")
            except Exception as e:
                self.logger.error(f"Cannot connect to WebDriverAgent: {e}")
                return False

        # If browser is Safari, try to ensure it's ready
        if options.capabilities.get('browserName', '').lower() == 'safari':
            self.logger.info("Safari requested, ensuring browser is launched")
            try:
                # Ensure Safari is launched by requesting a test URL
                test_url = "https://www.apple.com"
                self.logger.info(f"Launching Safari with test URL: {test_url}")

                # Try to direct WebDriverAgent to open Safari
                safari_bundle = "com.apple.mobilesafari"
                requests.post(f"{wda_url}/session",
                              json={"capabilities": {"bundleId": safari_bundle}})

                # Set a short timeout as this is just initialization
                time.sleep(2)
            except Exception as e:
                self.logger.warning(f"Error pre-launching Safari: {e}")
                # Continue anyway as this is just a helper step

        # Check if we already have a running Appium server
        appium_running = False
        try:
            import requests
            response = requests.get(f"http://127.0.0.1:{self.appium_port}/wd/hub/status", timeout=2)
            if response.status_code == 200:
                appium_running = True
                self.logger.info("Appium server is already running")
        except Exception:
            self.logger.info("Appium server is not running, will start it")

        # Only start Appium server if it's not already running
        if not appium_running and not self._ensure_appium_server():
            self.logger.error("Failed to ensure Appium server is running")
            # Try to connect directly with WebDriverAgent using Airtest
            return self._connect_with_airtest(device_id, wda_url)

        # Try to connect with retries
        max_retries = 3
        for attempt in range(max_retries):
            try:
                self.logger.info(f"iOS connection attempt {attempt+1}/{max_retries}")

                # Log the exact capabilities being used
                self.logger.info(f"Using capabilities: {options.capabilities}")

                # Check if the XCUITest driver is available
                try:
                    driver_check = requests.get(
                        f"http://127.0.0.1:{self.appium_port}/wd/hub/status",
                        timeout=2
                    )
                    driver_data = driver_check.json()
                    self.logger.info(f"Appium server status before connection: {driver_data}")
                except Exception as check_err:
                    self.logger.warning(f"Could not check driver status: {check_err}")

                # Set a shorter command timeout to prevent hanging sessions
                options.set_capability('newCommandTimeout', 120)  # 2 minutes instead of 5

                # Use a connection timeout to prevent hanging during connection
                import socket
                original_timeout = socket.getdefaulttimeout()
                socket.setdefaulttimeout(30)  # 30 second socket timeout

                try:
                    # Connect to Appium server
                    self.logger.info(f"Connecting to Appium server at http://127.0.0.1:{self.appium_port}/wd/hub")
                    self.driver = webdriver.Remote(
                        command_executor=f'http://127.0.0.1:{self.appium_port}/wd/hub',
                        options=options
                    )
                finally:
                    # Restore original socket timeout
                    socket.setdefaulttimeout(original_timeout)

                self.logger.info("Successfully connected to iOS device")

                # Verify the connection is working
                if self.driver and self.driver.session_id:
                    self.logger.info(f"Connected with session ID: {self.driver.session_id}")

                    # Verify connection is working by getting a simple property
                    try:
                        # Try to get session capabilities as a quick test
                        caps = self.driver.capabilities
                        self.logger.info(f"Connection verified with capabilities: {caps.get('platformName', 'unknown')}")

                        # Store session capabilities for later reference
                        self.session_capabilities = caps

                        return True
                    except Exception as verify_err:
                        self.logger.warning(f"Driver created but verification failed: {verify_err}")
                        # Try to close the potentially bad session
                        try:
                            self.driver.quit()
                        except:
                            pass
                        self.driver = None
                        # Continue to next retry
                        return False
                else:
                    self.logger.error("Connection succeeded but no session ID was created")
                    return False

            except Exception as e:
                self.logger.error(f"Error connecting to iOS device (attempt {attempt+1}/{max_retries}): {e}")

                # Try to get more detailed error information
                error_str = str(e)
                if "Could not find a driver for automationName 'XCUITest'" in error_str:
                    self.logger.error("XCUITest driver not found. Make sure it's installed globally with: appium driver install xcuitest")
                    # Try to check installed drivers
                    try:
                        drivers_output = subprocess.run(["appium", "driver", "list", "--installed"],
                                                      capture_output=True, text=True, check=True).stdout
                        self.logger.info(f"Installed Appium drivers: {drivers_output}")
                    except Exception as driver_err:
                        self.logger.error(f"Could not check installed drivers: {driver_err}")
                elif "Could not read the driver manifest" in error_str and "node_modules" in error_str:
                    self.logger.error("Appium is trying to use a local driver but should use the global one")
                    self.logger.info("Restarting Appium server with explicit driver path...")
                    # Kill any existing Appium server
                    try:
                        subprocess.run(["pkill", "-f", "appium"], check=False)
                        time.sleep(2)
                    except Exception as kill_err:
                        self.logger.warning(f"Error killing Appium processes: {kill_err}")
                    # Restart Appium server
                    if not self._ensure_appium_server():
                        self.logger.error("Failed to restart Appium server")
                        return False

                if attempt == max_retries - 1:
                    self.logger.error("Maximum retries reached, iOS connection failed")
                    # Try to connect directly with WebDriverAgent using Airtest as a fallback
                    return self._connect_with_airtest(device_id, wda_url)
                time.sleep(2)  # Short delay before retrying

        return False

    def _connect_with_airtest(self, device_id, wda_url):
        """
        Fallback method to connect to iOS device using Airtest

        Args:
            device_id (str): The iOS device UDID
            wda_url (str): WebDriverAgent URL

        Returns:
            bool: True if connection successful, False otherwise
        """
        self.logger.info("Attempting to connect with Airtest as fallback for iOS")
        self.logger.info(f"Initializing Airtest connection for device: {device_id}...")

        try:
            # Import Airtest iOS module
            from airtest.core.ios.ios import IOS

            # Connect to the device using WebDriverAgent
            self.logger.info(f"Connecting to iOS device with WebDriverAgent at {wda_url}")
            self.airtest_device = IOS(device_id, wda_url)

            # Try to get screen resolution to verify connection
            try:
                # First try to get screen dimensions from WDA status
                import requests
                response = requests.get(f"{wda_url}/status", timeout=2)
                if response.status_code == 200:
                    wda_status = response.json()
                    if 'value' in wda_status and 'screen' in wda_status['value']:
                        screen_info = wda_status['value']['screen']
                        if 'width' in screen_info and 'height' in screen_info:
                            width = screen_info['width']
                            height = screen_info['height']
                            self.device_dimensions = {'width': width, 'height': height}
                            self.logger.info(f"Got device dimensions from WDA status: {width}x{height}")
                            return True

                # If WDA status doesn't have screen info, try Airtest method
                screen_size = self.airtest_device.get_current_resolution()
                if screen_size and len(screen_size) == 2:
                    width, height = screen_size
                    self.device_dimensions = {'width': width, 'height': height}
                    self.logger.info(f"Connected to iOS device with screen resolution: {screen_size}")
                    return True
                else:
                    # If all else fails, use default dimensions
                    self.device_dimensions = {'width': 1170, 'height': 2532}  # Default iPhone dimensions
                    self.logger.warning(f"Using default iOS device dimensions: {self.device_dimensions}")
                    return True
            except Exception as e:
                self.logger.error(f"Failed to get screen resolution from iOS device: {e}")
                # Still return True but use default dimensions
                self.device_dimensions = {'width': 1170, 'height': 2532}  # Default iPhone dimensions
                self.logger.warning(f"Using default iOS device dimensions: {self.device_dimensions}")
                return True
        except ImportError:
            self.logger.error("Airtest is not installed. Cannot use fallback connection method.")
            return False
        except Exception as e:
            self.logger.error(f"Failed to connect with Airtest: {e}")
            return False

    def reconnect_device(self):
        """
        Attempt to reconnect to the device after a connection loss

        Returns:
            bool: True if reconnection successful, False otherwise
        """
        self.logger.info(f"Attempting to reconnect to device: {self.device_id}")

        # Store device info before disconnecting
        device_id = self.device_id
        platform_name = self.platform_name

        # Log the stored information for debugging
        self.logger.info(f"Stored device information for reconnection: id={device_id}, platform={platform_name}")

        # First, ensure we're fully disconnected to start fresh
        self.disconnect()

        # Verify that platform information was preserved
        self.logger.info(f"After disconnect, device information: id={self.device_id}, platform={self.platform_name}")

        # If platform information was lost, restore it from our local variables
        if not self.platform_name and platform_name:
            self.platform_name = platform_name
            self.logger.info(f"Restored platform_name from local variable: {self.platform_name}")

        if not self.device_id and device_id:
            self.device_id = device_id
            self.logger.info(f"Restored device_id from local variable: {self.device_id}")

        # Wait a moment for resources to be released
        time.sleep(2)

        # Check if Appium server is still running, restart if needed
        try:
            import requests
            response = requests.get(f"http://127.0.0.1:{self.appium_port}/wd/hub/status", timeout=2)
            if response.status_code != 200:
                self.logger.warning("Appium server not responding properly, restarting...")
                self._ensure_appium_server()
        except Exception:
            self.logger.warning("Appium server not responding, restarting...")
            self._ensure_appium_server()

        # For iOS devices, ensure WebDriverAgent is running
        if platform_name and platform_name.lower() == 'ios':
            # Check if we need to restart port forwarding
            try:
                wda_url = 'http://localhost:8100'  # Default WDA URL
                response = requests.get(f"{wda_url}/status", timeout=2)
                if response.status_code != 200:
                    self.logger.warning("WebDriverAgent not responding, restarting port forwarding...")
                    self._setup_ios_port_forwarding(device_id)
            except Exception:
                self.logger.warning("WebDriverAgent not responding, restarting port forwarding...")
                self._setup_ios_port_forwarding(device_id)

        # Now attempt to reconnect using the device ID and platform name
        max_attempts = 3
        for attempt in range(max_attempts):
            self.logger.info(f"Reconnection attempt {attempt+1}/{max_attempts}")
            try:
                # For iOS devices
                if platform_name and platform_name.lower() == 'ios':
                    # Create default iOS options
                    from appium.options.ios import XCUITestOptions
                    options = XCUITestOptions()
                    options.platform_name = 'iOS'
                    options.device_name = device_id
                    options.udid = device_id
                    options.new_command_timeout = 300
                    options.automation_name = 'XCUITest'
                    options.xcode_org_id = self.DEFAULT_XCODE_ORG_ID
                    options.xcode_signing_id = self.DEFAULT_XCODE_SIGNING_ID
                    options.webdriver_agent_url = 'http://localhost:8100'
                    options.show_ios_log = True

                # For Android devices
                elif platform_name and platform_name.lower() == 'android':
                    # Create default Android options
                    from appium.options.android import UiAutomator2Options
                    options = UiAutomator2Options()
                    options.platform_name = 'Android'
                    options.device_name = device_id
                    options.udid = device_id
                    options.new_command_timeout = 300
                    options.automation_name = 'UiAutomator2'
                    options.no_reset = True

                else:
                    self.logger.error(f"Unknown platform: {platform_name}")
                    return False

                if self.connect_to_device(device_id, options, platform_name):
                    self.logger.info(f"Successfully reconnected to device: {device_id}")
                    return True
            except Exception as e:
                self.logger.error(f"Error during reconnection attempt {attempt+1}: {e}")

            # Wait before next attempt
            time.sleep(2)

        self.logger.error(f"Failed to reconnect to device after {max_attempts} attempts")
        return False

    def disconnect(self):
        """Disconnect from the device and clean up all processes"""
        # Store platform information before disconnecting
        stored_device_id = self.device_id
        stored_platform_name = self.platform_name

        # Stop connection monitoring
        self.heartbeat_running = False
        if self.heartbeat_thread and self.heartbeat_thread.is_alive():
            try:
                self.heartbeat_thread.join(timeout=5)
            except Exception as e:
                self.logger.error(f"Error stopping heartbeat thread: {e}")

        # Clean up Appium driver
        if self.driver:
            try:
                self.logger.info("Disconnecting Appium driver")
                self.driver.quit()
            except Exception as e:
                self.logger.error(f"Error disconnecting Appium driver: {e}")
            finally:
                # Always set driver to None even if an error occurs
                self.driver = None
                self.element_cache = {}

        # Clean up Airtest device
        if self.airtest_device:
            try:
                self.logger.info("Disconnecting Airtest device")
                if hasattr(self.airtest_device, 'disconnect'):
                    self.airtest_device.disconnect()
            except Exception as e:
                self.logger.error(f"Error disconnecting Airtest device: {e}")
            finally:
                # Always set airtest_device to None even if an error occurs
                self.airtest_device = None

        # Restore platform information after disconnecting
        self.device_id = stored_device_id
        self.platform_name = stored_platform_name
        self.logger.info(f"Preserved device information after disconnect: id={self.device_id}, platform={self.platform_name}")

        # We don't kill Appium server on disconnect to allow reconnecting quickly
        # But we do kill iproxy processes as they're device-specific
        if sys.platform != 'win32':
            try:
                self.logger.info("Killing iproxy processes")
                subprocess.run(['pkill', '-f', 'iproxy'],
                              stdout=subprocess.PIPE, stderr=subprocess.PIPE, check=False)
            except Exception as e:
                self.logger.warning(f"Error killing iproxy processes: {e}")

        return True

    def shutdown(self):
        """Completely shut down all processes when app is closing"""
        self.disconnect()

        # Now kill Appium server too
        self.logger.info("Shutting down all processes")
        self._kill_existing_processes(force_kill=True)  # Force kill during shutdown

        return True

    def _init_airtest(self, device_id=None):
        """
        Initialize Airtest for additional automation capabilities

        Args:
            device_id (str, optional): The device ID to connect to. If None, uses self.device_id

        Returns:
            bool: True if successful, False otherwise
        """
        try:
            if not AIRTEST_AVAILABLE:
                self.logger.error("Airtest not available. Required for image recognition features.")
                return False

            from airtest.core.api import connect_device, set_current

            # Use the provided device_id or fall back to the stored one
            device_id = device_id or self.device_id

            self.logger.info(f"Initializing Airtest connection for device: {device_id}...")

            # For iOS devices, we need to use the iOS-USB protocol with WebDriverAgent
            if self.platform_name.lower() == 'ios':
                # Try to import the MinimalIOSDevice class
                try:
                    from .ios_device import MinimalIOSDevice
                except ImportError:
                    self.logger.error("Failed to import MinimalIOSDevice class. Make sure ios_device.py exists.")
                    return False

                # For iOS physical devices, connect to WebDriverAgent (WDA)
                try:
                    # Get WDA URL from options or use default
                    wda_url = self.options.get('wda_url', 'http://localhost:8100')
                    self.logger.info(f"Connecting to iOS device with WebDriverAgent at {wda_url}")

                    # Initialize the MinimalIOSDevice directly
                    self.airtest_device = MinimalIOSDevice(device_id, wda_url)

                    # Try to get screen resolution to validate connection
                    resolution = self.airtest_device.get_current_resolution()
                    if resolution:
                        self.logger.info(f"Successfully connected to iOS device with resolution {resolution}")
                        return True
                    else:
                        self.logger.error("Failed to get screen resolution from iOS device")
                        return False
                except Exception as e:
                    self.logger.error(f"Failed to connect to iOS device with WebDriverAgent: {e}")
                    self.logger.error(traceback.format_exc())
                    return False

            # For Android devices
            elif self.platform_name.lower() == 'android':
                # For Android, we use adb uri with the device_id
                device_uri = f"android://127.0.0.1:5037/{device_id}?cap_method=JAVACAP&ori_method=ADBORI"

                # Connect to the device using Airtest
                try:
                    self.logger.info(f"Connecting to Android device with Airtest using URI: {device_uri}")
                    self.airtest_device = connect_device(device_uri)
                    set_current(self.airtest_device)
                    self.logger.info("Successfully connected to Android device with Airtest")
                    return True
                except Exception as e:
                    self.logger.error(f"Failed to connect to Android device with Airtest: {e}")
                    return False
            else:
                self.logger.error(f"Unsupported platform for Airtest: {self.platform_name}")
                return False

        except ImportError as e:
            self.logger.error(f"Airtest not installed: {e}")
            return False
        except Exception as e:
            self.logger.error(f"Error initializing Airtest: {e}")
            self.logger.error(traceback.format_exc())
            return False

    def _init_minimal_ios_device(self):
        """
        Initialize a MinimalIOSDevice for Airtest compatibility

        Returns:
            bool: True if successful, False otherwise
        """
        try:
            # Import the MinimalIOSDevice class
            from .ios_device import MinimalIOSDevice

            # Get the WebDriverAgent URL from options
            wda_url = self.options.get('wda_url', 'http://localhost:8100')

            # Create the MinimalIOSDevice instance
            self.airtest_device = MinimalIOSDevice(
                device_id=self.device_id,
                wda_url=wda_url
            )

            # Try to get screen resolution to verify connection
            resolution = self.airtest_device.get_current_resolution()
            if resolution:
                self.logger.info(f"Successfully initialized MinimalIOSDevice with resolution {resolution}")
                return True
            else:
                self.logger.error("Failed to get screen resolution from MinimalIOSDevice")
                return False
        except ImportError:
            self.logger.error("Failed to import MinimalIOSDevice class")
            return False
        except Exception as e:
            self.logger.error(f"Error initializing MinimalIOSDevice: {e}")
            self.logger.error(traceback.format_exc())
            return False

    def _ensure_airtest_connected(self):
        """
        Ensure Airtest is connected, initializing if needed

        Returns:
            bool: True if connected, False otherwise
        """
        if self.airtest_device:
            return True

        self.logger.info("Airtest not connected, initializing connection...")
        return self._init_airtest()

    def take_screenshot(self, filename=None, save_debug=False, test_idx=None, step_idx=None, suite_id=None, action_id=None):
        """
        Take a screenshot of the current screen with retry logic and fallbacks

        Args:
            filename (str, optional): Path to save the screenshot. If None, a timestamped file will be created.
            save_debug (bool, optional): Whether to save debug images. Defaults to False.
            test_idx (int, optional): Test case index for the screenshot.
            step_idx (int, optional): Step index for the screenshot.
            suite_id (str, optional): Suite ID for the screenshot.
            action_id (str, optional): Unique action ID for the screenshot.

        Returns:
            dict: {'status': 'success'/'error', 'message': str, 'path': str or None}
        """
        # Import required modules
        import string
        import random

        # Check if we already have a screenshot with this action_id
        if action_id:
            from .database import check_screenshot_exists
            if check_screenshot_exists(action_id):
                self.logger.info(f"Screenshot with action_id {action_id} already exists, returning existing screenshot")

                # Try to find the existing screenshot file
                try:
                    # First check in the current screenshots directory if available
                    from app import current_screenshots_dir
                    if current_screenshots_dir and os.path.exists(current_screenshots_dir):
                        existing_path = os.path.join(current_screenshots_dir, f"{action_id}.png")
                        if os.path.exists(existing_path):
                            self.logger.info(f"Found existing screenshot in current report folder: {existing_path}")
                            screenshot_url = f"/screenshots/{action_id}.png"
                            return {
                                'status': 'success',
                                'message': 'Using existing screenshot with same action_id from report folder',
                                'path': existing_path,
                                'screenshot_url': screenshot_url,
                                'is_existing': True
                            }
                except (ImportError, AttributeError, Exception) as e:
                    self.logger.warning(f"Error checking current_screenshots_dir: {e}")

                # If not found in report folder, check in static screenshots directory
                static_path = os.path.join(self.screenshot_dir, f"{action_id}.png")
                if os.path.exists(static_path):
                    self.logger.info(f"Found existing screenshot in static folder: {static_path}")
                    screenshot_url = f"/screenshots/{action_id}.png"
                    return {
                        'status': 'success',
                        'message': 'Using existing screenshot with same action_id from static folder',
                        'path': static_path,
                        'screenshot_url': screenshot_url,
                        'is_existing': True
                    }

                # If we couldn't find the file but it exists in the database, create a placeholder
                self.logger.warning(f"Screenshot with action_id {action_id} exists in database but file not found")
                screenshot_url = f"/screenshots/{action_id}.png"
                return {
                    'status': 'success',
                    'message': 'Using existing screenshot reference with same action_id',
                    'path': os.path.join(self.screenshot_dir, f"{action_id}.png"),
                    'screenshot_url': screenshot_url,
                    'is_existing': True
                }

        # Set up retry parameters
        max_retries = 3
        retry_count = 0
        last_error = None

        # If filename is already provided, use it directly
        if filename:
            # Extract action_id from filename if not provided
            if not action_id and os.path.basename(filename).endswith('.png'):
                # Import string module locally to ensure it's available
                import string
                base_name = os.path.basename(filename).replace('.png', '')
                if len(base_name) == 10 and all(c in string.ascii_letters + string.digits for c in base_name):
                    action_id = base_name
                    self.logger.info(f"Extracted action_id from filename: {action_id}")
        else:
            # Generate filename if not provided
            try:
                from app import current_screenshots_dir, current_test_idx, current_step_idx

                # Always save to the report directory if available
                if current_screenshots_dir and os.path.exists(current_screenshots_dir):
                    self.logger.info(f"Using current report screenshots directory: {current_screenshots_dir}")

                    # Use the provided indices or get from current context
                    if test_idx is None:
                        test_idx = getattr(current_test_idx, 'value', 0)
                    if step_idx is None:
                        step_idx = getattr(current_step_idx, 'value', 0)

                    # Always use action_id for the filename
                    if action_id:
                        self.logger.info(f"Using action_id for screenshot filename: {action_id}")
                        standardized_filename = f"{action_id}.png"
                    else:
                        # We should never generate a new action_id here
                        # Log a warning and use a placeholder action_id
                        self.logger.warning("No action_id provided for screenshot - this should not happen")
                        # Use a placeholder that's clearly not a real action_id
                        action_id = "placeholder"
                        standardized_filename = f"{action_id}.png"

                    filename = os.path.join(current_screenshots_dir, standardized_filename)
                else:
                    # If no report directory, use the action_id for the filename in the static directory
                    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                    if action_id:
                        self.logger.info(f"Using action_id for screenshot filename: {action_id}")
                        standardized_filename = f"{action_id}.png"
                    else:
                        # We should never generate a new action_id here
                        # Log a warning and use a placeholder action_id
                        self.logger.warning("No action_id provided for screenshot - this should not happen")
                        # Use a placeholder that's clearly not a real action_id
                        action_id = "placeholder"
                        standardized_filename = f"{action_id}.png"

                    filename = os.path.join(self.screenshot_dir, standardized_filename)
            except (ImportError, AttributeError) as e:
                self.logger.warning(f"Error getting current screenshots directory: {e}")
                # If error, use the action_id for the filename in the static directory
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                if action_id:
                    self.logger.info(f"Using action_id for screenshot filename: {action_id}")
                    standardized_filename = f"{action_id}.png"
                else:
                    # We should never generate a new action_id here
                    # Log a warning and use a placeholder action_id
                    self.logger.warning("No action_id provided for screenshot - this should not happen")
                    # Use a placeholder that's clearly not a real action_id
                    action_id = "placeholder"
                    standardized_filename = f"{action_id}.png"

                filename = os.path.join(self.screenshot_dir, standardized_filename)

        # Extract test and step indices from filename if it follows the pattern step_X_Y.png
        if isinstance(filename, str) and 'step_' in filename:
            import re
            match = re.match(r'.*step_(\d+)_(\d+).*\.png', os.path.basename(filename))
            if match:
                extracted_test_idx = int(match.group(1))
                extracted_step_idx = int(match.group(2))

                # Only update if not explicitly provided
                if test_idx is None:
                    test_idx = extracted_test_idx
                if step_idx is None:
                    step_idx = extracted_step_idx

                self.logger.info(f"Extracted indices from filename: test_idx={test_idx}, step_idx={step_idx}")

        # Ensure the directory exists
        os.makedirs(os.path.dirname(filename), exist_ok=True)

        # Define latest.png paths
        latest_path = os.path.join(
            os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))),
            "app", "static", "screenshots", "latest.png"
        )
        os.makedirs(os.path.dirname(latest_path), exist_ok=True)

        # Get report directory latest.png path if available
        report_latest_path = None
        try:
            from app import current_screenshots_dir
            if current_screenshots_dir and os.path.exists(current_screenshots_dir):
                report_latest_path = os.path.join(current_screenshots_dir, "latest.png")
        except (ImportError, AttributeError):
            pass

        self.logger.info(f"Taking screenshot to {filename} (save_debug={save_debug})")

        # Try multiple times to get a screenshot
        while retry_count < max_retries:
            try:
                # First try with ImageMatcher if available (best for iOS)
                if hasattr(self, 'image_matcher') and self.image_matcher:
                    try:
                        self.logger.info(f"Taking screenshot using ImageMatcher (attempt {retry_count+1}/{max_retries})")

                        # Take screenshot using ImageMatcher
                        screenshot_path = self.image_matcher.take_screenshot_with_adb()

                        # If successful, copy to the desired location
                        if screenshot_path and os.path.exists(screenshot_path) and os.path.getsize(screenshot_path) > 0:
                            import shutil
                            shutil.copy2(screenshot_path, filename)
                            self.logger.info(f"Screenshot taken with ImageMatcher and copied to {filename}")

                            # Verify the screenshot was actually saved
                            if os.path.exists(filename) and os.path.getsize(filename) > 0:
                                # Only update latest.png in the report folder, not create additional copies
                                try:
                                    if report_latest_path:
                                        import shutil
                                        # Only copy to latest.png, don't create additional files
                                        if os.path.basename(filename) != "latest.png":
                                            shutil.copy2(filename, report_latest_path)
                                            self.logger.info(f"Screenshot also saved as latest.png in report folder: {report_latest_path}")
                                except Exception as copy_err:
                                    self.logger.warning(f"Error copying to latest.png in report folder: {copy_err}")

                                # Save to database
                                self._save_screenshot_to_database(filename, test_idx, step_idx, suite_id, action_id)

                                # Success
                                # Create a screenshot URL for the client
                                screenshot_url = f"/screenshots/{os.path.basename(filename)}"
                                self.logger.info(f"Generated screenshot URL: {screenshot_url}")

                                return {
                                    'status': 'success',
                                    'message': 'Screenshot taken successfully using ImageMatcher',
                                    'path': filename,
                                    'screenshot_url': screenshot_url
                                }
                            else:
                                self.logger.warning("ImageMatcher screenshot copied but file is missing or empty")
                                # Will fall back to other methods
                        else:
                            self.logger.warning("ImageMatcher screenshot failed, falling back to Airtest")
                            # Will fall back to Airtest method
                    except Exception as img_err:
                        last_error = f"ImageMatcher screenshot failed: {str(img_err)}"
                        self.logger.warning(last_error)
                        # Will fall back to Airtest method

                # Then try with Airtest if available
                if self.airtest_device and hasattr(self.airtest_device, 'snapshot'):
                    try:
                        self.logger.info(f"Taking screenshot using Airtest (attempt {retry_count+1}/{max_retries})")

                        # Pass save_debug parameter if supported
                        if hasattr(self.airtest_device, 'supports_debug_files') and self.airtest_device.supports_debug_files:
                            screenshot = self.airtest_device.snapshot(filename, save_debug=save_debug)
                        else:
                            screenshot = self.airtest_device.snapshot(filename)

                        # Verify the screenshot was actually saved
                        if screenshot and os.path.exists(filename) and os.path.getsize(filename) > 0:
                            # Only update latest.png in the report folder, not create additional copies
                            try:
                                if report_latest_path:
                                    import shutil
                                    # Only copy to latest.png, don't create additional files
                                    if os.path.basename(filename) != "latest.png":
                                        shutil.copy2(filename, report_latest_path)
                                        self.logger.info(f"Screenshot also saved as latest.png in report folder: {report_latest_path}")
                            except Exception as copy_err:
                                self.logger.warning(f"Error copying to latest.png in report folder: {copy_err}")

                            # Save to database
                            self._save_screenshot_to_database(filename, test_idx, step_idx, suite_id, action_id)

                            # Success
                            # Create a screenshot URL for the client
                            screenshot_url = f"/screenshots/{os.path.basename(filename)}"
                            self.logger.info(f"Generated screenshot URL: {screenshot_url}")

                            return {
                                'status': 'success',
                                'message': 'Screenshot taken successfully using Airtest',
                                'path': filename,
                                'screenshot_url': screenshot_url
                            }
                        else:
                            self.logger.warning("Airtest screenshot call seemed to succeed but file not found or empty")
                            # Will fall back to Appium method
                    except Exception as airtest_err:
                        last_error = f"Airtest screenshot failed: {str(airtest_err)}"
                        self.logger.warning(last_error)
                        # Will fall back to Appium method

                # If we get here, either Airtest failed or isn't available - try Appium
                if self.driver:
                    try:
                        self.logger.info(f"Taking screenshot using Appium (attempt {retry_count+1}/{max_retries})")

                        # Try to get screenshot as base64
                        screenshot_data = self.driver.get_screenshot_as_base64()
                        if screenshot_data:
                            # Decode and save to file
                            image_data = base64.b64decode(screenshot_data)
                            with open(filename, 'wb') as f:
                                f.write(image_data)

                            # Verify the file was saved correctly
                            if os.path.exists(filename) and os.path.getsize(filename) > 0:
                                # Only update latest.png in the report folder, not create additional copies
                                try:
                                    if report_latest_path:
                                        # Only write to latest.png, don't create additional files
                                        if os.path.basename(filename) != "latest.png":
                                            with open(report_latest_path, 'wb') as f:
                                                f.write(image_data)
                                            self.logger.info(f"Screenshot also saved as latest.png in report folder: {report_latest_path}")
                                except Exception as copy_err:
                                    self.logger.warning(f"Error copying to latest.png in report folder: {copy_err}")

                                # Save to database
                                self._save_screenshot_to_database(filename, test_idx, step_idx, suite_id, action_id)

                                # Success
                                # Create a screenshot URL for the client
                                screenshot_url = f"/screenshots/{os.path.basename(filename)}"
                                self.logger.info(f"Generated screenshot URL: {screenshot_url}")

                                return {
                                    'status': 'success',
                                    'message': 'Screenshot taken successfully using Appium',
                                    'path': filename,
                                    'screenshot_url': screenshot_url
                                }
                            else:
                                last_error = "Appium screenshot was saved but file is missing or empty"
                                self.logger.warning(last_error)
                        else:
                            last_error = "Appium returned empty screenshot data"
                            self.logger.warning(last_error)
                    except Exception as appium_err:
                        last_error = f"Appium screenshot failed: {str(appium_err)}"
                        self.logger.warning(last_error)
                else:
                    last_error = "No driver available for Appium screenshot"
                    self.logger.warning(last_error)

                # If we get here, both methods failed - retry if attempts remain
                retry_count += 1
                if retry_count < max_retries:
                    self.logger.info(f"Screenshot attempt {retry_count} failed, retrying ({retry_count+1}/{max_retries})...")

                    # Try to recover connection before next attempt
                    if retry_count > 0:
                        try:
                            self.recover_connection()
                            self.logger.info("Attempted device connection recovery before next screenshot attempt")
                        except Exception as recover_err:
                            self.logger.warning(f"Failed to recover connection: {str(recover_err)}")

                    # Wait before retry
                    time.sleep(1)
                else:
                    self.logger.error("All screenshot attempts failed, trying fallback options")
                    break
            except Exception as e:
                last_error = f"Unexpected error in screenshot process: {str(e)}"
                self.logger.error(last_error)
                retry_count += 1
                time.sleep(1)

        # All attempts failed, try fallback solutions

        # 1. First check if report_latest.png exists and copy it
        if report_latest_path and os.path.exists(report_latest_path) and os.path.getsize(report_latest_path) > 0:
            try:
                # Only copy if the target file doesn't exist or is different from latest.png
                if not os.path.exists(filename) or os.path.abspath(filename) != os.path.abspath(report_latest_path):
                    import shutil
                    shutil.copy2(report_latest_path, filename)
                    self.logger.info(f"Used report latest.png as fallback for screenshot")

                # Save to database
                self._save_screenshot_to_database(filename, test_idx, step_idx, suite_id, action_id)

                # Create a screenshot URL for the client
                screenshot_url = f"/screenshots/{os.path.basename(filename)}"
                self.logger.info(f"Generated fallback screenshot URL: {screenshot_url}")

                return {
                    'status': 'partial_success',
                    'message': f'Used fallback latest.png from report folder. Original error: {last_error}',
                    'path': filename,
                    'is_fallback': True,
                    'screenshot_url': screenshot_url
                }
            except Exception as fallback_err:
                self.logger.error(f"Failed to use latest.png fallback: {str(fallback_err)}")

        # 2. If still failed, look for any existing image with action_id pattern
        if action_id is not None:
            try:
                # Look for any existing screenshot with the same action_id
                existing_path = os.path.join(os.path.dirname(filename), f"{action_id}.png")

                if os.path.exists(existing_path):
                    # Only copy if the target file doesn't exist or is different from the existing file
                    if not os.path.exists(filename) or os.path.abspath(filename) != os.path.abspath(existing_path):
                        import shutil
                        shutil.copy2(existing_path, filename)
                        self.logger.info(f"Used existing action_id file as fallback: {existing_path} -> {filename}")

                    # Save to database
                    self._save_screenshot_to_database(filename, test_idx, step_idx, suite_id, action_id)

                    # Create a screenshot URL for the client
                    screenshot_url = f"/screenshots/{os.path.basename(filename)}"
                    self.logger.info(f"Generated action_id fallback screenshot URL: {screenshot_url}")

                    return {
                        'status': 'partial_success',
                        'message': f'Used existing action_id file as fallback. Original error: {last_error}',
                        'path': filename,
                        'is_fallback': True,
                        'screenshot_url': screenshot_url
                    }
            except Exception as similar_err:
                self.logger.error(f"Failed to use action_id file fallback: {str(similar_err)}")

        # If all fallbacks failed, return error
        return {
            'status': 'error',
            'message': last_error,
            'path': None
        }

    def find_element_at_position(self, x, y):
        """
        Alias for get_element_at_position to maintain backward compatibility

        Args:
            x (int): X coordinate
            y (int): Y coordinate

        Returns:
            dict: Element information including attributes, locators, and bounds
        """
        self.logger.info(f"find_element_at_position({x}, {y}) - calling get_element_at_position")
        return self.get_element_at_position(x, y)

    def get_element_at_position(self, x, y):
        """
        Get information about the element at a specific position

        Args:
            x (int): X coordinate
            y (int): Y coordinate

        Returns:
            dict: Element information including attributes, locators, and bounds
        """
        self.logger.info(f"Getting element at position ({x}, {y})")

        try:
            if not self.driver:
                self.logger.error("No Appium driver available")
                return None

            # Try to get element using Appium's tap_point method (iOS) or UiAutomator2 (Android)
            if self.platform_name and self.platform_name.lower() == 'ios':
                try:
                    # For iOS, use the mobile: findElementByXY command
                    element_data = self.driver.execute_script('mobile: findElementByXY', {'x': x, 'y': y})

                    if element_data:
                        # Process the element data
                        attributes = element_data.get('attributes', {})
                        locators = {}

                        # Extract common locator strategies
                        if 'name' in element_data:
                            locators['accessibility id'] = [element_data['name']]

                        # Add XPath locator
                        if 'type' in element_data:
                            locators['xpath'] = [f"//{element_data['type']}"]

                            # Add more specificity if possible
                            if 'name' in element_data and element_data['name']:
                                locators['xpath'] = [f"//{element_data['type']}[@name='{element_data['name']}']"]

                        # Create element info structure
                        element_info = {
                            'attributes': attributes,
                            'locators': locators,
                            'bounds': element_data.get('rect', {})
                        }

                        return element_info
                except Exception as ios_err:
                    self.logger.warning(f"Error getting element using iOS method: {str(ios_err)}")

            # For Android or as fallback
            try:
                # Get the source
                page_source = self.driver.page_source

                # Tap on the position to get the element
                action = self.driver.action
                action.tap(x=x, y=y).perform()

                # Get the currently selected element
                element = self.driver.switch_to.active_element

                if element:
                    # Get element attributes
                    attributes = {}

                    # Common attributes to check
                    attr_names = ['text', 'content-desc', 'resource-id', 'class', 'package',
                                  'checkable', 'checked', 'clickable', 'enabled', 'focusable',
                                  'focused', 'scrollable', 'long-clickable', 'password', 'selected']

                    for attr in attr_names:
                        try:
                            value = element.get_attribute(attr)
                            if value:
                                attributes[attr] = value
                        except:
                            pass

                    # Get element location and size
                    try:
                        rect = element.rect
                        bounds = {
                            'x': rect['x'],
                            'y': rect['y'],
                            'width': rect['width'],
                            'height': rect['height']
                        }
                    except:
                        bounds = {'x': x, 'y': y, 'width': 0, 'height': 0}

                    # Create locators
                    locators = {}

                    # Resource ID locator
                    if 'resource-id' in attributes and attributes['resource-id']:
                        locators['id'] = [attributes['resource-id']]

                    # Content description locator
                    if 'content-desc' in attributes and attributes['content-desc']:
                        locators['accessibility id'] = [attributes['content-desc']]

                    # Text locator
                    if 'text' in attributes and attributes['text']:
                        locators['text'] = [attributes['text']]

                    # Class locator
                    if 'class' in attributes and attributes['class']:
                        class_name = attributes['class']
                        locators['class'] = [class_name]

                        # Create XPath with class
                        xpath = f"//{class_name}"

                        # Add more specificity if text or content-desc is available
                        if 'text' in attributes and attributes['text']:
                            xpath += f"[@text='{attributes['text']}']"
                        elif 'content-desc' in attributes and attributes['content-desc']:
                            xpath += f"[@content-desc='{attributes['content-desc']}']"

                        locators['xpath'] = [xpath]

                    # Create element info structure
                    element_info = {
                        'attributes': attributes,
                        'locators': locators,
                        'bounds': bounds
                    }

                    return element_info
            except Exception as general_err:
                self.logger.warning(f"Error getting element using general method: {str(general_err)}")

            # If we get here, both methods failed
            self.logger.error(f"Could not get element at position ({x}, {y})")
            return None
        except Exception as e:
            self.logger.error(f"Error in get_element_at_position: {str(e)}")
            self.logger.error(traceback.format_exc())
            return None

    def input_text(self, locator_type=None, locator_value=None, text=None, clear_first=True, timeout=15):
        """
        Input text into an element identified by a locator

        Args:
            locator_type (str): Type of locator (id, xpath, accessibility_id, etc.)
            locator_value (str): Value of the locator
            text (str): Text to input
            clear_first (bool): Whether to clear the field before inputting text
            timeout (int): Maximum time to wait for the element in seconds

        Returns:
            dict: Result with status and message
        """
        # Handle the case when only text is provided (from TextAction)
        if text is None and isinstance(locator_type, str):
            text = locator_type
            locator_type = None
            locator_value = None
            self.logger.info(f"Inputting text '{text}' directly (no locator)")
        else:
            self.logger.info(f"Inputting text '{text}' into element with {locator_type}='{locator_value}'")

        if not text:
            self.logger.error("No text provided for input")
            return {
                "status": "error",
                "message": "No text provided for input"
            }

        if not self.driver and not self.airtest_device:
            self.logger.error("No Appium driver or Airtest device available")
            return {
                "status": "error",
                "message": "No Appium driver or Airtest device available"
            }

        try:
            # If no locator is provided, use Airtest or direct input methods
            if locator_type is None or locator_value is None:
                # Try using Airtest first
                if self.airtest_device and hasattr(self.airtest_device, 'text'):
                    try:
                        self.logger.info(f"Using Airtest to input text: '{text}'")
                        self.airtest_device.text(text)
                        return {
                            "status": "success",
                            "message": f"Text input successful using Airtest: '{text}'"
                        }
                    except Exception as airtest_e:
                        self.logger.warning(f"Error inputting text with Airtest: {airtest_e}")
                        # Continue to other methods

                # Try using Appium's direct text input for Android
                if self.driver and self.platform_name and self.platform_name.lower() == 'android':
                    try:
                        self.logger.info(f"Using Android direct text input: '{text}'")
                        # For Android, we can use ADB's input text command
                        self.driver.execute_script('mobile: shell', {
                            'command': 'input',
                            'args': ['text', text]
                        })
                        return {
                            "status": "success",
                            "message": f"Text input successful using Android shell: '{text}'"
                        }
                    except Exception as android_e:
                        self.logger.warning(f"Error inputting text with Android shell: {android_e}")
                        # Continue to other methods

                # For iOS, try multiple methods to input text
                if self.driver and self.platform_name and self.platform_name.lower() == 'ios':
                    # Method 1: Use XCUITest's typeText method via mobile: commands
                    try:
                        self.logger.info(f"Method 1: Using XCUITest mobile:typeText to input text: '{text}'")

                        # Use the mobile: typeText command which is the most reliable for iOS
                        self.driver.execute_script('mobile: typeText', {'text': text})

                        return {
                            "status": "success",
                            "message": f"Text input successful using XCUITest mobile:typeText: '{text}'"
                        }
                    except Exception as ios_e1:
                        self.logger.warning(f"Method 1 failed to input text using XCUITest mobile:typeText: {ios_e1}")

                        # Method 2: Try using active element
                        try:
                            self.logger.info(f"Method 2: Using active element to input text: '{text}'")

                            # Get the active element
                            active_element = self.driver.switch_to.active_element

                            # Send keys to the active element
                            active_element.send_keys(text)

                            return {
                                "status": "success",
                                "message": f"Text input successful using active element: '{text}'"
                            }
                        except Exception as ios_e2:
                            self.logger.warning(f"Method 2 failed to input text using active element: {ios_e2}")

                            # Method 3: Try using mobile: type (older method)
                            try:
                                self.logger.info(f"Method 3: Using mobile: type to input text: '{text}'")

                                # Use mobile: type command for iOS (older method)
                                self.driver.execute_script('mobile: type', {'text': text})

                                return {
                                    "status": "success",
                                    "message": f"Text input successful using mobile: type: '{text}'"
                                }
                            except Exception as ios_e3:
                                self.logger.warning(f"Method 3 failed to input text using mobile: type: {ios_e3}")

                                # Method 4: Try using keyboard element
                                try:
                                    self.logger.info(f"Method 4: Using Appium driver keyboard element to input text: '{text}'")

                                    # Find keyboard element
                                    keyboard = self.driver.find_element(by='class name', value='XCUIElementTypeKeyboard')

                                    # Type text character by character
                                    for char in text:
                                        try:
                                            key = self.driver.find_element(by='name', value=char)
                                            key.click()
                                        except:
                                            self.logger.warning(f"Could not find key for character: {char}")

                                    return {
                                        "status": "success",
                                        "message": f"Text input successful using keyboard element: '{text}'"
                                    }
                                except Exception as ios_e4:
                                    self.logger.warning(f"Method 4 failed to input text using keyboard element: {ios_e4}")
                                    # Continue to other methods

                # Try one more time with Airtest as a last resort
                if self.airtest_device and hasattr(self.airtest_device, 'text'):
                    try:
                        self.logger.info(f"Trying to input text using Airtest as last resort: '{text}'")
                        # For Airtest, we can't use locators directly, so we'll just input text
                        # This will work if the field is already focused
                        self.airtest_device.text(text)
                        return {
                            "status": "success",
                            "message": f"Text input successful using Airtest: '{text}'"
                        }
                    except Exception as airtest_e:
                        self.logger.warning(f"Error inputting text with Airtest: {airtest_e}")
                        # Continue to other methods

                # If we get here, we couldn't input text without a locator
                return {
                    "status": "error",
                    "message": f"Cannot input text without a valid locator: '{text}'"
                }

            # If locator is provided, use standard element-based input
            try:
                from selenium.webdriver.common.by import By
                from selenium.webdriver.support.ui import WebDriverWait
                from selenium.webdriver.support import expected_conditions as EC
                from selenium.common.exceptions import TimeoutException

                locator_map = {
                    'id': By.ID,
                    'xpath': By.XPATH,
                    'name': By.NAME,
                    'class': By.CLASS_NAME,
                    'accessibility_id': 'accessibility id',  # Appium specific
                    'ios_predicate': '-ios predicate string',  # iOS specific
                    'android_uiautomator': '-android uiautomator',  # Android specific
                    'ios_class_chain': '-ios class chain'  # iOS specific
                }

                by_type = locator_map.get(locator_type.lower())
                if not by_type:
                    by_type = locator_type  # Use as-is if not in mapping

                # Wait for element to be visible and enabled
                element = WebDriverWait(self.driver, timeout).until(
                    EC.element_to_be_clickable((by_type, locator_value))
                )

                # Clear the field if requested
                if clear_first:
                    try:
                        element.clear()
                    except Exception as clear_e:
                        self.logger.warning(f"Could not clear element before input: {clear_e}")
                        # Continue with input even if clear fails

                # For iOS, try multiple methods for text input
                if self.platform_name and self.platform_name.lower() == 'ios':
                    # Method 1: Try clicking the element first, then using XCUITest's typeText
                    try:
                        self.logger.info(f"Method 1: Clicking element then using XCUITest mobile:typeText for text input: '{text}'")
                        # Click the element to focus it
                        element.click()

                        # Use the mobile: typeText command which is the most reliable for iOS
                        self.driver.execute_script('mobile: typeText', {'text': text})

                        return {
                            "status": "success",
                            "message": f"Text input successful using click+mobile:typeText: '{text}' into {locator_type}='{locator_value}'"
                        }
                    except Exception as ios_e1:
                        self.logger.warning(f"Method 1 failed - Error using click+mobile:typeText: {ios_e1}")

                        # Method 2: Try using the element's send_keys method directly
                        try:
                            self.logger.info(f"Method 2: Using element.send_keys for iOS text input: '{text}'")
                            element.send_keys(text)
                            return {
                                "status": "success",
                                "message": f"Text input successful using element.send_keys: '{text}' into {locator_type}='{locator_value}'"
                            }
                        except Exception as ios_e2:
                            self.logger.warning(f"Method 2 failed - Error using element.send_keys: {ios_e2}")

                            # Method 3: Try clicking the element first, then using active element
                            try:
                                self.logger.info(f"Method 3: Clicking element then using active element for text input: '{text}'")
                                # Click the element to focus it (try again)
                                element.click()

                                # Get the active element and send keys
                                active_element = self.driver.switch_to.active_element
                                active_element.send_keys(text)
                                return {
                                    "status": "success",
                                    "message": f"Text input successful using click+active element: '{text}' into {locator_type}='{locator_value}'"
                                }
                            except Exception as ios_e3:
                                self.logger.warning(f"Method 3 failed - Error using click+active element: {ios_e3}")

                                # Method 4: Try using mobile: type (older method)
                                try:
                                    self.logger.info(f"Method 4: Using iOS mobile: type for text input: '{text}'")
                                    self.driver.execute_script('mobile: type', {'text': text})
                                    return {
                                        "status": "success",
                                        "message": f"Text input successful using iOS mobile: type: '{text}' into {locator_type}='{locator_value}'"
                                    }
                                except Exception as ios_e4:
                                    self.logger.warning(f"Method 4 failed - Error using iOS mobile: type: {ios_e4}")
                                    # Fall back to standard input below
                else:
                    # For Android or other platforms, use standard input
                    element.send_keys(text)
                    return {
                        "status": "success",
                        "message": f"Text input successful: '{text}' into {locator_type}='{locator_value}'"
                    }

            except TimeoutException:
                return {
                    "status": "error",
                    "message": f"Element not found or not interactable within timeout: {locator_type}='{locator_value}'"
                }
            except Exception as e:
                # Try alternative approach for older Appium versions
                try:
                    # Handle Appium-specific locator types
                    if locator_type.lower() == 'accessibility_id':
                        element = self.driver.find_element_by_accessibility_id(locator_value)
                    elif locator_type.lower() == 'ios_predicate':
                        element = self.driver.find_element_by_ios_predicate(locator_value)
                    elif locator_type.lower() == 'android_uiautomator':
                        element = self.driver.find_element_by_android_uiautomator(locator_value)
                    elif locator_type.lower() == 'ios_class_chain':
                        element = self.driver.find_element_by_ios_class_chain(locator_value)
                    # Standard Selenium locators
                    elif locator_type.lower() == 'id':
                        element = self.driver.find_element_by_id(locator_value)
                    elif locator_type.lower() == 'xpath':
                        element = self.driver.find_element_by_xpath(locator_value)
                    elif locator_type.lower() == 'name':
                        element = self.driver.find_element_by_name(locator_value)
                    elif locator_type.lower() == 'class':
                        element = self.driver.find_element_by_class_name(locator_value)
                    else:
                        return {
                            "status": "error",
                            "message": f"Unsupported locator type: {locator_type}"
                        }

                    # Clear the field if requested
                    if clear_first:
                        try:
                            element.clear()
                        except Exception as clear_e:
                            self.logger.warning(f"Could not clear element before input: {clear_e}")

                    # Input the text
                    element.send_keys(text)

                    return {
                        "status": "success",
                        "message": f"Text input successful: '{text}' into {locator_type}='{locator_value}'"
                    }

                except Exception as inner_e:
                    self.logger.error(f"Error inputting text: {inner_e}")
                    return {
                        "status": "error",
                        "message": f"Failed to input text: {str(inner_e)}"
                    }
        except Exception as e:
            self.logger.error(f"Error in input_text: {e}")
            return {
                "status": "error",
                "message": f"Failed to input text: {str(e)}"
            }

        # If we get here, all previous methods failed
        # Try one more time with Airtest as a last resort
        if self.airtest_device and hasattr(self.airtest_device, 'text'):
            try:
                self.logger.info(f"Trying to input text using Airtest as last resort: '{text}'")
                # For Airtest, we can't use locators directly, so we'll just input text
                # This will work if the field is already focused
                self.airtest_device.text(text)
                return {
                    "status": "success",
                    "message": f"Text input successful using Airtest: '{text}'"
                }
            except Exception as airtest_e:
                self.logger.error(f"Error inputting text with Airtest: {airtest_e}")
                return {
                    "status": "error",
                    "message": f"Failed to input text with Airtest: {str(airtest_e)}"
                }

        # If we get here, all methods failed
        return {
            "status": "error",
            "message": f"Failed to input text '{text}' using all available methods"
        }

    def tap(self, x, y, duration=None):
        """
        Tap at the specified coordinates

        Args:
            x (int): X coordinate
            y (int): Y coordinate
            duration (int, optional): Duration of tap in milliseconds

        Returns:
            dict: Result of the operation with status and message
        """
        self.logger.info(f"Tapping at coordinates: ({x}, {y})")
        self._update_activity_time()

        try:
            # Convert coordinates to integers
            x, y = int(x), int(y)

            # Only use Appium driver for tapping (no Airtest fallback)
            if self.driver:
                # Different implementation based on platform
                if self.platform_name.lower() == 'ios':
                    # For iOS use TouchAction or direct tap
                    try:
                        # Try mobile: tap for iOS
                        self.logger.info(f"Using mobile: tap for iOS at ({x}, {y})")
                        self.driver.execute_script('mobile: tap', {
                            'x': x,
                            'y': y,
                            'duration': duration if duration else 50  # Default duration of 50ms
                        })
                        return {
                            'status': 'success',
                            'message': f'Successfully tapped at ({x}, {y}) using mobile: tap'
                        }
                    except Exception as tap_err:
                        self.logger.warning(f"Error using mobile: tap: {tap_err}")

                        try:
                            # Use TouchAction as fallback
                            self.logger.info(f"Using TouchAction for iOS at ({x}, {y})")
                            actions = TouchAction(self.driver)
                            actions.tap(x=x, y=y).perform()
                            return {
                                'status': 'success',
                                'message': f'Successfully tapped at ({x}, {y}) using TouchAction'
                            }
                        except Exception as touch_err:
                            self.logger.error(f"Error using TouchAction: {touch_err}")

                            # Try W3C Actions API as a last resort for iOS
                            try:
                                self.logger.info(f"Using W3C Actions API for iOS at ({x}, {y})")
                                from selenium.webdriver.common.actions.pointer_input import PointerInput
                                from selenium.webdriver.common.actions.action_builder import ActionBuilder

                                pointer = PointerInput(PointerInput.POINTER_TOUCH, "touch")
                                actions = ActionBuilder(self.driver, mouse=pointer)
                                actions.pointer_action.move_to_location(x, y)
                                actions.pointer_action.click()
                                actions.perform()
                                return {
                                    'status': 'success',
                                    'message': f'Successfully tapped at ({x}, {y}) using W3C Actions API'
                                }
                            except Exception as w3c_err:
                                self.logger.error(f"Error using W3C Actions API: {w3c_err}")
                                raise Exception(f"Failed to tap: {w3c_err}")
                else:
                    # For Android use TouchAction
                    try:
                        self.logger.info(f"Using TouchAction for Android at ({x}, {y})")
                        actions = TouchAction(self.driver)
                        actions.tap(x=x, y=y).perform()
                        return {
                            'status': 'success',
                            'message': f'Successfully tapped at ({x}, {y})'
                        }
                    except Exception as e:
                        self.logger.error(f"Error tapping with TouchAction: {e}")

                        # Try W3C Actions API as a fallback for Android
                        try:
                            self.logger.info(f"Using W3C Actions API for Android at ({x}, {y})")
                            from selenium.webdriver.common.actions.pointer_input import PointerInput
                            from selenium.webdriver.common.actions.action_builder import ActionBuilder

                            pointer = PointerInput(PointerInput.POINTER_TOUCH, "touch")
                            actions = ActionBuilder(self.driver, mouse=pointer)
                            actions.pointer_action.move_to_location(x, y)
                            actions.pointer_action.click()
                            actions.perform()
                            return {
                                'status': 'success',
                                'message': f'Successfully tapped at ({x}, {y}) using W3C Actions API'
                            }
                        except Exception as w3c_err:
                            self.logger.error(f"Error using W3C Actions API: {w3c_err}")
                            raise Exception(f"Failed to tap: {w3c_err}")

            # No viable method found
            raise Exception("No Appium driver available to perform tap action")

        except Exception as e:
            self.logger.error(f"Error in tap: {e}")
            self.logger.error(traceback.format_exc())
            return {
                'status': 'error',
                'message': f'Failed to tap at ({x}, {y}): {str(e)}'
            }

    def tap_element(self, locator_type, locator_value, timeout=15, interval=0.5):
        """
        Tap on an element identified by a locator

        Args:
            locator_type (str): Type of locator (id, xpath, accessibility_id, etc.)
            locator_value (str): Value of the locator
            timeout (int): Maximum time to wait for the element in seconds
            interval (float): Polling interval in seconds

        Returns:
            dict: Result with status and message
        """
        self.logger.info(f"Tapping on element with {locator_type}='{locator_value}' (timeout={timeout}s, interval={interval}s)")

        if not self.driver:
            self.logger.error("No Appium driver available")
            return {"status": "error", "message": "No Appium driver available"}

        try:
            from selenium.webdriver.common.by import By
            from selenium.webdriver.support.ui import WebDriverWait
            from selenium.webdriver.support import expected_conditions as EC
            from selenium.common.exceptions import TimeoutException, NoSuchElementException

            # Map locator type to Selenium/Appium By type
            locator_map = {
                'id': By.ID,
                'xpath': By.XPATH,
                'name': By.NAME,
                'class_name': By.CLASS_NAME,
                'class': By.CLASS_NAME,
                'accessibility_id': 'accessibility id',  # Appium specific
                'ios_predicate': '-ios predicate string',  # iOS specific
                'predicate': '-ios predicate string',  # iOS specific (alias)
                'android_uiautomator': '-android uiautomator',  # Android specific
                'ios_class_chain': '-ios class chain',  # iOS specific
                'class_chain': '-ios class chain'  # iOS specific (alias)
            }

            by_type = locator_map.get(locator_type.lower())
            if not by_type:
                by_type = locator_type  # Use as-is if not in mapping

            # Wait for element to be clickable
            try:
                self.logger.info(f"Waiting for element to be clickable: {locator_type}='{locator_value}'")
                element = WebDriverWait(self.driver, timeout, interval).until(
                    EC.element_to_be_clickable((by_type, locator_value))
                )

                # Tap on the element
                self.logger.info(f"Element found, tapping on it")
                element.click()

                return {
                    "status": "success",
                    "message": f"Tapped on element with {locator_type}='{locator_value}'"
                }
            except TimeoutException:
                self.logger.warning(f"Element not clickable within timeout: {locator_type}='{locator_value}'")

                # Try to find the element even if it's not clickable
                try:
                    self.logger.info(f"Trying to find element even if not clickable: {locator_type}='{locator_value}'")
                    element = WebDriverWait(self.driver, 1).until(
                        EC.presence_of_element_located((by_type, locator_value))
                    )

                    # Try to click it anyway
                    self.logger.info(f"Element found but not clickable, trying to tap anyway")
                    element.click()

                    return {
                        "status": "success",
                        "message": f"Tapped on non-clickable element with {locator_type}='{locator_value}'"
                    }
                except Exception as inner_e:
                    self.logger.warning(f"Could not find or tap on element: {inner_e}")
                    return {
                        "status": "error",
                        "message": f"Element not found or not tappable: {locator_type}='{locator_value}'"
                    }

        except Exception as e:
            self.logger.error(f"Error in tap_element: {e}")
            self.logger.error(traceback.format_exc())
            return {
                "status": "error",
                "message": f"Error tapping element: {str(e)}"
            }

    def tap_on_image(self, image_path, timeout=15, confidence=0.5):
        """
        Tap on an image on the screen

        Args:
            image_path (str): Path to the image to find and tap on
            timeout (int): Maximum time to wait for the image in seconds
            confidence (float): Confidence threshold for image matching (0.0-1.0)

        Returns:
            dict: Result with status and message
        """
        self.logger.info(f"Tapping on image: {image_path} (timeout={timeout}s, confidence={confidence})")

        # Check if we have an Airtest device (preferred for image operations)
        if self.airtest_device:
            try:
                from airtest.core.api import exists, touch
                from airtest.core.cv import Template

                # Try to find the image
                start_time = time.time()
                image_found = False

                while time.time() - start_time < timeout:
                    # Check if image exists
                    pos = exists(Template(image_path, threshold=confidence))
                    if pos:
                        image_found = True
                        break
                    time.sleep(0.5)  # Poll every 500ms

                if image_found and pos:
                    # Tap on the image
                    self.logger.info(f"Image found at {pos}, tapping on it")
                    touch(pos)
                    return {
                        "status": "success",
                        "message": f"Tapped on image: {image_path}"
                    }
                else:
                    self.logger.warning(f"Image not found: {image_path}")
                    return {
                        "status": "error",
                        "message": f"Image not found: {image_path}"
                    }
            except Exception as e:
                self.logger.error(f"Error tapping on image: {e}")
                self.logger.error(traceback.format_exc())
                return {
                    "status": "error",
                    "message": f"Error tapping on image: {str(e)}"
                }
        else:
            self.logger.error("No Airtest device available for image operations")
            return {
                "status": "error",
                "message": "No Airtest device available for image operations"
            }

    def tap_on_text(self, text, timeout=15, fuzzy=False):
        """
        Tap on text found on the screen

        Args:
            text (str): Text to find and tap on
            timeout (int): Maximum time to wait for the text in seconds
            fuzzy (bool): Whether to use fuzzy matching

        Returns:
            dict: Result with status and message
        """
        self.logger.info(f"Tapping on text: '{text}' (timeout={timeout}s, fuzzy={fuzzy})")

        if not self.driver:
            self.logger.error("No Appium driver available")
            return {"status": "error", "message": "No Appium driver available"}

        try:
            # Get screen size for coordinate calculations
            window_size = self.driver.get_window_size()
            screen_width = window_size['width']
            screen_height = window_size['height']

            # Different strategies based on platform
            if self.platform_name.lower() == 'ios':
                # For iOS, use predicate string
                try:
                    # Try exact match first
                    predicate = f'label == "{text}" OR name == "{text}" OR value == "{text}"'
                    if fuzzy:
                        predicate = f'label CONTAINS "{text}" OR name CONTAINS "{text}" OR value CONTAINS "{text}"'

                    self.logger.info(f"Using iOS predicate: {predicate}")

                    # Wait for element to be present
                    from selenium.webdriver.support.ui import WebDriverWait
                    from selenium.webdriver.support import expected_conditions as EC

                    element = WebDriverWait(self.driver, timeout).until(
                        EC.presence_of_element_located(('-ios predicate string', predicate))
                    )

                    # Tap on the element
                    element.click()

                    return {
                        "status": "success",
                        "message": f"Tapped on text: '{text}'"
                    }
                except Exception as ios_e:
                    self.logger.warning(f"Error finding text with iOS predicate: {ios_e}")

                    # Try OCR as fallback
                    try:
                        # Take screenshot
                        screenshot = self.take_screenshot()
                        if not screenshot:
                            raise Exception("Failed to take screenshot for OCR")

                        # Use OCR to find text
                        import pytesseract
                        from PIL import Image
                        import io
                        import base64

                        # Convert base64 to image
                        image_data = base64.b64decode(screenshot.split(',')[1])
                        image = Image.open(io.BytesIO(image_data))

                        # Run OCR
                        ocr_result = pytesseract.image_to_data(image, output_type=pytesseract.Output.DICT)

                        # Find text matches
                        text_found = False
                        for i, word in enumerate(ocr_result['text']):
                            if (text == word) or (fuzzy and text in word):
                                text_found = True
                                # Calculate center of the text
                                x = ocr_result['left'][i] + ocr_result['width'][i] // 2
                                y = ocr_result['top'][i] + ocr_result['height'][i] // 2

                                # Tap on the text
                                self.logger.info(f"Text found with OCR at ({x}, {y}), tapping on it")
                                result = self.tap(x, y)

                                if isinstance(result, dict) and result.get('status') == 'success':
                                    return {
                                        "status": "success",
                                        "message": f"Tapped on text: '{text}' using OCR"
                                    }
                                else:
                                    raise Exception(f"Failed to tap on text coordinates: {result}")

                        if not text_found:
                            self.logger.warning(f"Text not found with OCR: '{text}'")
                            return {
                                "status": "error",
                                "message": f"Text not found: '{text}'"
                            }
                    except Exception as ocr_e:
                        self.logger.error(f"Error using OCR to find text: {ocr_e}")
                        self.logger.error(traceback.format_exc())
                        return {
                            "status": "error",
                            "message": f"Failed to find text with OCR: {str(ocr_e)}"
                        }
            else:
                # For Android, use UiSelector
                try:
                    # Try exact match first
                    selector = f'new UiSelector().text("{text}")'
                    if fuzzy:
                        selector = f'new UiSelector().textContains("{text}")'

                    self.logger.info(f"Using Android UiSelector: {selector}")

                    # Find and tap on the element
                    element = self.driver.find_element('-android uiautomator', selector)
                    element.click()

                    return {
                        "status": "success",
                        "message": f"Tapped on text: '{text}'"
                    }
                except Exception as android_e:
                    self.logger.warning(f"Error finding text with UiSelector: {android_e}")

                    # Try OCR as fallback (same as iOS)
                    try:
                        # Take screenshot
                        screenshot = self.take_screenshot()
                        if not screenshot:
                            raise Exception("Failed to take screenshot for OCR")

                        # Use OCR to find text
                        import pytesseract
                        from PIL import Image
                        import io
                        import base64

                        # Convert base64 to image
                        image_data = base64.b64decode(screenshot.split(',')[1])
                        image = Image.open(io.BytesIO(image_data))

                        # Run OCR
                        ocr_result = pytesseract.image_to_data(image, output_type=pytesseract.Output.DICT)

                        # Find text matches
                        text_found = False
                        for i, word in enumerate(ocr_result['text']):
                            if (text == word) or (fuzzy and text in word):
                                text_found = True
                                # Calculate center of the text
                                x = ocr_result['left'][i] + ocr_result['width'][i] // 2
                                y = ocr_result['top'][i] + ocr_result['height'][i] // 2

                                # Tap on the text
                                self.logger.info(f"Text found with OCR at ({x}, {y}), tapping on it")
                                result = self.tap(x, y)

                                if isinstance(result, dict) and result.get('status') == 'success':
                                    return {
                                        "status": "success",
                                        "message": f"Tapped on text: '{text}' using OCR"
                                    }
                                else:
                                    raise Exception(f"Failed to tap on text coordinates: {result}")

                        if not text_found:
                            self.logger.warning(f"Text not found with OCR: '{text}'")
                            return {
                                "status": "error",
                                "message": f"Text not found: '{text}'"
                            }
                    except Exception as ocr_e:
                        self.logger.error(f"Error using OCR to find text: {ocr_e}")
                        self.logger.error(traceback.format_exc())
                        return {
                            "status": "error",
                            "message": f"Failed to find text with OCR: {str(ocr_e)}"
                        }
        except Exception as e:
            self.logger.error(f"Error in tap_on_text: {e}")
            self.logger.error(traceback.format_exc())
            return {
                "status": "error",
                "message": f"Error tapping on text: {str(e)}"
            }

    def double_tap(self, x, y):
        """
        Perform a double tap at the specified coordinates

        Args:
            x (int): X coordinate
            y (int): Y coordinate

        Returns:
            dict: Result of the operation with status and message
        """
        self.logger.info(f"Double tapping at coordinates: ({x}, {y})")

        try:
            # Convert coordinates to integers
            x, y = int(x), int(y)

            # Check if we have an Airtest device (preferred for iOS)
            if self.airtest_device and hasattr(self.airtest_device, 'double_click'):
                try:
                    self.logger.info(f"Using Airtest device to double tap at ({x}, {y})")
                    self.airtest_device.double_click((x, y))
                    return {
                        'status': 'success',
                        'message': f'Successfully double tapped at ({x}, {y}) using Airtest'
                    }
                except Exception as e:
                    self.logger.error(f"Error using Airtest device to double tap: {e}")
                    # Fall back to Appium method

            # Use Appium driver if available
            if self.driver:
                # Different implementation based on platform
                if self.platform_name.lower() == 'ios':
                    # For iOS use mobile: doubleTap
                    try:
                        self.logger.info(f"Using mobile: doubleTap for iOS at ({x}, {y})")
                        self.driver.execute_script('mobile: doubleTap', {
                            'x': x,
                            'y': y
                        })
                        return {
                            'status': 'success',
                            'message': f'Successfully double tapped at ({x}, {y}) using mobile: doubleTap'
                        }
                    except Exception as double_tap_err:
                        self.logger.warning(f"Error using mobile: doubleTap: {double_tap_err}")

                        # Fall back to two separate taps
                        try:
                            self.logger.info(f"Using two separate taps for iOS at ({x}, {y})")
                            # Tap twice with a short delay
                            result1 = self.tap(x, y)
                            time.sleep(0.1)  # 100ms delay between taps
                            result2 = self.tap(x, y)

                            if result1['status'] == 'success' and result2['status'] == 'success':
                                return {
                                    'status': 'success',
                                    'message': f'Successfully double tapped at ({x}, {y}) using two separate taps'
                                }
                            else:
                                raise Exception("Failed to complete both taps")
                        except Exception as separate_taps_err:
                            self.logger.error(f"Error using separate taps: {separate_taps_err}")
                            raise Exception(f"Failed to double tap: {separate_taps_err}")
                else:
                    # For Android use TouchAction with double tap or two separate taps
                    try:
                        self.logger.info(f"Using separate taps for Android at ({x}, {y})")
                        # Tap twice with a short delay
                        result1 = self.tap(x, y)
                        time.sleep(0.1)  # 100ms delay between taps
                        result2 = self.tap(x, y)

                        if result1['status'] == 'success' and result2['status'] == 'success':
                            return {
                                'status': 'success',
                                'message': f'Successfully double tapped at ({x}, {y}) using two separate taps'
                            }
                        else:
                            raise Exception("Failed to complete both taps")
                    except Exception as e:
                        self.logger.error(f"Error double tapping: {e}")
                        raise Exception(f"Failed to double tap: {e}")

            # No viable method found
            raise Exception("No driver or device available to perform double tap action")

        except Exception as e:
            self.logger.error(f"Error in double_tap: {e}")
            self.logger.error(traceback.format_exc())
            return {
                'status': 'error',
                'message': f'Failed to double tap at ({x}, {y}): {str(e)}'
            }

    def swipe(self, start_x, start_y, end_x, end_y, duration=300):
        """Performs a swipe gesture on the screen.

        Args:
            start_x (int): Starting X coordinate.
            start_y (int): Starting Y coordinate.
            end_x (int): Ending X coordinate.
            end_y (int): Ending Y coordinate.
            duration (int): Duration of the swipe in milliseconds.

        Returns:
            dict: Result dictionary with 'status' and 'message'.
        """
        if not self.driver:
            return {"status": "error", "message": "Driver not available"}
        try:
            self.driver.swipe(int(start_x), int(start_y), int(end_x), int(end_y), int(duration))
            self.logger.info(f"Swiped from ({start_x},{start_y}) to ({end_x},{end_y})")
            return {"status": "success", "message": f"Swiped from ({start_x},{start_y}) to ({end_x},{end_y})"}
        except Exception as e:
            self.logger.error(f"Swipe failed: {e}")
            return {"status": "error", "message": f"Swipe failed: {str(e)}"}


    def long_press(self, x, y, duration=1000):
        """
        Perform a long press at the specified coordinates

        Args:
            x (int): X coordinate
            y (int): Y coordinate
            duration (int, optional): Duration of press in milliseconds, default 1000

        Returns:
            dict: Result of the operation with status and message
        """
        self.logger.info(f"Long pressing at coordinates: ({x}, {y}) for {duration}ms")

        try:
            # Convert coordinates to integers
            x, y = int(x), int(y)
            duration = int(duration)

            # Check if we have an Airtest device
            if self.airtest_device and hasattr(self.airtest_device, 'long_click'):
                try:
                    self.logger.info(f"Using Airtest device to long press at ({x}, {y})")
                    # Airtest uses seconds instead of milliseconds
                    airtest_duration = duration / 1000.0
                    self.airtest_device.long_click((x, y), duration=airtest_duration)
                    return {
                        'status': 'success',
                        'message': f'Successfully long pressed at ({x}, {y}) using Airtest'
                    }
                except Exception as e:
                    self.logger.error(f"Error using Airtest device to long press: {e}")
                    # Fall back to Appium method

            # Use Appium driver if available
            if self.driver:
                # Different implementation based on platform
                if self.platform_name.lower() == 'ios':
                    # For iOS use mobile: touchAndHold
                    try:
                        self.logger.info(f"Using mobile: touchAndHold for iOS at ({x}, {y})")
                        self.driver.execute_script('mobile: touchAndHold', {
                            'x': x,
                            'y': y,
                            'duration': duration / 1000.0  # Convert ms to seconds for iOS
                        })
                        return {
                            'status': 'success',
                            'message': f'Successfully long pressed at ({x}, {y}) using mobile: touchAndHold'
                        }
                    except Exception as touch_hold_err:
                        self.logger.warning(f"Error using mobile: touchAndHold: {touch_hold_err}")

                        # Try TouchAction as fallback
                        try:
                            self.logger.info(f"Using TouchAction for iOS long press at ({x}, {y})")
                            actions = TouchAction(self.driver)
                            actions.long_press(x=x, y=y, duration=duration).release().perform()
                            return {
                                'status': 'success',
                                'message': f'Successfully long pressed at ({x}, {y}) using TouchAction'
                            }
                        except Exception as touch_err:
                            self.logger.error(f"Error using TouchAction for long press: {touch_err}")
                            raise Exception(f"Failed to long press: {touch_err}")
                else:
                    # For Android use TouchAction
                    try:
                        self.logger.info(f"Using TouchAction for Android long press at ({x}, {y})")
                        actions = TouchAction(self.driver)
                        actions.long_press(x=x, y=y, duration=duration).release().perform()
                        return {
                            'status': 'success',
                            'message': f'Successfully long pressed at ({x}, {y}) using TouchAction'
                        }
                    except Exception as e:
                        self.logger.error(f"Error long pressing with TouchAction: {e}")
                        raise Exception(f"Failed to long press: {e}")

            # No viable method found
            raise Exception("No driver or device available to perform long press action")

        except Exception as e:
            self.logger.error(f"Error in long_press: {e}")
            self.logger.error(traceback.format_exc())
            return {
                'status': 'error',
                'message': f'Failed to long press at ({x}, {y}): {str(e)}'
            }

    def press_button(self, button_name):
        """
        Press a device button like HOME, VOLUME_UP, etc.

        Args:
            button_name (str): Name of the button to press (home, volumeup, volumedown, power)

        Returns:
            dict: Result of the operation with status and message
        """
        self.logger.info(f"Pressing button: {button_name}")

        try:
            button_name = button_name.lower()

            # Check if we have an Airtest device
            if self.airtest_device:
                try:
                    if button_name == 'home' and hasattr(self.airtest_device, 'home'):
                        self.logger.info("Using Airtest device to press home button")
                        self.airtest_device.home()
                        return {
                            'status': 'success',
                            'message': 'Successfully pressed home button using Airtest'
                        }
                    # For other buttons, we'll fall back to Appium
                except Exception as e:
                    self.logger.error(f"Error using Airtest device to press button: {e}")

            # Use Appium driver if available
            if self.driver:
                if self.platform_name.lower() == 'ios':
                    # For iOS use custom commands
                    try:
                        if button_name == 'home':
                            self.logger.info("Using XCUITest homescreen command for iOS")
                            self.driver.execute_script('mobile: pressButton', {'name': 'home'})
                            return {
                                'status': 'success',
                                'message': 'Successfully pressed home button'
                            }
                        elif button_name == 'volumeup':
                            self.driver.execute_script('mobile: pressButton', {'name': 'volumeUp'})
                            return {
                                'status': 'success',
                                'message': 'Successfully pressed volume up button'
                            }
                        elif button_name == 'volumedown':
                            self.driver.execute_script('mobile: pressButton', {'name': 'volumeDown'})
                            return {
                                'status': 'success',
                                'message': 'Successfully pressed volume down button'
                            }
                        else:
                            raise Exception(f"Unsupported button: {button_name}")
                    except Exception as e:
                        self.logger.error(f"Error pressing button on iOS: {e}")
                        raise Exception(f"Failed to press button: {e}")
                else:
                    # For Android use keyevent
                    try:
                        android_keycode = None
                        if button_name == 'home':
                            android_keycode = 3  # KEYCODE_HOME
                        elif button_name == 'back':
                            android_keycode = 4  # KEYCODE_BACK
                        elif button_name == 'volumeup':
                            android_keycode = 24  # KEYCODE_VOLUME_UP
                        elif button_name == 'volumedown':
                            android_keycode = 25  # KEYCODE_VOLUME_DOWN
                        elif button_name == 'power':
                            android_keycode = 26  # KEYCODE_POWER

                        if android_keycode is not None:
                            self.logger.info(f"Using Android keyevent: {android_keycode}")
                            self.driver.press_keycode(android_keycode)
                            return {
                                'status': 'success',
                                'message': f'Successfully pressed {button_name} button'
                            }
                        else:
                            raise Exception(f"Unsupported button: {button_name}")
                    except Exception as e:
                        self.logger.error(f"Error pressing button on Android: {e}")
                        raise Exception(f"Failed to press button: {e}")

            # No viable method found
            raise Exception("No driver or device available to perform button press")

        except Exception as e:
            self.logger.error(f"Error in press_button: {e}")
            self.logger.error(traceback.format_exc())
            return {
                'status': 'error',
                'message': f'Failed to press {button_name} button: {str(e)}'
            }

    def launch_app(self, bundle_id=None):
        """
        Launch an app on the device

        Args:
            bundle_id (str, optional): Bundle ID for iOS or package name for Android
                                      If None, uses the one specified in capabilities

        Returns:
            dict: Result of the operation with status and message
        """
        try:
            # Use the provided bundle_id or get from capabilities
            app_id = bundle_id
            if not app_id:
                if self.platform_name.lower() == 'ios':
                    app_id = self.desired_capabilities.get('bundleId')
                else:
                    app_id = self.desired_capabilities.get('appPackage')

            if not app_id:
                return {
                    'status': 'error',
                    'message': 'No bundle ID or package name provided or found in capabilities'
                }

            self.logger.info(f"Launching app: {app_id}")

            # Check if we have an Airtest device
            if self.airtest_device and hasattr(self.airtest_device, 'start_app'):
                try:
                    self.logger.info(f"Using Airtest device to launch app: {app_id}")
                    self.airtest_device.start_app(app_id)
                    return {
                        'status': 'success',
                        'message': f'Successfully launched app {app_id} using Airtest'
                    }
                except Exception as e:
                    self.logger.error(f"Error using Airtest device to launch app: {e}")
                    # Fall back to Appium method

            # Use Appium driver if available
            if self.driver:
                if self.platform_name.lower() == 'ios':
                    # For iOS
                    try:
                        self.logger.info(f"Using XCUITest to launch iOS app: {app_id}")
                        self.driver.execute_script('mobile: launchApp', {'bundleId': app_id})
                        return {
                            'status': 'success',
                            'message': f'Successfully launched app {app_id}'
                        }
                    except Exception as e:
                        self.logger.error(f"Error launching iOS app: {e}")
                        raise Exception(f"Failed to launch app: {e}")
                else:
                    # For Android
                    try:
                        self.logger.info(f"Using Appium to launch Android app: {app_id}")
                        self.driver.activate_app(app_id)
                        return {
                            'status': 'success',
                            'message': f'Successfully launched app {app_id}'
                        }
                    except Exception as e:
                        self.logger.error(f"Error launching Android app: {e}")
                        raise Exception(f"Failed to launch app: {e}")

            # No viable method found
            raise Exception("No driver or device available to launch app")

        except Exception as e:
            self.logger.error(f"Error in launch_app: {e}")
            self.logger.error(traceback.format_exc())
            return {
                'status': 'error',
                'message': f'Failed to launch app: {str(e)}'
            }

    def terminate_app(self, bundle_id=None):
        """
        Terminate an app on the device

        Args:
            bundle_id (str, optional): Bundle ID for iOS or package name for Android
                                      If None, uses the one specified in capabilities

        Returns:
            dict: Result of the operation with status and message
        """
        try:
            # Use the provided bundle_id or get from capabilities
            app_id = bundle_id
            if not app_id:
                if self.platform_name.lower() == 'ios':
                    app_id = self.desired_capabilities.get('bundleId')
                else:
                    app_id = self.desired_capabilities.get('appPackage')

            if not app_id:
                return {
                    'status': 'error',
                    'message': 'No bundle ID or package name provided or found in capabilities'
                }

            self.logger.info(f"Terminating app: {app_id}")

            # Check if we have an Airtest device
            if self.airtest_device and hasattr(self.airtest_device, 'stop_app'):
                try:
                    self.logger.info(f"Using Airtest device to terminate app: {app_id}")
                    self.airtest_device.stop_app(app_id)
                    return {
                        'status': 'success',
                        'message': f'Successfully terminated app {app_id} using Airtest'
                    }
                except Exception as e:
                    self.logger.error(f"Error using Airtest device to terminate app: {e}")
                    # Fall back to Appium method

            # Use Appium driver if available
            if self.driver:
                if self.platform_name.lower() == 'ios':
                    # For iOS
                    try:
                        self.logger.info(f"Using XCUITest to terminate iOS app: {app_id}")
                        self.driver.execute_script('mobile: terminateApp', {'bundleId': app_id})
                        return {
                            'status': 'success',
                            'message': f'Successfully terminated app {app_id}'
                        }
                    except Exception as e:
                        self.logger.error(f"Error terminating iOS app: {e}")
                        raise Exception(f"Failed to terminate app: {e}")
                else:
                    # For Android
                    try:
                        self.logger.info(f"Using Appium to terminate Android app: {app_id}")
                        self.driver.terminate_app(app_id)
                        return {
                            'status': 'success',
                            'message': f'Successfully terminated app {app_id}'
                        }
                    except Exception as e:
                        self.logger.error(f"Error terminating Android app: {e}")
                        raise Exception(f"Failed to terminate app: {e}")

            # No viable method found
            raise Exception("No driver or device available to terminate app")

        except Exception as e:
            self.logger.error(f"Error in terminate_app: {e}")
            self.logger.error(traceback.format_exc())
            return {
                'status': 'error',
                'message': f'Failed to terminate app: {str(e)}'
            }

    def uninstall_app(self, package_id):
        """
        Uninstall an app from the device

        Args:
            package_id (str): Bundle ID for iOS or package name for Android

        Returns:
            bool or dict: True if successful, False if failed, or dict with status info
        """
        try:
            if not package_id:
                self.logger.error("No package ID provided for uninstall")
                return False

            self.logger.info(f"Uninstalling app: {package_id}")

            # Check if we have an Airtest device
            if self.airtest_device:
                try:
                    # For Android, we can use shell command
                    if self.platform_name.lower() == 'android':
                        self.logger.info(f"Using Airtest device to uninstall Android app: {package_id}")
                        result = self.airtest_device.shell(f"pm uninstall {package_id}")
                        if "Success" in result:
                            return True
                    # For iOS, use the Airtest iOS uninstall_app method
                    elif self.platform_name.lower() == 'ios':
                        self.logger.info(f"Using Airtest iOS to uninstall app: {package_id}")
                        try:
                            # Check if the airtest_device is an iOS device with uninstall_app method
                            if hasattr(self.airtest_device, 'uninstall_app'):
                                self.airtest_device.uninstall_app(package_id)
                                self.logger.info(f"Successfully uninstalled iOS app: {package_id} using Airtest")
                                return True
                            else:
                                self.logger.warning(f"Airtest device does not have uninstall_app method. Device type: {type(self.airtest_device)}")
                                # Try to import and use the iOS class directly
                                try:
                                    from airtest.core.ios.ios import IOS
                                    if isinstance(self.airtest_device, IOS):
                                        self.logger.info(f"Using Airtest IOS class to uninstall app: {package_id}")
                                        self.airtest_device.uninstall_app(package_id)
                                        return True
                                except Exception as ios_err:
                                    self.logger.error(f"Error using Airtest IOS class: {ios_err}")
                        except Exception as ios_uninstall_err:
                            self.logger.error(f"Error using Airtest iOS uninstall_app: {ios_uninstall_err}")
                except Exception as e:
                    self.logger.error(f"Error using Airtest device to uninstall app: {e}")
                    # Fall back to Appium method

            # Use Appium driver if available
            if self.driver:
                if self.platform_name.lower() == 'ios':
                    # For iOS
                    try:
                        self.logger.info(f"Using XCUITest to uninstall iOS app: {package_id}")
                        try:
                            # Try the standard Appium method first
                            self.driver.remove_app(package_id)
                            return True
                        except Exception as e:
                            if "Method has not yet been implemented" in str(e):
                                # If the method is not implemented, try using direct Airtest methods
                                self.logger.info("remove_app not implemented, trying direct Airtest methods")
                                direct_result = self._uninstall_app_ios_direct(package_id)
                                if direct_result:
                                    self.logger.info(f"Successfully uninstalled iOS app {package_id} using direct Airtest methods")
                                    return {
                                        "status": "success",
                                        "message": f"iOS app {package_id} uninstalled using Airtest"
                                    }

                                # If direct method fails, try using a workaround with mobile: commands
                                self.logger.info("Direct Airtest method failed, trying mobile: command workaround")
                                try:
                                    # Try using the mobile: terminateApp command (this doesn't uninstall but at least terminates)
                                    self.driver.execute_script('mobile: terminateApp', {'bundleId': package_id})
                                    self.logger.warning(f"Could not uninstall iOS app {package_id}, but terminated it instead")
                                    return {
                                        "status": "warning",
                                        "message": f"iOS app {package_id} could not be uninstalled, but was terminated"
                                    }
                                except Exception as term_err:
                                    self.logger.error(f"Failed to terminate iOS app: {term_err}")
                            raise Exception(f"Failed to uninstall iOS app: {e}")
                    except Exception as e:
                        self.logger.error(f"Error uninstalling iOS app: {e}")
                        return {
                            "status": "error",
                            "message": f"Failed to uninstall iOS app: {str(e)}"
                        }
                else:
                    # For Android
                    try:
                        self.logger.info(f"Using Appium to uninstall Android app: {package_id}")
                        self.driver.remove_app(package_id)
                        return True
                    except Exception as e:
                        self.logger.error(f"Error uninstalling Android app: {e}")
                        return {
                            "status": "error",
                            "message": f"Failed to uninstall Android app: {str(e)}"
                        }

            # No viable method found
            return {
                "status": "error",
                "message": "No driver or device available to uninstall app"
            }

        except Exception as e:
            self.logger.error(f"Error in uninstall_app: {e}")
            self.logger.error(traceback.format_exc())
            return {
                "status": "error",
                "message": f"Error in uninstall_app: {str(e)}"
            }

    def _uninstall_app_ios_direct(self, bundle_id):
        """
        Attempt to uninstall an iOS app using direct Airtest methods

        Args:
            bundle_id (str): The bundle ID of the app to uninstall

        Returns:
            bool: True if successful, False otherwise
        """
        try:
            self.logger.info(f"Attempting direct iOS uninstall for bundle ID: {bundle_id}")

            # Try to import the iOS class directly
            try:
                from airtest.core.ios.ios import IOS, TIDevice

                # If we don't have an Airtest device yet, try to create one
                if not self.airtest_device and self.device_id:
                    try:
                        # Try to create a direct connection to the iOS device
                        self.logger.info(f"Creating direct iOS connection for device: {self.device_id}")
                        ios_device = IOS(self.device_id)

                        # Try to uninstall the app
                        self.logger.info(f"Using direct IOS instance to uninstall: {bundle_id}")
                        ios_device.uninstall_app(bundle_id)
                        self.logger.info(f"Successfully uninstalled app {bundle_id} using direct IOS instance")
                        return True
                    except Exception as direct_err:
                        self.logger.error(f"Error creating direct iOS connection: {direct_err}")

                        # Try using TIDevice directly if available
                        try:
                            self.logger.info(f"Attempting to use TIDevice directly for device: {self.device_id}")
                            ti_device = TIDevice(self.device_id)
                            ti_device.uninstall_app(bundle_id)
                            self.logger.info(f"Successfully uninstalled app {bundle_id} using TIDevice")
                            return True
                        except Exception as ti_err:
                            self.logger.error(f"Error using TIDevice: {ti_err}")
            except ImportError as import_err:
                self.logger.error(f"Could not import iOS classes: {import_err}")

            return False
        except Exception as e:
            self.logger.error(f"Error in _uninstall_app_ios_direct: {e}")
            return False

    @property
    def session_id(self):
        """Get the current Appium session ID."""
        if self.driver and hasattr(self.driver, 'session_id'):
            return self.driver.session_id
        return None

    def set_clipboard(self, content):
        """Set the content of the device's clipboard (iOS only)

        Args:
            content (str): The text content to set in the clipboard

        Returns:
            dict: Result with status and message
        """
        try:
            if not self._ensure_airtest_connected():
                return {"status": "error", "message": "Airtest connection not available"}

            if not hasattr(self.airtest_device, 'set_clipboard'):
                return {"status": "error", "message": "set_clipboard not supported on this device"}

            self.logger.info(f"Setting clipboard content: '{content}'")

            # Call Airtest's set_clipboard method
            self.airtest_device.set_clipboard(content)

            self.logger.info("Clipboard content set successfully")
            return {"status": "success", "message": "Clipboard content set successfully"}

        except Exception as e:
            self.logger.error(f"Error setting clipboard content: {str(e)}")
            self.logger.error(traceback.format_exc())
            return {"status": "error", "message": f"Failed to set clipboard: {str(e)}"}

    def paste_clipboard(self):
        """Paste the current clipboard content at the current cursor position (iOS only)

        This function simulates a paste action using keyboard shortcuts.

        Returns:
            dict: Result with status and message
        """
        try:
            if not self._ensure_airtest_connected():
                return {"status": "error", "message": "Airtest connection not available"}

            self.logger.info("Pasting clipboard content")

            # For iOS, we can use the built-in Appium keyboard shortcuts
            if hasattr(self.driver, 'execute_script'):
                # Try using mobile:pressButton command for paste
                try:
                    self.logger.info("Attempting to paste using mobile:pressButton")
                    self.driver.execute_script('mobile:pressButton', {'name': 'paste'})
                    self.logger.info("Paste command executed successfully")
                    return {"status": "success", "message": "Clipboard content pasted successfully"}
                except Exception as e1:
                    self.logger.warning(f"Could not paste using mobile:pressButton: {str(e1)}")

                    # Fallback: Try simulate CMD+V (iOS keyboard shortcut for paste)
                    try:
                        self.logger.info("Attempting to paste using key combination")
                        # Using the Actions API to press command+v
                        actions = ActionChains(self.driver)
                        actions.key_down(Keys.COMMAND).send_keys('v').key_up(Keys.COMMAND).perform()
                        self.logger.info("Paste key combination executed successfully")
                        return {"status": "success", "message": "Clipboard content pasted successfully"}
                    except Exception as e2:
                        self.logger.error(f"Could not paste using key combination: {str(e2)}")

            # If we're here, both paste methods failed
            self.logger.error("No valid method found to paste clipboard content")
            return {"status": "error", "message": "Could not paste clipboard content, no valid method available"}

        except Exception as e:
            self.logger.error(f"Error pasting clipboard content: {str(e)}")
            self.logger.error(traceback.format_exc())
            return {"status": "error", "message": f"Failed to paste clipboard content: {str(e)}"}

    def capture_image_area(self, selection_x, selection_y, selection_width, selection_height,
                          displayed_width, displayed_height, natural_width, natural_height,
                          image_name, save_debug=True):
        """Crops the latest screenshot based on scaled coordinates and saves it."""
        try:
            # Define reference images directory (ensure it exists)
            # Using relative path from this file's location might be more robust
            base_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
            reference_images_dir = os.path.join(base_dir, 'static', 'reference_images')
            os.makedirs(reference_images_dir, exist_ok=True)
            self.logger.info(f"Reference images directory: {reference_images_dir}")

            # Path to the latest full screenshot used for display
            latest_screenshot_path = os.path.join(base_dir, "app", "static", "screenshots", "latest.png")
            self.logger.info(f"Using latest screenshot: {latest_screenshot_path}")

            if not os.path.exists(latest_screenshot_path):
                self.logger.error(f"Latest screenshot not found at {latest_screenshot_path}")
                # Attempt to take a fresh screenshot as a fallback
                screenshot_result = self.take_screenshot()
                if screenshot_result['status'] != 'success':
                     return {"status": "error", "error": "Latest screenshot not found and failed to take a new one"}
                latest_screenshot_path = screenshot_result['path']
                self.logger.warning(f"Latest screenshot not found, took a new one: {latest_screenshot_path}")
                # We might need to re-fetch natural dimensions if we took a new one, but proceed for now


            # Calculate scaling factors
            # Handle potential zero division if dimensions are invalid (should be caught by API)
            if displayed_width <= 0 or displayed_height <= 0:
                self.logger.error("Invalid displayed dimensions for scaling.")
                return {"status": "error", "error": "Invalid displayed dimensions for scaling"}

            scale_x = natural_width / displayed_width
            scale_y = natural_height / displayed_height
            self.logger.info(f"Calculated scaling factors: scale_x={scale_x:.4f}, scale_y={scale_y:.4f}")

            # Calculate crop coordinates relative to the natural image size
            crop_x = selection_x * scale_x
            crop_y = selection_y * scale_y
            crop_width = selection_width * scale_x
            crop_height = selection_height * scale_y
            self.logger.info(f"Calculated crop area (natural coords): x={crop_x:.2f}, y={crop_y:.2f}, w={crop_width:.2f}, h={crop_height:.2f}")

            # Open the original full-resolution screenshot
            with PIL.Image.open(latest_screenshot_path) as img:
                img_width, img_height = img.size
                self.logger.info(f"Actual screenshot dimensions: {img_width}x{img_height}")

                # --- Coordinate Validation and Adjustment --- Start
                # Ensure coordinates are integers for cropping
                x1 = int(round(crop_x))
                y1 = int(round(crop_y))
                w = int(round(crop_width))
                h = int(round(crop_height))

                # Validate starting coordinates
                if x1 < 0 or y1 < 0 or x1 >= img_width or y1 >= img_height:
                    self.logger.warning(f"Calculated start coordinates ({x1}, {y1}) outside image bounds ({img_width}x{img_height}). Clamping.")
                    x1 = max(0, min(x1, img_width - 1))
                    y1 = max(0, min(y1, img_height - 1))
                    self.logger.info(f"Clamped start coordinates to ({x1}, {y1})")

                # Validate dimensions
                if w <= 0 or h <= 0:
                    self.logger.error(f"Calculated invalid dimensions: w={w}, h={h}. Setting to 1x1.")
                    w = max(1, w)
                    h = max(1, h)

                # Calculate bottom-right coordinates
                x2 = x1 + w
                y2 = y1 + h

                # Ensure crop area doesn't exceed image boundaries
                if x2 > img_width:
                    self.logger.warning(f"Calculated width ({w}) exceeds image width ({img_width}) at x={x1}. Adjusting.")
                    x2 = img_width
                    w = x2 - x1 # Recalculate width
                if y2 > img_height:
                     self.logger.warning(f"Calculated height ({h}) exceeds image height ({img_height}) at y={y1}. Adjusting.")
                     y2 = img_height
                     h = y2 - y1 # Recalculate height

                # Ensure dimensions are still positive after clamping
                w = max(1, w)
                h = max(1, h)

                # Final validated crop box
                crop_box = (x1, y1, x1 + w, y1 + h)
                self.logger.info(f"Final validated crop box (int): {crop_box}")
                # --- Coordinate Validation and Adjustment --- End

                # Save debug image with rectangle BEFORE cropping
                if save_debug:
                    try:
                        debug_img = img.convert("RGBA").copy()
                        draw = PIL.ImageDraw.Draw(debug_img)
                        # Draw validated rectangle in red
                        draw.rectangle(crop_box, outline='red', width=3)
                        # Draw calculated (float) rectangle in blue for comparison
                        draw.rectangle((crop_x, crop_y, crop_x + crop_width, crop_y + crop_height), outline='blue', width=1)

                        # Add text info
                        font = None # Use default font
                        draw.text((10, 10), f"Crop Box: {crop_box}", fill='red', font=font)
                        draw.text((10, 30), f"Scale: {scale_x:.2f}x, {scale_y:.2f}y", fill='blue', font=font)
                        draw.text((10, 50), f"Sel: {selection_x:.1f},{selection_y:.1f} {selection_width:.1f}x{selection_height:.1f}", fill='blue', font=font)

                        debug_dir = os.path.join(base_dir, "debug_images")
                        os.makedirs(debug_dir, exist_ok=True)
                        debug_path = os.path.join(debug_dir, f"debug_capture_{image_name}")
                        debug_img.save(debug_path)
                        self.logger.info(f"Saved debug image with selection rectangle to {debug_path}")
                    except Exception as dbg_err:
                        self.logger.error(f"Failed to save debug image: {dbg_err}")

                # Crop the image
                cropped_img = img.crop(crop_box)
                self.logger.info(f"Cropped image size: {cropped_img.size}")

                # Save the final cropped image
                output_path = os.path.join(reference_images_dir, image_name)
                cropped_img.save(output_path)
                self.logger.info(f"Saved final cropped image to: {output_path}")

                # Convert cropped image to base64 for preview
                buffered = io.BytesIO()
                cropped_img.save(buffered, format="PNG")
                img_str = base64.b64encode(buffered.getvalue()).decode('utf-8')

                return {
                    "status": "success",
                    "message": f"Image area captured and saved as {image_name}",
                    "image": f"data:image/png;base64,{img_str}",
                    "filename": image_name, # Return filename for dropdown selection
                    "path": output_path
                }

        except FileNotFoundError:
             self.logger.error(f"Screenshot file not found at: {latest_screenshot_path}")
             return {"status": "error", "error": "Screenshot file not found"}
        except Exception as e:
            self.logger.error(f"Error in capture_image_area: {str(e)}")
            self.logger.error(traceback.format_exc())
            return {"status": "error", "error": f"Failed to capture image area: {str(e)}"}

    def find_element_with_fallback(self, locators, timeout=10):
        """
        Find an element using multiple locators with fallback

        Args:
            locators (list): List of dictionaries containing locator_type and locator_value pairs
                             Example: [{'locator_type': 'id', 'locator_value': 'my_id'},
                                      {'locator_type': 'xpath', 'locator_value': '//button[@text="Submit"]'}]
            timeout (int): Maximum time to wait for the element in seconds (total timeout will be divided among locators)

        Returns:
            WebElement or None: The found element or None if not found
        """
        if not self.driver:
            self.logger.error("No driver available for find_element_with_fallback")
            return None

        if not locators or not isinstance(locators, list) or len(locators) == 0:
            self.logger.error("No valid locators provided for find_element_with_fallback")
            return None

        # Calculate timeout per locator (minimum 1 second per locator)
        timeout_per_locator = max(1, int(timeout / len(locators)))
        self.logger.info(f"Using {len(locators)} locators with {timeout_per_locator}s timeout per locator")

        # Try each locator in sequence
        for i, locator_info in enumerate(locators):
            locator_type = locator_info.get('locator_type')
            locator_value = locator_info.get('locator_value')

            if not locator_type or not locator_value:
                self.logger.warning(f"Skipping invalid locator at index {i}: missing locator_type or locator_value")
                continue

            self.logger.info(f"Trying locator {i+1}/{len(locators)}: {locator_type}='{locator_value}'")

            # Try to find element with this locator
            element = self.find_element(locator_type, locator_value, timeout=timeout_per_locator)

            if element:
                self.logger.info(f"Element found with locator {i+1}/{len(locators)}: {locator_type}='{locator_value}'")
                return element

            self.logger.info(f"Locator {i+1}/{len(locators)} failed, trying next locator if available")

        self.logger.warning(f"Element not found with any of the {len(locators)} provided locators")
        return None

    def find_element(self, locator_type, locator_value, timeout=10):
        """
        Find an element based on the provided locator type and value

        Args:
            locator_type (str): The type of locator (id, xpath, accessibility_id, etc.)
            locator_value (str): The value to search for
            timeout (int): Maximum time to wait for the element in seconds

        Returns:
            WebElement or None: The found element or None if not found
        """
        if not self.driver:
            self.logger.error("No driver available for find_element")
            return None

        try:
            from selenium.webdriver.support.ui import WebDriverWait
            from selenium.webdriver.support import expected_conditions as EC
            from selenium.common.exceptions import TimeoutException, NoSuchElementException
            from appium.webdriver.common.appiumby import AppiumBy

            # Map locator type to Appium locator strategy
            locator_map = {
                'id': AppiumBy.ID,
                'xpath': AppiumBy.XPATH,
                'accessibility_id': AppiumBy.ACCESSIBILITY_ID,
                'name': AppiumBy.NAME,
                'class': AppiumBy.CLASS_NAME,
                'text': None  # Special case, handled separately
            }

            # Handle special case for text locator
            if locator_type.lower() == 'text':
                # For iOS
                if self.platform_name and self.platform_name.lower() == 'ios':
                    locator = (AppiumBy.XPATH, f"//*[@name='{locator_value}' or @label='{locator_value}' or @value='{locator_value}']")
                # For Android
                else:
                    locator = (AppiumBy.XPATH, f"//*[@text='{locator_value}' or contains(@content-desc, '{locator_value}')]")
            else:
                # Get the appropriate locator strategy
                strategy = locator_map.get(locator_type.lower())
                if not strategy:
                    self.logger.error(f"Unsupported locator type: {locator_type}")
                    return None

                locator = (strategy, locator_value)

            # Wait for the element to be present
            self.logger.info(f"Finding element with {locator_type}: {locator_value}, timeout={timeout}s")
            element = WebDriverWait(self.driver, timeout).until(
                EC.presence_of_element_located(locator)
            )

            return element
        except TimeoutException:
            self.logger.info(f"Element with {locator_type}: {locator_value} not found within timeout of {timeout} seconds")
            return None
        except NoSuchElementException:
            self.logger.info(f"Element with {locator_type}: {locator_value} not found")
            return None
        except Exception as e:
            self.logger.error(f"Error finding element with {locator_type}: {locator_value}: {e}")
            return None

    def find_image(self, image_path, threshold=0.8, timeout=10):
        """
        Find an image on the screen using template matching

        Args:
            image_path (str): Path to the image to find
            threshold (float): Matching threshold (0.0-1.0)
            timeout (int): Maximum time to wait for the image in seconds

        Returns:
            tuple or None: (x, y) coordinates of the center of the found image, or None if not found
        """
        if not AIRTEST_AVAILABLE:
            self.logger.error("Airtest not available for image recognition")
            return None

        try:
            # Ensure Airtest connection is active
            if not self.airtest_device:
                if not self._ensure_airtest_connected():
                    self.logger.error("Could not connect to device with Airtest for image recognition")
                    return None

            # Import required Airtest functions
            from airtest.core.api import Template, exists

            # Log the parameters for debugging
            self.logger.info(f"Finding image: path={image_path}, threshold={threshold}, timeout={timeout}")

            # Verify image path exists and resolve it if needed
            resolved_path = None

            # Check if the path is already absolute and exists
            if os.path.isabs(image_path) and os.path.exists(image_path):
                resolved_path = image_path
                self.logger.info(f"Using absolute image path: {resolved_path}")
            else:
                # Try to resolve from reference_images directory
                try:
                    from config import DIRECTORIES
                    reference_dir = DIRECTORIES.get('REFERENCE_IMAGES', '')
                    if reference_dir:
                        # Try with the full path
                        full_path = os.path.join(reference_dir, image_path)
                        if os.path.exists(full_path):
                            resolved_path = full_path
                            self.logger.info(f"Resolved image path (full): {resolved_path}")
                        else:
                            # Try with just the basename
                            basename_path = os.path.join(reference_dir, os.path.basename(image_path))
                            if os.path.exists(basename_path):
                                resolved_path = basename_path
                                self.logger.info(f"Resolved image path (basename): {resolved_path}")
                except (ImportError, Exception) as e:
                    self.logger.warning(f"Could not resolve reference image from config: {e}")

            # If still not resolved, try some common locations
            if not resolved_path:
                # Try current directory
                if os.path.exists(image_path):
                    resolved_path = image_path
                    self.logger.info(f"Found image in current directory: {resolved_path}")
                # Try reference_images in the project root
                elif os.path.exists(os.path.join('reference_images', os.path.basename(image_path))):
                    resolved_path = os.path.join('reference_images', os.path.basename(image_path))
                    self.logger.info(f"Found image in project reference_images: {resolved_path}")

            # If still doesn't exist, return None
            if not resolved_path or not os.path.exists(resolved_path):
                self.logger.error(f"Image file not found after all resolution attempts: {image_path}")
                return None

            # Use the resolved path
            image_path = resolved_path
            self.logger.info(f"Using resolved image path: {image_path}")

            # Create template with the specified threshold
            template = Template(image_path, threshold=threshold)
            self.logger.info(f"Created template with threshold: {threshold}")

            # Wait for the image to appear
            start_time = time.time()
            attempts = 0

            while time.time() - start_time < timeout:
                attempts += 1
                # Check if image exists
                try:
                    match_pos = exists(template)
                    if match_pos:
                        # Import the coordinate validator
                        from .coordinate_validator import validate_coordinates

                        # Get device dimensions if available
                        device_width = None
                        device_height = None
                        try:
                            if hasattr(self, 'get_device_size'):
                                device_size = self.get_device_size()
                                if device_size and len(device_size) == 2:
                                    device_width, device_height = device_size
                        except Exception as size_err:
                            self.logger.warning(f"Failed to get device size: {size_err}")

                        # Validate coordinates to prevent infinity or NaN values
                        valid_coords = validate_coordinates(match_pos, device_width, device_height)

                        if valid_coords:
                            self.logger.info(f"Image found at valid position: {valid_coords} (attempt {attempts}, elapsed {time.time() - start_time:.2f}s)")
                            return valid_coords
                        else:
                            self.logger.error(f"Image found but coordinates are invalid: {match_pos}. Continuing search...")
                            # Continue the search since these coordinates are invalid
                except Exception as match_err:
                    self.logger.warning(f"Error during image matching (attempt {attempts}): {match_err}")

                # Wait before next attempt
                time.sleep(0.5)

            self.logger.info(f"Image '{image_path}' not found after {attempts} attempts within timeout of {timeout} seconds")
            return None
        except Exception as e:
            self.logger.error(f"Error finding image: {e}")
            import traceback
            self.logger.error(traceback.format_exc())
            return None

    def get_screen_text(self):
        """Get all text content from the current screen as a single string"""
        try:
            # First try using UI Automator to get all text
            text_content = self.get_screen_content()

            if text_content:
                # Join all text elements into a single string for easier searching
                return " ".join(text_content)

            # Fallback method using dumpsys
            if self.device_id:
                try:
                    # Use dumpsys to get window state which may contain some text
                    output = self._run_adb_command(['shell', 'dumpsys', 'window', 'windows'], self.device_id)
                    if output:
                        return output
                except Exception as e:
                    self.logger.warning(f"Error getting screen text from dumpsys: {e}")

            return ""
        except Exception as e:
            self.logger.error(f"Error getting screen text: {e}")
            return ""

    def get_screen_content(self):
        """Get all text elements from the current screen"""
        try:
            if not self.driver:
                self.logger.error("No driver available for get_screen_content")
                return []

            # Get page source
            page_source = self.driver.page_source

            # Extract text content based on platform
            if self.platform_name and self.platform_name.lower() == 'ios':
                # For iOS, extract text from name, label, and value attributes
                import re
                text_elements = []

                # Extract text from name attributes
                name_matches = re.findall(r'name="([^"]*)"', page_source)
                text_elements.extend([m for m in name_matches if m.strip()])

                # Extract text from label attributes
                label_matches = re.findall(r'label="([^"]*)"', page_source)
                text_elements.extend([m for m in label_matches if m.strip()])

                # Extract text from value attributes
                value_matches = re.findall(r'value="([^"]*)"', page_source)
                text_elements.extend([m for m in value_matches if m.strip()])

                return text_elements
            else:
                # For Android, extract text from text attributes
                import re
                text_matches = re.findall(r'text="([^"]*)"', page_source)
                return [m for m in text_matches if m.strip()]
        except Exception as e:
            self.logger.error(f"Error getting screen content: {e}")
            return []

    def _run_adb_command(self, command, device_id=None):
        """Run an ADB command and return the output"""
        try:
            # Use provided device_id or fall back to self.device_id
            device_id = device_id or self.device_id

            if not device_id:
                return None

            # Run the command
            result = subprocess.run(['adb', '-s', device_id] + command,
                                  check=False,
                                  capture_output=True,
                                  text=True)

            return result.stdout.strip()
        except Exception as e:
            self.logger.error(f"Error running ADB command: {e}")
            return None

    def recover_connection(self):
        """Attempt to recover the device connection after an error"""
        self.logger.info("Attempting to recover device connection...")

        # Check if we have a device ID
        if not self.device_id:
            self.logger.error("Cannot recover connection - no device ID available")
            return False

        # Multiple retry attempts
        max_retries = 3
        for retry_attempt in range(max_retries):
            try:
                self.logger.info(f"Recovery attempt {retry_attempt + 1}/{max_retries}")

                # First, try to close the current driver if it exists
                if self.driver:
                    try:
                        self.logger.info("Closing existing driver session")
                        self.driver.quit()
                    except Exception as quit_error:
                        self.logger.warning(f"Error closing driver: {str(quit_error)}")
                    finally:
                        self.driver = None

                # Add a delay between stopping and starting new session
                time.sleep(3)

                # For iOS devices, try to terminate and restart WebDriverAgent
                if self.platform_name.lower() == 'ios':
                    try:
                        # Use idevicesyslog to check device connectivity
                        subprocess.run(['idevicesyslog', '-n', '0', '-u', self.device_id],
                                      timeout=2, check=False, stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL)

                        # Try to restart WebDriverAgent on the device
                        subprocess.run(['xcrun', 'xctrace', 'list', 'devices'],
                                      check=False, timeout=5)

                        self.logger.info("Verified iOS device is still connected")
                    except Exception as ios_error:
                        self.logger.warning(f"iOS device check failed: {str(ios_error)}")

                # For Android devices, try to restart UiAutomator2 server
                elif self.platform_name.lower() == 'android':
                    try:
                        # Check if device is connected via ADB
                        adb_result = subprocess.run(['adb', '-s', self.device_id, 'devices'],
                                                 check=False, capture_output=True, text=True, timeout=5)

                        if self.device_id in adb_result.stdout:
                            # Kill UiAutomator2 processes
                            subprocess.run(['adb', '-s', self.device_id, 'shell', 'am', 'force-stop', 'io.appium.uiautomator2.server'],
                                          check=False, timeout=5)
                            subprocess.run(['adb', '-s', self.device_id, 'shell', 'am', 'force-stop', 'io.appium.uiautomator2.server.test'],
                                          check=False, timeout=5)

                            self.logger.info("Restarted UiAutomator2 server processes")
                        else:
                            self.logger.warning(f"Device {self.device_id} not found in ADB devices list")
                    except Exception as android_error:
                        self.logger.warning(f"Android device server restart failed: {str(android_error)}")

                # Now reconnect with the same settings as before
                self.logger.info(f"Reinitializing driver for {self.platform_name} device {self.device_id}")

                # Create a new desired capabilities based on previously used settings
                if self.platform_name.lower() == 'ios':
                    desired_caps = {
                        'platformName': 'iOS',
                        'deviceName': self.device_id,
                        'udid': self.device_id,
                        'automationName': 'XCUITest',
                        'newCommandTimeout': 300,
                        'wdaLaunchTimeout': 60000,
                        'wdaConnectionTimeout': 60000,
                        'noReset': True,
                        'fullReset': False
                    }
                else:  # Android
                    desired_caps = {
                        'platformName': 'Android',
                        'deviceName': self.device_id,
                        'udid': self.device_id,
                        'automationName': 'UiAutomator2',
                        'newCommandTimeout': 300,
                        'noReset': True,
                        'fullReset': False
                    }

                # Recreate the driver
                self.logger.info("Creating new Appium driver session")
                self.driver = webdriver.Remote(
                    command_executor=self.appium_server_url,
                    desired_capabilities=desired_caps
                )

                # Verify the connection works
                if self.driver:
                    try:
                        # Take a screenshot as a test
                        test_screenshot = self.driver.get_screenshot_as_base64()
                        if test_screenshot:
                            self.logger.info("Connection successfully recovered and verified!")
                            return True
                    except Exception as verify_error:
                        self.logger.warning(f"Driver created but verification failed: {str(verify_error)}")
                        # Continue to next retry attempt

                # Add delay before next retry
                time.sleep(5)

            except Exception as e:
                self.logger.error(f"Error during recovery attempt {retry_attempt + 1}: {str(e)}")
                # Add delay before next retry
                time.sleep(5)

        self.logger.error("All connection recovery attempts failed")
        return False

    def _save_screenshot_to_database(self, filename, test_idx=None, step_idx=None, suite_id=None, action_id=None):
        """Helper to save screenshot info to database with error handling"""
        try:
            # Skip if no action_id is provided - this prevents duplicate entries
            if not action_id:
                self.logger.warning("Not saving screenshot to database because action_id is missing")
                return False

            # Skip if the filename is "latest.png" - we don't want to save this to the database
            if os.path.basename(filename) == "latest.png":
                self.logger.info("Not saving latest.png to database")
                return True

            # Use the provided indices or get from current context
            if test_idx is None or step_idx is None:
                try:
                    from app import current_test_idx, current_step_idx
                    if test_idx is None:
                        test_idx = getattr(current_test_idx, 'value', 0)
                    if step_idx is None:
                        step_idx = getattr(current_step_idx, 'value', 0)
                except (ImportError, AttributeError):
                    if test_idx is None:
                        test_idx = 0
                    if step_idx is None:
                        step_idx = 0

            # Use provided suite_id or try to get from app context
            if suite_id is None:
                try:
                    from app import current_suite_id
                    suite_id = current_suite_id
                except (ImportError, AttributeError):
                    suite_id = None

            # Extract the standardized filename from the path
            standardized_filename = os.path.basename(filename)

            # Ensure the filename matches the action_id
            if action_id and not standardized_filename.startswith(action_id):
                self.logger.info(f"Standardizing filename to match action_id: {action_id}.png")
                standardized_filename = f"{action_id}.png"

            # Check if this screenshot has already been saved to the database
            from .database import check_screenshot_exists
            if check_screenshot_exists(action_id):
                self.logger.info(f"Screenshot with action_id {action_id} already exists in database, skipping save")
                return True

            # Verify the file exists before saving to database
            if not os.path.exists(filename):
                self.logger.warning(f"Screenshot file {filename} does not exist, not saving to database")
                return False

            # Save to database
            from .database import save_screenshot_info
            self.logger.info(f"=== SAVING SCREENSHOT TO DATABASE ===")
            self.logger.info(f"suite_id: {suite_id}")
            self.logger.info(f"test_idx: {test_idx}")
            self.logger.info(f"step_idx: {step_idx}")
            self.logger.info(f"filename: {standardized_filename}")
            self.logger.info(f"action_id: {action_id}")

            save_screenshot_info(
                suite_id=suite_id,
                test_idx=test_idx,
                step_idx=step_idx,
                filename=standardized_filename,
                path=f"screenshots/{standardized_filename}",
                action_id=action_id
            )
            self.logger.info(f"Saved screenshot info to database for step {test_idx}_{step_idx}")
            return True
        except Exception as db_error:
            self.logger.error(f"Error saving screenshot info to database: {str(db_error)}")
            self.logger.exception(db_error)
            return False

    def is_session_active(self):
        """
        Check if the current Appium session is active and responsive

        Returns:
            bool: True if session is active and responsive, False otherwise
        """
        if not self.driver:
            self.logger.debug("No driver instance exists")
            return False

        try:
            # First check: Try to get the session status with a timeout
            import socket
            original_timeout = socket.getdefaulttimeout()
            socket.setdefaulttimeout(10)  # 10 second timeout for status check

            try:
                status = self.driver.status()
                self.logger.debug(f"Session status: {status}")
            finally:
                socket.setdefaulttimeout(original_timeout)

            # Second check: Try to perform a simple operation to verify responsiveness
            try:
                # Use a short timeout for this operation
                from selenium.webdriver.support.ui import WebDriverWait
                WebDriverWait(self.driver, 5).until(lambda d: d.current_activity is not None)
                self.logger.debug("Session is responsive (current_activity check passed)")
            except Exception as activity_err:
                # If getting current_activity fails, try getting page source instead
                try:
                    # Set a short timeout for getting page source
                    import threading
                    import time

                    # Flag to track if operation completed
                    operation_completed = False

                    # Function to get page source with timeout
                    def get_page_source():
                        nonlocal operation_completed
                        try:
                            self.driver.page_source
                            operation_completed = True
                        except Exception as ps_err:
                            self.logger.warning(f"Error getting page source: {ps_err}")

                    # Start thread to get page source
                    thread = threading.Thread(target=get_page_source)
                    thread.daemon = True
                    thread.start()

                    # Wait for thread to complete or timeout
                    start_time = time.time()
                    while thread.is_alive() and time.time() - start_time < 5:
                        time.sleep(0.1)

                    # Check if operation completed
                    if not operation_completed:
                        self.logger.warning("Session appears to be unresponsive (page_source check timed out)")
                        return False

                    self.logger.debug("Session is responsive (page_source check passed)")
                except Exception as ps_err:
                    self.logger.warning(f"Session appears to be unresponsive: {ps_err}")
                    return False

            # If we get here, the session is active and responsive
            return True
        except Exception as e:
            self.logger.warning(f"Session appears to be inactive: {e}")
            return False

    def is_connected(self):
        """
        Check if the device controller is connected and functional

        Returns:
            bool: True if connected, False otherwise
        """
        try:
            # Check if we have a device ID
            if not self.device_id:
                return False

            # Check if we have an active Appium driver session
            if self.driver and self.is_session_active():
                return True

            # Check if we have an active Airtest connection (fallback mode)
            if self.airtest_device and hasattr(self.airtest_device, 'get_current_resolution'):
                try:
                    # Try to get screen resolution as a quick connectivity test
                    resolution = self.airtest_device.get_current_resolution()
                    if resolution and len(resolution) == 2:
                        return True
                except Exception:
                    pass

            return False
        except Exception as e:
            self.logger.error(f"Error checking connection status: {e}")
            return False

    def get_driver(self):
        """
        Get the Appium driver instance, checking session health first

        Returns:
            WebDriver: The Appium driver instance or None if not available
        """
        # Check if we have a driver and if the session is active
        if self.driver and self.is_session_active():
            return self.driver

        # If we have a device ID but no active session, try to reconnect
        if self.device_id and not self.driver:
            self.logger.info("Driver not initialized but device ID exists, attempting to reconnect")
            if self.reconnect_device():
                return self.driver

        # If we have a driver but session is inactive, try to reconnect
        if self.driver and not self.is_session_active():
            self.logger.info("Driver exists but session is inactive, attempting to reconnect")
            if self.reconnect_device():
                return self.driver

        return self.driver

    def get_device_dimensions(self):
        """
        Get the dimensions of the connected device's screen

        Returns:
            tuple: (width, height) or None if dimensions cannot be determined
        """
        self.logger.info("Getting device dimensions")

        try:
            # Method 1: Try to get dimensions from Appium driver
            if self.driver:
                try:
                    window_size = self.driver.get_window_size()
                    if window_size and 'width' in window_size and 'height' in window_size:
                        width, height = window_size['width'], window_size['height']
                        self.logger.info(f"Got device dimensions from Appium: {width}x{height}")
                        self.device_dimensions = {'width': width, 'height': height}
                        return (width, height)
                except Exception as e:
                    self.logger.warning(f"Could not get window size from Appium driver: {e}")

            # Method 2: Try to get dimensions from Airtest device
            if self.airtest_device and hasattr(self.airtest_device, 'get_current_resolution'):
                resolution = self.airtest_device.get_current_resolution()
                if resolution and len(resolution) == 2:
                    width, height = resolution
                    self.logger.info(f"Got device dimensions from Airtest: {width}x{height}")
                    self.device_dimensions = {'width': width, 'height': height}
                    return (width, height)

            # Method 3: Use default dimensions based on platform
            if self.platform_name:
                if self.platform_name.lower() == 'ios':
                    # Default iPhone dimensions
                    width, height = 1170, 2532
                    self.logger.warning(f"Using default iOS dimensions: {width}x{height}")
                    self.device_dimensions = {'width': width, 'height': height}
                    return (width, height)
                elif self.platform_name.lower() == 'android':
                    # Default Android dimensions
                    width, height = 1080, 1920
                    self.logger.warning(f"Using default Android dimensions: {width}x{height}")
                    self.device_dimensions = {'width': width, 'height': height}
                    return (width, height)

            # If all methods fail, return None
            self.logger.warning("Could not determine device dimensions")
            return None

        except Exception as e:
            self.logger.error(f"Error getting device dimensions: {e}")
            self.logger.error(traceback.format_exc())
            return None

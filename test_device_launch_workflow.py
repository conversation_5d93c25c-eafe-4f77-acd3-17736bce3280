#!/usr/bin/env python3
"""
Test Device Launch Workflow

This script tests the complete end-to-end workflow from AppiumDeviceManager
to automation app launch with session isolation.
"""

import os
import sys
import json
import time
import logging
import requests
import subprocess
from pathlib import Path
from typing import List, Dict

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class DeviceLaunchWorkflowTester:
    """
    Tests the complete device launch workflow
    """
    
    def __init__(self):
        """Initialize the tester"""
        self.device_manager_url = "http://localhost:9000"
        self.test_results = []
        self.launched_sessions = []
        
    def log_test_result(self, test_name: str, passed: bool, message: str = ""):
        """Log a test result"""
        status = "PASS" if passed else "FAIL"
        result = {
            "test": test_name,
            "status": status,
            "message": message,
            "timestamp": time.time()
        }
        self.test_results.append(result)
        
        logger.info(f"[{status}] {test_name}: {message}")
    
    def test_device_manager_health(self) -> bool:
        """Test device manager API health"""
        logger.info("Testing device manager health...")
        
        try:
            response = requests.get(f"{self.device_manager_url}/api/device-manager/health", timeout=10)
            
            if response.status_code == 200:
                data = response.json()
                if data.get("success"):
                    self.log_test_result("Device Manager Health", True, "Device manager is healthy")
                    return True
                else:
                    self.log_test_result("Device Manager Health", False, "Device manager reports unhealthy")
                    return False
            else:
                self.log_test_result("Device Manager Health", False, f"HTTP {response.status_code}")
                return False
                
        except Exception as e:
            self.log_test_result("Device Manager Health", False, f"Connection error: {str(e)}")
            return False
    
    def test_device_discovery(self) -> List[Dict]:
        """Test device discovery functionality"""
        logger.info("Testing device discovery...")
        
        try:
            response = requests.get(f"{self.device_manager_url}/api/unified/devices", timeout=10)
            
            if response.status_code == 200:
                data = response.json()
                devices = data.get("devices", [])
                
                if len(devices) > 0:
                    self.log_test_result("Device Discovery", True, f"Found {len(devices)} devices")
                    
                    # Log device details
                    for device in devices:
                        logger.info(f"  Device: {device.get('name')} ({device.get('id')}) - {device.get('platform')}")
                    
                    return devices
                else:
                    self.log_test_result("Device Discovery", False, "No devices found")
                    return []
            else:
                self.log_test_result("Device Discovery", False, f"HTTP {response.status_code}")
                return []
                
        except Exception as e:
            self.log_test_result("Device Discovery", False, f"Error: {str(e)}")
            return []
    
    def test_device_launch(self, device: Dict) -> Dict:
        """Test launching automation app for a device"""
        device_id = device.get('id')
        platform = device.get('platform')
        
        logger.info(f"Testing device launch for {device_id} ({platform})...")
        
        try:
            launch_data = {
                "device_id": device_id,
                "platform": platform
            }
            
            response = requests.post(
                f"{self.device_manager_url}/api/device-manager/launch",
                json=launch_data,
                timeout=120  # Allow up to 2 minutes for launch
            )
            
            if response.status_code == 200:
                data = response.json()
                
                if data.get("success"):
                    session_id = data.get("session_id")
                    session_url = data.get("session_url")
                    
                    self.log_test_result("Device Launch", True, f"Session {session_id} launched successfully")
                    
                    # Store launched session for cleanup
                    self.launched_sessions.append({
                        "session_id": session_id,
                        "device_id": device_id,
                        "session_url": session_url
                    })
                    
                    return data
                else:
                    error = data.get("error", "Unknown error")
                    self.log_test_result("Device Launch", False, f"Launch failed: {error}")
                    return {}
            else:
                self.log_test_result("Device Launch", False, f"HTTP {response.status_code}")
                return {}
                
        except Exception as e:
            self.log_test_result("Device Launch", False, f"Error: {str(e)}")
            return {}
    
    def test_session_accessibility(self, session_data: Dict) -> bool:
        """Test that the launched session is accessible"""
        session_url = session_data.get("session_url")
        session_id = session_data.get("session_id")
        
        logger.info(f"Testing session accessibility for {session_id}...")
        
        if not session_url:
            self.log_test_result("Session Accessibility", False, "No session URL provided")
            return False
        
        try:
            # Wait a moment for the session to fully start
            time.sleep(5)
            
            # Test if the session URL is accessible
            response = requests.get(session_url, timeout=30)
            
            if response.status_code == 200:
                # Check if it contains expected content
                content = response.text
                
                if "Mobile App Automation Tool" in content:
                    self.log_test_result("Session Accessibility", True, "Session is accessible and contains expected content")
                    return True
                else:
                    self.log_test_result("Session Accessibility", False, "Session accessible but missing expected content")
                    return False
            else:
                self.log_test_result("Session Accessibility", False, f"HTTP {response.status_code}")
                return False
                
        except Exception as e:
            self.log_test_result("Session Accessibility", False, f"Error: {str(e)}")
            return False
    
    def test_pre_connected_state(self, session_data: Dict) -> bool:
        """Test that the session has pre-connected device state"""
        session_url = session_data.get("session_url")
        device_id = session_data.get("device_id")
        
        logger.info(f"Testing pre-connected state for device {device_id}...")
        
        try:
            # Parse URL parameters to check for pre-connection info
            from urllib.parse import urlparse, parse_qs
            
            parsed_url = urlparse(session_url)
            params = parse_qs(parsed_url.query)
            
            required_params = ['deviceId', 'platform', 'sessionId', 'autoConnect', 'hideDeviceList']
            missing_params = []
            
            for param in required_params:
                if param not in params:
                    missing_params.append(param)
            
            if missing_params:
                self.log_test_result("Pre-Connected State", False, f"Missing URL parameters: {missing_params}")
                return False
            
            # Check parameter values
            if params.get('autoConnect', [''])[0] != 'true':
                self.log_test_result("Pre-Connected State", False, "autoConnect parameter not set to true")
                return False
            
            if params.get('hideDeviceList', [''])[0] != 'true':
                self.log_test_result("Pre-Connected State", False, "hideDeviceList parameter not set to true")
                return False
            
            if params.get('deviceId', [''])[0] != device_id:
                self.log_test_result("Pre-Connected State", False, "deviceId parameter mismatch")
                return False
            
            self.log_test_result("Pre-Connected State", True, "All pre-connection parameters are correct")
            return True
            
        except Exception as e:
            self.log_test_result("Pre-Connected State", False, f"Error: {str(e)}")
            return False
    
    def test_session_isolation(self) -> bool:
        """Test that sessions are properly isolated"""
        logger.info("Testing session isolation...")
        
        if len(self.launched_sessions) < 1:
            self.log_test_result("Session Isolation", False, "No sessions available for isolation testing")
            return False
        
        try:
            # Check that each session has unique ports and session IDs
            session_ids = set()
            flask_ports = set()
            
            for session in self.launched_sessions:
                session_id = session.get("session_id")
                
                if session_id in session_ids:
                    self.log_test_result("Session Isolation", False, f"Duplicate session ID: {session_id}")
                    return False
                
                session_ids.add(session_id)
                
                # Extract port from session URL
                from urllib.parse import urlparse
                parsed_url = urlparse(session.get("session_url", ""))
                port = parsed_url.port
                
                if port in flask_ports:
                    self.log_test_result("Session Isolation", False, f"Duplicate Flask port: {port}")
                    return False
                
                flask_ports.add(port)
            
            self.log_test_result("Session Isolation", True, f"All {len(self.launched_sessions)} sessions are properly isolated")
            return True
            
        except Exception as e:
            self.log_test_result("Session Isolation", False, f"Error: {str(e)}")
            return False
    
    def cleanup_sessions(self):
        """Clean up launched sessions"""
        logger.info("Cleaning up launched sessions...")
        
        for session in self.launched_sessions:
            session_id = session.get("session_id")
            device_id = session.get("device_id")
            
            try:
                response = requests.post(
                    f"{self.device_manager_url}/api/device-manager/terminate/{device_id}",
                    timeout=30
                )
                
                if response.status_code == 200:
                    logger.info(f"Successfully terminated session {session_id}")
                else:
                    logger.warning(f"Failed to terminate session {session_id}: HTTP {response.status_code}")
                    
            except Exception as e:
                logger.error(f"Error terminating session {session_id}: {e}")
    
    def run_all_tests(self) -> Dict:
        """Run all workflow tests"""
        logger.info("Starting Device Launch Workflow Tests...")
        
        # Test 1: Device Manager Health
        if not self.test_device_manager_health():
            logger.error("Device Manager is not healthy - cannot continue tests")
            return self.generate_test_report()
        
        # Test 2: Device Discovery
        devices = self.test_device_discovery()
        if not devices:
            logger.error("No devices found - cannot continue with launch tests")
            return self.generate_test_report()
        
        # Test 3: Device Launch (test with first available device)
        test_device = devices[0]
        session_data = self.test_device_launch(test_device)
        
        if session_data:
            # Test 4: Session Accessibility
            self.test_session_accessibility(session_data)
            
            # Test 5: Pre-Connected State
            self.test_pre_connected_state(session_data)
        
        # Test 6: Session Isolation (if multiple devices available)
        if len(devices) > 1:
            # Launch second device for isolation testing
            second_device = devices[1]
            second_session = self.test_device_launch(second_device)
            
            if second_session:
                self.test_session_isolation()
        
        # Cleanup
        self.cleanup_sessions()
        
        return self.generate_test_report()
    
    def generate_test_report(self) -> Dict:
        """Generate comprehensive test report"""
        total_tests = len(self.test_results)
        passed_tests = len([r for r in self.test_results if r['status'] == 'PASS'])
        failed_tests = total_tests - passed_tests
        
        report = {
            "summary": {
                "total_tests": total_tests,
                "passed": passed_tests,
                "failed": failed_tests,
                "success_rate": (passed_tests / total_tests * 100) if total_tests > 0 else 0
            },
            "results": self.test_results,
            "launched_sessions": len(self.launched_sessions),
            "timestamp": time.time()
        }
        
        return report
    
    def print_test_report(self, report: Dict):
        """Print formatted test report"""
        print("\n" + "="*80)
        print("DEVICE LAUNCH WORKFLOW TEST REPORT")
        print("="*80)
        
        summary = report['summary']
        print(f"\nSummary:")
        print(f"  Total Tests: {summary['total_tests']}")
        print(f"  Passed: {summary['passed']}")
        print(f"  Failed: {summary['failed']}")
        print(f"  Success Rate: {summary['success_rate']:.1f}%")
        print(f"  Sessions Launched: {report['launched_sessions']}")
        
        print(f"\nDetailed Results:")
        for result in report['results']:
            status_symbol = "✅" if result['status'] == 'PASS' else "❌"
            print(f"  {status_symbol} {result['test']}: {result['message']}")
        
        print("\n" + "="*80)
        
        if summary['failed'] == 0:
            print("🎉 ALL TESTS PASSED - Device launch workflow is working correctly!")
        else:
            print(f"⚠️  {summary['failed']} TESTS FAILED - Please review the issues above")
        
        print("="*80)

def main():
    """Main test runner"""
    tester = DeviceLaunchWorkflowTester()
    
    try:
        print("🧪 Device Launch Workflow Tester")
        print("="*50)
        print("This test verifies the complete workflow:")
        print("1. Device Manager health")
        print("2. Device discovery")
        print("3. Automation app launch")
        print("4. Session accessibility")
        print("5. Pre-connected device state")
        print("6. Session isolation")
        print("="*50)
        
        # Check if device manager is running
        print("\n📡 Checking if Device Manager is running...")
        print("   If not running, start it with: python start_device_manager.py")
        
        report = tester.run_all_tests()
        tester.print_test_report(report)
        
        # Save report to file
        report_file = f"device_launch_workflow_test_report_{int(time.time())}.json"
        with open(report_file, 'w') as f:
            json.dump(report, f, indent=2)
        
        logger.info(f"Test report saved to: {report_file}")
        
        # Exit with appropriate code
        if report['summary']['failed'] == 0:
            sys.exit(0)
        else:
            sys.exit(1)
            
    except Exception as e:
        logger.error(f"Test runner failed: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
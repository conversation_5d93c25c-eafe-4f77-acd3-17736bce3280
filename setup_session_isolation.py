#!/usr/bin/env python3
"""
Session Isolation Setup Script

This script sets up and configures the complete session isolation system
for the Mobile App Automation Tool.
"""

import os
import sys
import json
import shutil
import logging
from pathlib import Path

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def setup_session_isolation():
    """Set up the session isolation system"""
    logger.info("Setting up Session Isolation System...")
    
    # Create necessary directories
    create_directories()
    
    # Verify file permissions
    verify_permissions()
    
    # Test imports
    test_imports()
    
    # Create sample configuration
    create_sample_config()
    
    logger.info("Session Isolation System setup completed successfully!")
    
    # Print usage instructions
    print_usage_instructions()

def create_directories():
    """Create necessary directories for session isolation"""
    logger.info("Creating directories...")
    
    directories = [
        "sessions",
        "sessions/temp",
        "app/data",
        "data",
        "logs"
    ]
    
    for directory in directories:
        Path(directory).mkdir(parents=True, exist_ok=True)
        logger.info(f"Created directory: {directory}")

def verify_permissions():
    """Verify file permissions for session isolation components"""
    logger.info("Verifying file permissions...")
    
    executable_files = [
        "session_isolation_manager.py",
        "enhanced_device_manager.py", 
        "device_manager_launcher.py",
        "test_session_isolation.py",
        "setup_session_isolation.py"
    ]
    
    for file_path in executable_files:
        if os.path.exists(file_path):
            # Make file executable
            os.chmod(file_path, 0o755)
            logger.info(f"Set executable permissions: {file_path}")

def test_imports():
    """Test that all session isolation modules can be imported"""
    logger.info("Testing module imports...")
    
    try:
        # Test core modules
        from session_isolation_manager import session_manager
        logger.info("✓ session_isolation_manager imported successfully")
        
        from enhanced_device_manager import device_manager
        logger.info("✓ enhanced_device_manager imported successfully")
        
        from unified_device_discovery import get_all_devices
        logger.info("✓ unified_device_discovery imported successfully")
        
        # Test that session manager is functional
        sessions = session_manager.list_active_sessions()
        logger.info(f"✓ Session manager functional - found {len(sessions)} active sessions")
        
    except ImportError as e:
        logger.error(f"Import error: {e}")
        logger.error("Please ensure all dependencies are installed")
        sys.exit(1)
    except Exception as e:
        logger.warning(f"Module test warning: {e}")

def create_sample_config():
    """Create sample configuration files"""
    logger.info("Creating sample configuration...")
    
    # Sample session configuration
    sample_session_config = {
        "session_isolation": {
            "enabled": True,
            "auto_discover_devices": True,
            "auto_start_sessions": False,
            "cleanup_on_exit": True
        },
        "ui_settings": {
            "hide_device_lists": True,
            "show_session_ids": True,
            "auto_refresh_screenshots": True,
            "screenshot_refresh_interval": 10
        },
        "port_allocation": {
            "base_flask_port": 8080,
            "base_appium_port": 4723,
            "base_wda_port": 8100,
            "port_range_limit": 100
        }
    }
    
    config_file = Path("session_isolation_config.json")
    if not config_file.exists():
        with open(config_file, 'w') as f:
            json.dump(sample_session_config, f, indent=2)
        logger.info(f"Created sample configuration: {config_file}")

def print_usage_instructions():
    """Print usage instructions for the session isolation system"""
    print("\n" + "="*80)
    print("SESSION ISOLATION SYSTEM - SETUP COMPLETE")
    print("="*80)
    
    print("\n🎉 The session isolation system has been set up successfully!")
    
    print("\n📋 QUICK START GUIDE:")
    print("="*50)
    
    print("\n1. Test the system:")
    print("   python test_session_isolation.py")
    
    print("\n2. Launch device manager (interactive):")
    print("   python device_manager_launcher.py")
    
    print("\n3. Auto-start all sessions:")
    print("   python device_manager_launcher.py --auto-start")
    
    print("\n4. Show session summary:")
    print("   python device_manager_launcher.py --summary-only")
    
    print("\n5. Access session manager UI:")
    print("   Open browser to: http://localhost:8080/api/session/manager")
    print("   (after starting at least one session)")
    
    print("\n🔧 CONFIGURATION:")
    print("="*50)
    print("- Edit 'session_isolation_config.json' to customize settings")
    print("- Session data stored in: ./sessions/")
    print("- Logs available in: ./logs/")
    
    print("\n📖 DOCUMENTATION:")
    print("="*50)
    print("- Read 'SESSION_ISOLATION_IMPLEMENTATION.md' for detailed documentation")
    print("- API endpoints documented in the implementation guide")
    
    print("\n🚀 KEY FEATURES:")
    print("="*50)
    print("✓ Complete session isolation per device")
    print("✓ Automatic device discovery and session creation")
    print("✓ Session ID-focused UI (device lists hidden)")
    print("✓ Isolated databases, screenshots, and configuration")
    print("✓ Unique port allocation per session")
    print("✓ Real-time session monitoring and control")
    
    print("\n⚠️  REQUIREMENTS:")
    print("="*50)
    print("- Connect at least one iOS or Android device")
    print("- Ensure device is unlocked and trusted")
    print("- For iOS: Xcode and libimobiledevice installed")
    print("- For Android: ADB installed and device in developer mode")
    
    print("\n" + "="*80)

def main():
    """Main setup function"""
    print("Session Isolation Setup")
    print("="*30)
    
    try:
        setup_session_isolation()
    except KeyboardInterrupt:
        print("\nSetup interrupted by user")
        sys.exit(1)
    except Exception as e:
        logger.error(f"Setup failed: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
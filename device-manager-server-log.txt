[0] [run.py] stderr: [2025-06-30 19:59:39,699] INFO in database: Screenshots table schema updated successfully
[0] 
[0] [run.py] stderr: [2025-06-30 19:59:39,699] INFO in database: === CLEARING EXECUTION TRACKING TABLE ===
[0] 
[0] [run.py] stderr: [2025-06-30 19:59:39,700] INFO in database: Found 0 records in execution_tracking table before clearing
[0] 
[0] [run.py] stderr: [2025-06-30 19:59:39,701] INFO in database: Successfully cleared execution_tracking table. Removed 0 records.
[0] 
[0] [run.py] stderr: [2025-06-30 19:59:39,805] INFO in appium_device_controller: Initialized AppiumDeviceController with Appium port: 4723, WDA port: 8100
[0] 
[0] [run.py] stderr: [2025-06-30 19:59:39,818] WARNING in appium_device_controller: Appium server check failed: HTTPConnectionPool(host='127.0.0.1', port=4723): Max retries exceeded with url: /wd/hub/status (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x116dee270>: Failed to establish a new connection: [Errno 61] Connection refused'))
[0] [2025-06-30 19:59:39,818] INFO in appium_device_controller: Checking for existing Appium and iproxy processes...
[0] 
[0] [run.py] stderr: [2025-06-30 19:59:39,857] INFO in appium_device_controller: Attempted to kill Appium processes
[0] 
[0] [run.py] stderr: [2025-06-30 19:59:39,895] INFO in appium_device_controller: Attempted to kill iproxy processes (default ports only)
[0] 
[0] [run.py] stderr: [2025-06-30 19:59:41,899] INFO in appium_device_controller: No Appium server detected. Starting a new one...
[0] 
[0] [run.py] stderr: [2025-06-30 19:59:41,899] INFO in appium_device_controller: Using local Appium installation at: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/.bin/appium
[0] 
[0] [API] Received GET request for /api/sessions
[0] [run.py] stderr: [2025-06-30 19:59:43,557] INFO in appium_device_controller: Installed Appium drivers: 
[0] 
[0] [run.py] stderr: [2025-06-30 19:59:43,557] INFO in appium_device_controller: Installing XCUITest driver...
[0] 
[0] [run.py] stderr: Error: ✖ A driver named "xcuitest" is already installed. Did you mean to update? Run "appium driver update". See installed drivers with "appium driver list --installed".
[0] 
[0] [run.py] stderr: [2025-06-30 19:59:44,263] ERROR in appium_device_controller: Error checking/installing drivers: Command '['/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/.bin/appium', 'driver', 'install', 'xcuitest']' returned non-zero exit status 1.
[0] 
[0] [run.py] stderr: [2025-06-30 19:59:44,264] INFO in appium_device_controller: Enabling inspector plugin if available
[0] [2025-06-30 19:59:44,264] INFO in appium_device_controller: Appium server output will be logged to: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/appium_server.log
[0] 
[0] [run.py] stderr: [2025-06-30 19:59:44,270] INFO in appium_device_controller: Started Appium server using command: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/.bin/appium --base-path /wd/hub --port 4723 --relaxed-security --use-drivers xcuitest,uiautomator2 --use-plugins=inspector --session-override --allow-cors --allow-insecure chromedriver_autodownload
[0] 
[0] [API] Received GET request for /api/sessions
[0] [run.py] stderr: [2025-06-30 19:59:46,273] WARNING in appium_device_controller: Waiting for Appium server to start (attempt 1/15): HTTPConnectionPool(host='127.0.0.1', port=4723): Max retries exceeded with url: /wd/hub/status (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x116dbec10>: Failed to establish a new connection: [Errno 61] Connection refused'))
[0] 
[0] [run.py] stderr: [2025-06-30 19:59:48,289] INFO in appium_device_controller: Appium server started successfully
[0] 
[0] [run.py] stderr: [2025-06-30 19:59:48,289] INFO in appium_device_controller: Appium server status: {'value': {'ready': True, 'message': 'The server is ready to accept new connections', 'build': {'version': '2.17.1', 'git-sha': '80fad842b6d7017fa1e21346eddab40a1623500c', 'built': '2025-06-29 21:34:57 +1000'}}}
[0] 
[0] [run.py] stderr: [2025-06-30 19:59:50,296] INFO in appium_device_controller: Appium server started successfully
[0] 
[0] [run.py] stderr: [2025-06-30 19:59:50,296] INFO in appium_device_controller: Appium server status: {'value': {'ready': True, 'message': 'The server is ready to accept new connections', 'build': {'version': '2.17.1', 'git-sha': '80fad842b6d7017fa1e21346eddab40a1623500c', 'built': '2025-06-29 21:34:57 +1000'}}}
[0] 
[0] [API] Received POST request for /api/devices/launch-app
[0] Launching run.py for iOS device 00008120-00186C801E13C01E on port 8080
[0] Session created: session_1751277591259_huiyacjp9 for device 00008120-00186C801E13C01E on port 8080
[0] Automation app launched with PID: 17848, Session: session_1751277591259_huiyacjp9
[0] [run.py] stderr: 2025-06-30 19:59:51,638 - utils.directory_paths_db - INFO - Directory paths and environments database initialized/verified
[0] 
[0] [run.py] stderr: 2025-06-30 19:59:51,639 - utils.directory_paths_db - INFO - Directory paths and environments database initialized/verified
[0] 
[0] [run.py] stderr: 2025-06-30 19:59:51,639 - config - INFO - Using database path for TEST_CASES: /Users/<USER>/Documents/automation-tool/test_cases
[0] 
[0] [run.py] stderr: 2025-06-30 19:59:51,640 - config - INFO - Using database path for REPORTS: /Users/<USER>/Documents/automation-tool/reports
[0] 
[0] [run.py] stderr: 2025-06-30 19:59:51,641 - config - INFO - Using database path for SCREENSHOTS: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/screenshots
[0] 
[0] [run.py] stderr: 2025-06-30 19:59:51,641 - config - INFO - Using database path for REFERENCE_IMAGES: /Users/<USER>/Documents/automation-tool/reference_images
[0] 
[0] [run.py] stderr: 2025-06-30 19:59:51,641 - config - INFO - Using database path for TEST_SUITES: /Users/<USER>/Documents/automation-tool/test_suites
[0] 
[0] [run.py] stderr: 2025-06-30 19:59:51,642 - config - INFO - Using database path for RESULTS: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/reports/suites
[0] 
[0] [run.py] stderr: 2025-06-30 19:59:51,642 - config - INFO - Using database path for RECORDINGS: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/recordings
[0] 
[0] [run.py] stderr: 2025-06-30 19:59:51,643 - config - INFO - Using database path for TEMP_FILES: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/temp
[0] 
[0] [run.py] stderr: 2025-06-30 19:59:51,643 - config - INFO - Using database path for FILES_TO_PUSH: /Users/<USER>/Documents/automation-tool/files_to_push
[0] 
[0] [run.py] stderr: 2025-06-30 19:59:51,643 - __main__ - INFO - Device-specific session isolation enabled for device: 00008120-00186C801E13C01E
[0] 
[0] [run.py] stderr: 2025-06-30 19:59:51,643 - __main__ - INFO - Using device-specific database suffix: _device_00008120_00186C801E13C01E
[0] 2025-06-30 19:59:51,643 - __main__ - INFO - Platform: iOS
[0] 
[0] [run.py] stderr: 2025-06-30 19:59:51,643 - __main__ - INFO - Using default ports - killing existing processes to avoid conflicts
[0] 
[0] [run.py] stderr: 2025-06-30 19:59:51,643 - __main__ - INFO - Killing any existing Appium and iproxy processes...
[0] 
[0] [run.py] stderr: [2025-06-30 19:59:52,302] WARNING in appium_device_controller: Waiting for Appium server to start (attempt 4/15): HTTPConnectionPool(host='127.0.0.1', port=4723): Max retries exceeded with url: /wd/hub/status (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x116df1d90>: Failed to establish a new connection: [Errno 61] Connection refused'))
[0] 
[0] [run.py] stderr: [2025-06-30 19:59:52,302] ERROR in appium_device_controller: Appium process exited with code 0
[0] [2025-06-30 19:59:52,302] ERROR in appium_device_controller: Stdout: None
[0] [2025-06-30 19:59:52,302] ERROR in appium_device_controller: Stderr: None
[0] 
[0] [run.py] stdout: Starting Mobile App Automation Tool...
[0] Configuration:
[0]   - Flask server port: 8080
[0]   - Appium server port: 4723
[0]   - WebDriverAgent port: 8100
[0] Open your web browser and navigate to: http://localhost:8080
[0]  * Serving Flask app 'app'
[0]  * Debug mode: on
[0] 
[0] [run.py] stderr: [2025-06-30 19:59:52,343] INFO in _internal: WARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.
[0]  * Running on all addresses (0.0.0.0)
[0]  * Running on http://127.0.0.1:8080
[0]  * Running on http://************:8080
[0] 
[0] [run.py] stderr: [2025-06-30 19:59:52,343] INFO in _internal: Press CTRL+C to quit
[0] 
[0] [run.py] stderr: [2025-06-30 19:59:53,350] INFO in _internal: 127.0.0.1 - - [30/Jun/2025 19:59:53] "GET / HTTP/1.1" 200 -
[0] 
[0] [run.py] stderr: [2025-06-30 19:59:53,381] INFO in _internal: 127.0.0.1 - - [30/Jun/2025 19:59:53] "GET /static/css/execution-overlay.css HTTP/1.1" 304 -
[0] 
[0] [run.py] stderr: [2025-06-30 19:59:53,381] INFO in _internal: 127.0.0.1 - - [30/Jun/2025 19:59:53] "GET /static/css/test-suites-styles.css HTTP/1.1" 304 -
[0] 
[0] [run.py] stderr: [2025-06-30 19:59:53,382] INFO in _internal: 127.0.0.1 - - [30/Jun/2025 19:59:53] "GET /static/css/test-case.css HTTP/1.1" 304 -
[0] 
[0] [run.py] stderr: [2025-06-30 19:59:53,384] INFO in _internal: 127.0.0.1 - - [30/Jun/2025 19:59:53] "GET /static/css/style.css HTTP/1.1" 304 -
[0] 
[0] [run.py] stderr: [2025-06-30 19:59:53,388] INFO in _internal: 127.0.0.1 - - [30/Jun/2025 19:59:53] "GET /static/css/test-cases-styles.css HTTP/1.1" 304 -
[0] 
[0] [run.py] stderr: [2025-06-30 19:59:53,389] INFO in _internal: 127.0.0.1 - - [30/Jun/2025 19:59:53] "GET /static/css/actionStyles.css HTTP/1.1" 304 -
[0] 
[0] [run.py] stderr: [2025-06-30 19:59:53,390] INFO in _internal: 127.0.0.1 - - [30/Jun/2025 19:59:53] "GET /static/css/modern-styles.css HTTP/1.1" 304 -
[0] 
[0] [run.py] stderr: [2025-06-30 19:59:53,390] INFO in _internal: 127.0.0.1 - - [30/Jun/2025 19:59:53] "GET /static/css/fixed-device-screen.css HTTP/1.1" 304 -
[0] 
[0] [run.py] stderr: [2025-06-30 19:59:53,392] INFO in _internal: 127.0.0.1 - - [30/Jun/2025 19:59:53] "GET /static/js/modules/uiUtils.js HTTP/1.1" 304 -
[0] 
[0] [run.py] stderr: [2025-06-30 19:59:53,393] INFO in _internal: 127.0.0.1 - - [30/Jun/2025 19:59:53] "GET /static/js/modules/actionFormManager.js HTTP/1.1" 304 -
[0] 
[0] [run.py] stderr: [2025-06-30 19:59:53,396] INFO in _internal: 127.0.0.1 - - [30/Jun/2025 19:59:53] "GET /static/js/modules/reportAndFormUtils.js HTTP/1.1" 304 -
[0] 
[0] [run.py] stderr: [2025-06-30 19:59:53,397] INFO in _internal: 127.0.0.1 - - [30/Jun/2025 19:59:53] "GET /static/js/export-run.js HTTP/1.1" 304 -
[0] 
[0] [run.py] stderr: [2025-06-30 19:59:53,400] INFO in _internal: 127.0.0.1 - - [30/Jun/2025 19:59:53] "GET /static/img/no_device.png HTTP/1.1" 304 -
[0] 
[0] [run.py] stderr: [2025-06-30 19:59:53,401] INFO in _internal: 127.0.0.1 - - [30/Jun/2025 19:59:53] "GET /static/js/utils.js HTTP/1.1" 304 -
[0] 
[0] [run.py] stderr: [2025-06-30 19:59:53,404] INFO in _internal: 127.0.0.1 - - [30/Jun/2025 19:59:53] "GET /static/js/action-manager.js?v=1751280593 HTTP/1.1" 200 -
[0] 
[0] [run.py] stderr: [2025-06-30 19:59:53,405] INFO in _internal: 127.0.0.1 - - [30/Jun/2025 19:59:53] "GET /static/js/modules/ElementInteractions.js HTTP/1.1" 304 -
[0] 
[0] [run.py] stderr: [2025-06-30 19:59:53,407] INFO in _internal: 127.0.0.1 - - [30/Jun/2025 19:59:53] "GET /static/js/fixed-device-screen.js HTTP/1.1" 304 -
[0] 
[0] [run.py] stderr: [2025-06-30 19:59:53,410] INFO in _internal: 127.0.0.1 - - [30/Jun/2025 19:59:53] "GET /static/js/execution-overlay.js HTTP/1.1" 304 -
[0] 
[0] [run.py] stderr: [2025-06-30 19:59:53,412] INFO in _internal: 127.0.0.1 - - [30/Jun/2025 19:59:53] "GET /static/js/execution-manager.js HTTP/1.1" 304 -
[0] 
[0] [run.py] stderr: [2025-06-30 19:59:53,413] INFO in _internal: 127.0.0.1 - - [30/Jun/2025 19:59:53] "GET /static/js/modules/TestCaseManager.js HTTP/1.1" 304 -
[0] 
[0] [run.py] stderr: [2025-06-30 19:59:53,418] INFO in _internal: 127.0.0.1 - - [30/Jun/2025 19:59:53] "GET /static/js/action-description.js HTTP/1.1" 304 -
[0] 
[0] [run.py] stderr: [2025-06-30 19:59:53,418] INFO in _internal: 127.0.0.1 - - [30/Jun/2025 19:59:53] "GET /static/js/multi-step-action.js HTTP/1.1" 304 -
[0] 
[0] [run.py] stderr: [2025-06-30 19:59:53,422] INFO in _internal: 127.0.0.1 - - [30/Jun/2025 19:59:53] "GET /static/guide/faq_guide.html HTTP/1.1" 304 -
[0] 
[0] [run.py] stderr: [2025-06-30 19:59:53,424] INFO in _internal: 127.0.0.1 - - [30/Jun/2025 19:59:53] "GET /static/js/repeat-steps-action.js HTTP/1.1" 304 -
[0] 
[0] [run.py] stderr: [2025-06-30 19:59:53,426] INFO in _internal: 127.0.0.1 - - [30/Jun/2025 19:59:53] "GET /static/js/modules/fallback-locators.js HTTP/1.1" 304 -
[0] 
[0] [run.py] stderr: [2025-06-30 19:59:53,431] INFO in _internal: 127.0.0.1 - - [30/Jun/2025 19:59:53] "GET /static/js/random-data-generator.js HTTP/1.1" 304 -
[0] 
[0] [run.py] stderr: [2025-06-30 19:59:53,432] INFO in _internal: 127.0.0.1 - - [30/Jun/2025 19:59:53] "GET /static/js/hook-action.js HTTP/1.1" 304 -
[0] 
[0] [run.py] stderr: [2025-06-30 19:59:53,434] INFO in _internal: 127.0.0.1 - - [30/Jun/2025 19:59:53] "GET /static/js/modules/tap-fallback-manager.js HTTP/1.1" 304 -
[0] 
[0] [run.py] stderr: [2025-06-30 19:59:53,435] INFO in _internal: 127.0.0.1 - - [30/Jun/2025 19:59:53] "GET /static/js/main.js?v=1751280593 HTTP/1.1" 200 -
[0] 
[0] [run.py] stderr: [2025-06-30 19:59:53,437] INFO in _internal: 127.0.0.1 - - [30/Jun/2025 19:59:53] "GET /static/js/test_suites.js HTTP/1.1" 304 -
[0] 
[0] [run.py] stderr: [2025-06-30 19:59:53,439] INFO in _internal: 127.0.0.1 - - [30/Jun/2025 19:59:53] "GET /static/js/settings.js HTTP/1.1" 304 -
[0] 
[0] [run.py] stderr: [2025-06-30 19:59:53,440] INFO in _internal: 127.0.0.1 - - [30/Jun/2025 19:59:53] "GET /static/js/environment-variables.js HTTP/1.1" 304 -
[0] 
[0] [run.py] stderr: [2025-06-30 19:59:53,483] INFO in _internal: 127.0.0.1 - - [30/Jun/2025 19:59:53] "GET /api/random_data/generators HTTP/1.1" 200 -
[0] 
[0] [run.py] stderr: [2025-06-30 19:59:53,488] INFO in _internal: 127.0.0.1 - - [30/Jun/2025 19:59:53] "GET /api/random_data/generators HTTP/1.1" 200 -
[0] 
[0] [run.py] stderr: [2025-06-30 19:59:53,490] INFO in _internal: 127.0.0.1 - - [30/Jun/2025 19:59:53] "GET /api/environments HTTP/1.1" 200 -
[0] 
[0] [run.py] stderr: [2025-06-30 19:59:53,496] INFO in _internal: 127.0.0.1 - - [30/Jun/2025 19:59:53] "GET /api/reference_images HTTP/1.1" 200 -
[0] 
[0] [run.py] stderr: [2025-06-30 19:59:53,502] INFO in directory_paths_db: Directory paths and environments database initialized/verified
[0] 
[0] [run.py] stderr: [2025-06-30 19:59:53,504] INFO in _internal: 127.0.0.1 - - [30/Jun/2025 19:59:53] "GET /api/settings HTTP/1.1" 200 -
[0] 
[0] [run.py] stderr: [2025-06-30 19:59:53,508] INFO in _internal: 127.0.0.1 - - [30/Jun/2025 19:59:53] "GET /api/environment_variables HTTP/1.1" 200 -
[0] 
[0] [run.py] stderr: [2025-06-30 19:59:53,510] INFO in directory_utils: Using reports directory from config.json: /Users/<USER>/Documents/automation-tool/reports
[0] 
[0] [run.py] stderr: [2025-06-30 19:59:53,514] INFO in _internal: 127.0.0.1 - - [30/Jun/2025 19:59:53] "GET /api/reference_images HTTP/1.1" 200 -
[0] 
[0] [run.py] stderr: [2025-06-30 19:59:53,521] INFO in _internal: 127.0.0.1 - - [30/Jun/2025 19:59:53] "GET /api/reference_images HTTP/1.1" 200 -
[0] 
[0] [run.py] stderr: [2025-06-30 19:59:53,526] INFO in _internal: 127.0.0.1 - - [30/Jun/2025 19:59:53] "GET /api/test_suites/list HTTP/1.1" 200 -
[0] 
[0] [run.py] stderr: [2025-06-30 19:59:53,529] INFO in _internal: 127.0.0.1 - - [30/Jun/2025 19:59:53] "GET /api/reference_images HTTP/1.1" 200 -
[0] 
[0] [run.py] stderr: [2025-06-30 19:59:53,532] INFO in _internal: 127.0.0.1 - - [30/Jun/2025 19:59:53] "GET /api/reports/latest HTTP/1.1" 200 -
[0] 
[0] [run.py] stderr: [2025-06-30 19:59:53,534] INFO in _internal: 127.0.0.1 - - [30/Jun/2025 19:59:53] "GET /api/environments/current HTTP/1.1" 200 -
[0] 
[0] [run.py] stderr: [2025-06-30 19:59:53,538] INFO in _internal: 127.0.0.1 - - [30/Jun/2025 19:59:53] "GET /static/img/favicon.ico HTTP/1.1" 304 -
[0] 
[0] [run.py] stderr: [2025-06-30 19:59:53,541] INFO in _internal: 127.0.0.1 - - [30/Jun/2025 19:59:53] "GET /api/reference_images HTTP/1.1" 200 -
[0] 
[0] [run.py] stderr: [2025-06-30 19:59:53,547] INFO in _internal: 127.0.0.1 - - [30/Jun/2025 19:59:53] "GET /api/reference_images HTTP/1.1" 200 -
[0] 
[0] [run.py] stderr: [2025-06-30 19:59:53,560] INFO in _internal: 127.0.0.1 - - [30/Jun/2025 19:59:53] "GET /api/environments/5/variables HTTP/1.1" 200 -
[0] 
[0] [run.py] stderr: [2025-06-30 19:59:53,594] INFO in _internal: 127.0.0.1 - - [30/Jun/2025 19:59:53] "GET /api/test_cases_for_multi_step HTTP/1.1" 200 -
[0] 
[0] [run.py] stderr: [2025-06-30 19:59:53,602] INFO in _internal: 127.0.0.1 - - [30/Jun/2025 19:59:53] "GET /api/recording/list HTTP/1.1" 200 -
[0] 
[0] [run.py] stderr: [2025-06-30 19:59:53,625] INFO in _internal: 127.0.0.1 - - [30/Jun/2025 19:59:53] "GET /api/tools/scan-databases HTTP/1.1" 200 -
[0] 
[0] [run.py] stderr: [2025-06-30 19:59:53,631] INFO in _internal: 127.0.0.1 - - [30/Jun/2025 19:59:53] "GET /api/test_cases_for_multi_step HTTP/1.1" 200 -
[0] 
[0] [run.py] stderr: 2025-06-30 19:59:53,722 - __main__ - INFO - Existing processes terminated
[0] 
[0] [run.py] stderr: 2025-06-30 19:59:54,647 - utils.global_values_db - INFO - Using global values database at: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app/data/global_values_device_00008120_00186C801E13C01E.db
[0] 
[0] [run.py] stderr: 2025-06-30 19:59:54,647 - utils.global_values_db - INFO - Global values database initialized successfully
[0] 
[0] [run.py] stderr: 2025-06-30 19:59:54,647 - utils.global_values_db - INFO - Using global values from config.py
[0] 
[0] [run.py] stderr: 2025-06-30 19:59:54,647 - utils.global_values_db - INFO - Updated default values from config.py: {'default_element_timeout': 60, 'Test Run Retry': 2, 'Auto Rerun Failed': False, 'Test Case Delay': 15, 'Max Step Execution Time': 300}
[0] 
[0] [run.py] stderr: 2025-06-30 19:59:54,648 - utils.healenium_config - INFO - Loaded Healenium configuration: enabled=True
[0] 
[0] [run.py] stderr: 2025-06-30 19:59:54,649 - appium_device_controller - WARNING - TouchAction not available in this Appium Python Client version - using W3C Actions fallback
[0] 
[0] [run.py] stderr: 2025-06-30 19:59:54,675 - AppiumDeviceController - INFO - Successfully imported Airtest library.
[0] 
[0] [API] Received GET request for /api/sessions
[0] [run.py] stderr: 2025-06-30 19:59:55,084 - app - INFO - Using directories from config.py:
[0] 
[0] [run.py] stderr: 2025-06-30 19:59:55,084 - app - INFO -   - TEST_CASES_DIR: /Users/<USER>/Documents/automation-tool/test_cases
[0] 2025-06-30 19:59:55,084 - app - INFO -   - REFERENCE_IMAGES_DIR: /Users/<USER>/Documents/automation-tool/reference_images
[0] 2025-06-30 19:59:55,084 - app - INFO -   - SCREENSHOTS_DIR: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/screenshots
[0] 
[0] [run.py] stderr: [2025-06-30 19:59:55,089] INFO in database: === UPDATING TEST_STEPS TABLE SCHEMA ===
[0] 
[0] [run.py] stderr: [2025-06-30 19:59:55,090] INFO in database: Test_steps table schema updated successfully
[0] 
[0] [run.py] stderr: [2025-06-30 19:59:55,090] INFO in database: === UPDATING SCREENSHOTS TABLE SCHEMA ===
[0] 
[0] [run.py] stderr: [2025-06-30 19:59:55,091] INFO in database: Screenshots table schema updated successfully
[0] 
[0] [run.py] stderr: [2025-06-30 19:59:55,091] INFO in database: === UPDATING EXECUTION_TRACKING TABLE SCHEMA ===
[0] 
[0] [run.py] stderr: [2025-06-30 19:59:55,092] INFO in database: step_idx column already exists in execution_tracking table
[0] 
[0] [run.py] stderr: [2025-06-30 19:59:55,092] INFO in database: action_type column already exists in execution_tracking table
[0] 
[0] [run.py] stderr: [2025-06-30 19:59:55,093] INFO in database: action_params column already exists in execution_tracking table
[0] 
[0] [run.py] stderr: [2025-06-30 19:59:55,093] INFO in database: action_id column already exists in execution_tracking table
[0] 
[0] [run.py] stderr: [2025-06-30 19:59:55,093] INFO in database: Successfully updated execution_tracking table schema
[0] 
[0] [run.py] stderr: [2025-06-30 19:59:55,093] INFO in database: Database initialized successfully
[0] 
[0] [run.py] stderr: [2025-06-30 19:59:55,093] INFO in database: Checking initial database state...
[0] 
[0] [run.py] stderr: [2025-06-30 19:59:55,094] INFO in database: Database state: 0 suites, 0 cases, 0 steps, 1 screenshots, 0 tracking entries
[0] 
[0] [run.py] stderr: [2025-06-30 19:59:55,096] INFO in database: === UPDATING TEST_STEPS TABLE SCHEMA ===
[0] 
[0] [run.py] stderr: [2025-06-30 19:59:55,097] INFO in database: Test_steps table schema updated successfully
[0] 
[0] [run.py] stderr: [2025-06-30 19:59:55,097] INFO in database: === UPDATING SCREENSHOTS TABLE SCHEMA ===
[0] 
[0] [run.py] stderr: [2025-06-30 19:59:55,098] INFO in database: Screenshots table schema updated successfully
[0] 
[0] [run.py] stderr: [2025-06-30 19:59:55,098] INFO in database: === UPDATING EXECUTION_TRACKING TABLE SCHEMA ===
[0] 
[0] [run.py] stderr: [2025-06-30 19:59:55,099] INFO in database: step_idx column already exists in execution_tracking table
[0] 
[0] [run.py] stderr: [2025-06-30 19:59:55,099] INFO in database: action_type column already exists in execution_tracking table
[0] 
[0] [run.py] stderr: [2025-06-30 19:59:55,099] INFO in database: action_params column already exists in execution_tracking table
[0] 
[0] [run.py] stderr: [2025-06-30 19:59:55,100] INFO in database: action_id column already exists in execution_tracking table
[0] 
[0] [run.py] stderr: [2025-06-30 19:59:55,100] INFO in database: Successfully updated execution_tracking table schema
[0] 
[0] [run.py] stderr: [2025-06-30 19:59:55,100] INFO in database: Database initialized successfully
[0] 
[0] [run.py] stderr: [2025-06-30 19:59:55,100] INFO in database: === UPDATING EXECUTION_TRACKING TABLE SCHEMA ===
[0] 
[0] [run.py] stderr: [2025-06-30 19:59:55,101] INFO in database: step_idx column already exists in execution_tracking table
[0] 
[0] [run.py] stderr: [2025-06-30 19:59:55,101] INFO in database: action_type column already exists in execution_tracking table
[0] 
[0] [run.py] stderr: [2025-06-30 19:59:55,101] INFO in database: action_params column already exists in execution_tracking table
[0] 
[0] [run.py] stderr: [2025-06-30 19:59:55,101] INFO in database: action_id column already exists in execution_tracking table
[0] 
[0] [run.py] stderr: [2025-06-30 19:59:55,101] INFO in database: Successfully updated execution_tracking table schema
[0] 
[0] [run.py] stderr: [2025-06-30 19:59:55,101] INFO in database: === UPDATING SCREENSHOTS TABLE SCHEMA ===
[0] 
[0] [run.py] stderr: [2025-06-30 19:59:55,102] INFO in database: Screenshots table schema updated successfully
[0] 
[0] [run.py] stderr: [2025-06-30 19:59:55,102] INFO in database: === CLEARING EXECUTION TRACKING TABLE ===
[0] 
[0] [run.py] stderr: [2025-06-30 19:59:55,103] INFO in database: Found 0 records in execution_tracking table before clearing
[0] 
[0] [run.py] stderr: [2025-06-30 19:59:55,104] INFO in database: Successfully cleared execution_tracking table. Removed 0 records.
[0] 
[0] [run.py] stderr: [2025-06-30 19:59:55,234] INFO in appium_device_controller: Initialized AppiumDeviceController with Appium port: 4723, WDA port: 8100
[0] 
[0] [run.py] stderr: [2025-06-30 19:59:55,239] WARNING in appium_device_controller: Appium server check failed: HTTPConnectionPool(host='127.0.0.1', port=4723): Max retries exceeded with url: /wd/hub/status (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x116ebb680>: Failed to establish a new connection: [Errno 61] Connection refused'))
[0] 
[0] [run.py] stderr: [2025-06-30 19:59:55,239] INFO in appium_device_controller: Checking for existing Appium and iproxy processes...
[0] 
[0] [run.py] stderr: [2025-06-30 19:59:55,277] INFO in appium_device_controller: Initialized AppiumDeviceController with Appium port: 4723, WDA port: 8100
[0] 
[0] [run.py] stderr: [2025-06-30 19:59:55,293] WARNING in appium_device_controller: Appium server check failed: HTTPConnectionPool(host='127.0.0.1', port=4723): Max retries exceeded with url: /wd/hub/status (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x115ae2270>: Failed to establish a new connection: [Errno 61] Connection refused'))
[0] 
[0] [run.py] stderr: [2025-06-30 19:59:55,293] INFO in appium_device_controller: Checking for existing Appium and iproxy processes...
[0] 
[0] [run.py] stderr: [2025-06-30 19:59:55,303] INFO in appium_device_controller: Attempted to kill Appium processes
[0] 
[0] [run.py] stderr: [2025-06-30 19:59:55,346] INFO in appium_device_controller: Attempted to kill Appium processes
[0] 
[0] [run.py] stderr: [2025-06-30 19:59:55,354] INFO in appium_device_controller: Attempted to kill iproxy processes (default ports only)
[0] 
[0] [run.py] stderr: [2025-06-30 19:59:55,383] INFO in appium_device_controller: Attempted to kill iproxy processes (default ports only)
[0] 
[0] [API] Received GET request for /api/sessions
[0] [run.py] stderr: [2025-06-30 19:59:57,361] INFO in appium_device_controller: No Appium server detected. Starting a new one...
[0] 
[0] [run.py] stderr: [2025-06-30 19:59:57,361] INFO in appium_device_controller: Using local Appium installation at: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/.bin/appium
[0] 
[0] [run.py] stderr: [2025-06-30 19:59:57,390] INFO in appium_device_controller: No Appium server detected. Starting a new one...
[0] 
[0] [run.py] stderr: [2025-06-30 19:59:57,391] INFO in appium_device_controller: Using local Appium installation at: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/.bin/appium
[0] 
[0] [run.py] stderr: [2025-06-30 19:59:58,172] INFO in appium_device_controller: Installed Appium drivers: 
[0] [2025-06-30 19:59:58,172] INFO in appium_device_controller: Installing XCUITest driver...
[0] 
[0] [run.py] stderr: [2025-06-30 19:59:58,172] INFO in appium_device_controller: Installed Appium drivers: 
[0] 
[0] [run.py] stderr: [2025-06-30 19:59:58,173] INFO in appium_device_controller: Installing XCUITest driver...
[0] 
[0] [run.py] stderr: [2025-06-30 19:59:58,481] INFO in directory_utils: Using reports directory from config.json: /Users/<USER>/Documents/automation-tool/reports
[0] 
[0] [run.py] stderr: [2025-06-30 19:59:58,483] INFO in _internal: 127.0.0.1 - - [30/Jun/2025 19:59:58] "GET /api/reports/latest HTTP/1.1" 200 -
[0] 
[0] [run.py] stderr: [2025-06-30 19:59:58,486] INFO in directory_utils: Using reports directory from config.json: /Users/<USER>/Documents/automation-tool/reports
[0] 
[0] [run.py] stderr: [2025-06-30 19:59:58,487] INFO in _internal: 127.0.0.1 - - [30/Jun/2025 19:59:58] "GET /api/reports/latest HTTP/1.1" 200 -
[0] 
[0] [run.py] stderr: Error: ✖ A driver named "xcuitest" is already installed. Did you mean to update? Run "appium driver update". See installed drivers with "appium driver list --installed".
[0] 
[0] [run.py] stderr: Error: ✖ A driver named "xcuitest" is already installed. Did you mean to update? Run "appium driver update". See installed drivers with "appium driver list --installed".
[0] 
[0] [run.py] stderr: [2025-06-30 19:59:58,990] ERROR in appium_device_controller: Error checking/installing drivers: Command '['/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/.bin/appium', 'driver', 'install', 'xcuitest']' returned non-zero exit status 1.
[0] 
[0] [run.py] stderr: [2025-06-30 19:59:58,990] INFO in appium_device_controller: Enabling inspector plugin if available
[0] [2025-06-30 19:59:58,990] INFO in appium_device_controller: Appium server output will be logged to: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/appium_server.log
[0] 
[0] [run.py] stderr: [2025-06-30 19:59:58,992] ERROR in appium_device_controller: Error checking/installing drivers: Command '['/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/.bin/appium', 'driver', 'install', 'xcuitest']' returned non-zero exit status 1.
[0] 
[0] [run.py] stderr: [2025-06-30 19:59:58,992] INFO in appium_device_controller: Enabling inspector plugin if available
[0] 
[0] [run.py] stderr: [2025-06-30 19:59:58,992] INFO in appium_device_controller: Appium server output will be logged to: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/appium_server.log
[0] 
[0] [run.py] stderr: [2025-06-30 19:59:58,997] INFO in appium_device_controller: Started Appium server using command: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/.bin/appium --base-path /wd/hub --port 4723 --relaxed-security --use-drivers xcuitest,uiautomator2 --use-plugins=inspector --session-override --allow-cors --allow-insecure chromedriver_autodownload
[0] 
[0] [run.py] stderr: [2025-06-30 19:59:58,998] INFO in appium_device_controller: Started Appium server using command: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/.bin/appium --base-path /wd/hub --port 4723 --relaxed-security --use-drivers xcuitest,uiautomator2 --use-plugins=inspector --session-override --allow-cors --allow-insecure chromedriver_autodownload
[0] 
[0] [run.py] stderr: [2025-06-30 20:00:01,018] INFO in appium_device_controller: Appium server started successfully
[0] 
[0] [run.py] stderr: [2025-06-30 20:00:01,018] INFO in appium_device_controller: Appium server status: {'value': {'ready': True, 'message': 'The server is ready to accept new connections', 'build': {'version': '2.17.1', 'git-sha': '80fad842b6d7017fa1e21346eddab40a1623500c', 'built': '2025-06-29 21:34:57 +1000'}}}
[0] 
[0] [run.py] stderr: [2025-06-30 20:00:01,022] INFO in appium_device_controller: Appium server started successfully
[0] 
[0] [run.py] stderr: [2025-06-30 20:00:01,022] INFO in appium_device_controller: Appium server status: {'value': {'ready': True, 'message': 'The server is ready to accept new connections', 'build': {'version': '2.17.1', 'git-sha': '80fad842b6d7017fa1e21346eddab40a1623500c', 'built': '2025-06-29 21:34:57 +1000'}}}
[0] 
[0] [run.py] stderr: [2025-06-30 20:00:03,027] INFO in appium_device_controller: Appium server started successfully
[0] 
[0] [run.py] stderr: [2025-06-30 20:00:03,027] INFO in appium_device_controller: Appium server status: {'value': {'ready': True, 'message': 'The server is ready to accept new connections', 'build': {'version': '2.17.1', 'git-sha': '80fad842b6d7017fa1e21346eddab40a1623500c', 'built': '2025-06-29 21:34:57 +1000'}}}
[0] 
[0] [run.py] stderr: [2025-06-30 20:00:03,030] INFO in appium_device_controller: Appium server started successfully
[0] 
[0] [run.py] stderr: [2025-06-30 20:00:03,030] INFO in appium_device_controller: Appium server status: {'value': {'ready': True, 'message': 'The server is ready to accept new connections', 'build': {'version': '2.17.1', 'git-sha': '80fad842b6d7017fa1e21346eddab40a1623500c', 'built': '2025-06-29 21:34:57 +1000'}}}
[0] 
[0] [run.py] stderr: [2025-06-30 20:00:03,480] INFO in directory_utils: Using reports directory from config.json: /Users/<USER>/Documents/automation-tool/reports
[0] 
[0] [run.py] stderr: [2025-06-30 20:00:03,482] INFO in _internal: 127.0.0.1 - - [30/Jun/2025 20:00:03] "GET /api/reports/latest HTTP/1.1" 200 -
[0] 
[0] [run.py] stderr: [2025-06-30 20:00:03,484] INFO in directory_utils: Using reports directory from config.json: /Users/<USER>/Documents/automation-tool/reports
[0] 
[0] [run.py] stderr: [2025-06-30 20:00:03,485] INFO in _internal: 127.0.0.1 - - [30/Jun/2025 20:00:03] "GET /api/reports/latest HTTP/1.1" 200 -
[0] 
[0] [run.py] stderr: [2025-06-30 20:00:05,033] INFO in appium_device_controller: Appium server started successfully
[0] 
[0] [run.py] stderr: [2025-06-30 20:00:05,034] INFO in appium_device_controller: Appium server status: {'value': {'ready': True, 'message': 'The server is ready to accept new connections', 'build': {'version': '2.17.1', 'git-sha': '80fad842b6d7017fa1e21346eddab40a1623500c', 'built': '2025-06-29 21:34:57 +1000'}}}
[0] 
[0] [run.py] stderr: [2025-06-30 20:00:05,036] INFO in appium_device_controller: Appium server started successfully
[0] 
[0] [run.py] stderr: [2025-06-30 20:00:05,036] INFO in appium_device_controller: Appium server status: {'value': {'ready': True, 'message': 'The server is ready to accept new connections', 'build': {'version': '2.17.1', 'git-sha': '80fad842b6d7017fa1e21346eddab40a1623500c', 'built': '2025-06-29 21:34:57 +1000'}}}
[0] 
[0] [API] Received GET request for /api/sessions
[0] [run.py] stderr: [2025-06-30 20:00:07,040] INFO in appium_device_controller: Appium server started successfully
[0] 
[0] [run.py] stderr: [2025-06-30 20:00:07,040] INFO in appium_device_controller: Appium server status: {'value': {'ready': True, 'message': 'The server is ready to accept new connections', 'build': {'version': '2.17.1', 'git-sha': '80fad842b6d7017fa1e21346eddab40a1623500c', 'built': '2025-06-29 21:34:57 +1000'}}}
[0] 
[0] [run.py] stderr: [2025-06-30 20:00:07,042] INFO in appium_device_controller: Appium server started successfully
[0] 
[0] [run.py] stderr: [2025-06-30 20:00:07,042] INFO in appium_device_controller: Appium server status: {'value': {'ready': True, 'message': 'The server is ready to accept new connections', 'build': {'version': '2.17.1', 'git-sha': '80fad842b6d7017fa1e21346eddab40a1623500c', 'built': '2025-06-29 21:34:57 +1000'}}}
[0] 
[0] [API] Received GET request for /api/sessions
[0] [run.py] stderr: [2025-06-30 20:00:08,481] INFO in directory_utils: Using reports directory from config.json: /Users/<USER>/Documents/automation-tool/reports
[0] 
[0] [run.py] stderr: [2025-06-30 20:00:08,483] INFO in _internal: 127.0.0.1 - - [30/Jun/2025 20:00:08] "GET /api/reports/latest HTTP/1.1" 200 -
[0] 
[0] [run.py] stderr: [2025-06-30 20:00:08,485] INFO in directory_utils: Using reports directory from config.json: /Users/<USER>/Documents/automation-tool/reports
[0] 
[0] [run.py] stderr: [2025-06-30 20:00:08,486] INFO in _internal: 127.0.0.1 - - [30/Jun/2025 20:00:08] "GET /api/reports/latest HTTP/1.1" 200 -
[0] 
[0] [run.py] stderr: [2025-06-30 20:00:09,052] INFO in appium_device_controller: Appium server started successfully
[0] 
[0] [run.py] stderr: [2025-06-30 20:00:09,053] INFO in appium_device_controller: Appium server status: {'value': {'ready': True, 'message': 'The server is ready to accept new connections', 'build': {'version': '2.17.1', 'git-sha': '80fad842b6d7017fa1e21346eddab40a1623500c', 'built': '2025-06-29 21:34:57 +1000'}}}
[0] 
[0] [run.py] stderr: [2025-06-30 20:00:09,055] INFO in appium_device_controller: Appium server started successfully
[0] 
[0] [run.py] stderr: [2025-06-30 20:00:09,055] INFO in appium_device_controller: Appium server status: {'value': {'ready': True, 'message': 'The server is ready to accept new connections', 'build': {'version': '2.17.1', 'git-sha': '80fad842b6d7017fa1e21346eddab40a1623500c', 'built': '2025-06-29 21:34:57 +1000'}}}
[0] 
[0] [run.py] stderr: [2025-06-30 20:00:11,058] INFO in appium_device_controller: Appium server started successfully
[0] 
[0] [run.py] stderr: [2025-06-30 20:00:11,058] INFO in appium_device_controller: Appium server status: {'value': {'ready': True, 'message': 'The server is ready to accept new connections', 'build': {'version': '2.17.1', 'git-sha': '80fad842b6d7017fa1e21346eddab40a1623500c', 'built': '2025-06-29 21:34:57 +1000'}}}
[0] 
[0] [run.py] stderr: [2025-06-30 20:00:11,060] INFO in appium_device_controller: Appium server started successfully
[0] 
[0] [run.py] stderr: [2025-06-30 20:00:11,060] INFO in appium_device_controller: Appium server status: {'value': {'ready': True, 'message': 'The server is ready to accept new connections', 'build': {'version': '2.17.1', 'git-sha': '80fad842b6d7017fa1e21346eddab40a1623500c', 'built': '2025-06-29 21:34:57 +1000'}}}
[0] 
[0] [run.py] stderr: [2025-06-30 20:00:13,039] INFO in _internal: 127.0.0.1 - - [30/Jun/2025 20:00:13] "GET /api/recording/list HTTP/1.1" 200 -
[0] 
[0] [run.py] stderr: [2025-06-30 20:00:13,061] INFO in _internal: 127.0.0.1 - - [30/Jun/2025 20:00:13] "GET /api/recording/list HTTP/1.1" 200 -
[0] 
[0] [run.py] stderr: [2025-06-30 20:00:13,066] INFO in appium_device_controller: Appium server started successfully
[0] 
[0] [run.py] stderr: [2025-06-30 20:00:13,066] INFO in appium_device_controller: Appium server status: {'value': {'ready': True, 'message': 'The server is ready to accept new connections', 'build': {'version': '2.17.1', 'git-sha': '80fad842b6d7017fa1e21346eddab40a1623500c', 'built': '2025-06-29 21:34:57 +1000'}}}
[0] 
[0] [run.py] stderr: [2025-06-30 20:00:13,068] INFO in appium_device_controller: Appium server started successfully
[0] 
[0] [run.py] stderr: [2025-06-30 20:00:13,069] INFO in appium_device_controller: Appium server status: {'value': {'ready': True, 'message': 'The server is ready to accept new connections', 'build': {'version': '2.17.1', 'git-sha': '80fad842b6d7017fa1e21346eddab40a1623500c', 'built': '2025-06-29 21:34:57 +1000'}}}
[0] 
[0] [run.py] stderr: [2025-06-30 20:00:13,480] INFO in directory_utils: Using reports directory from config.json: /Users/<USER>/Documents/automation-tool/reports
[0] 
[0] [run.py] stderr: [2025-06-30 20:00:13,482] INFO in _internal: 127.0.0.1 - - [30/Jun/2025 20:00:13] "GET /api/reports/latest HTTP/1.1" 200 -
[0] 
[0] [run.py] stderr: [2025-06-30 20:00:13,485] INFO in directory_utils: Using reports directory from config.json: /Users/<USER>/Documents/automation-tool/reports
[0] 
[0] [run.py] stderr: [2025-06-30 20:00:13,487] INFO in _internal: 127.0.0.1 - - [30/Jun/2025 20:00:13] "GET /api/reports/latest HTTP/1.1" 200 -
[0] 
[0] [run.py] stderr: [2025-06-30 20:00:14,831] INFO in _internal: 127.0.0.1 - - [30/Jun/2025 20:00:14] "GET /api/healenium/config HTTP/1.1" 200 -
[0] 
[0] [run.py] stderr: [2025-06-30 20:00:14,832] INFO in _internal: 127.0.0.1 - - [30/Jun/2025 20:00:14] "GET /api/directory_paths HTTP/1.1" 200 -
[0] 
[0] [run.py] stderr: [2025-06-30 20:00:14,833] INFO in _internal: 127.0.0.1 - - [30/Jun/2025 20:00:14] "GET /api/settings HTTP/1.1" 200 -
[0] 
[0] [run.py] stderr: [2025-06-30 20:00:14,836] INFO in _internal: 127.0.0.1 - - [30/Jun/2025 20:00:14] "GET /api/settings HTTP/1.1" 200 -
[0] 
[0] [run.py] stderr: [2025-06-30 20:00:15,074] INFO in appium_device_controller: Appium server started successfully
[0] 
[0] [run.py] stderr: [2025-06-30 20:00:15,074] INFO in appium_device_controller: Appium server status: {'value': {'ready': True, 'message': 'The server is ready to accept new connections', 'build': {'version': '2.17.1', 'git-sha': '80fad842b6d7017fa1e21346eddab40a1623500c', 'built': '2025-06-29 21:34:57 +1000'}}}
[0] 
[0] [run.py] stderr: [2025-06-30 20:00:15,075] INFO in appium_device_controller: Appium server started successfully
[0] 
[0] [run.py] stderr: [2025-06-30 20:00:15,075] INFO in appium_device_controller: Appium server status: {'value': {'ready': True, 'message': 'The server is ready to accept new connections', 'build': {'version': '2.17.1', 'git-sha': '80fad842b6d7017fa1e21346eddab40a1623500c', 'built': '2025-06-29 21:34:57 +1000'}}}
[0] 
[0] [API] Received GET request for /api/sessions
[0] [run.py] stderr: [2025-06-30 20:00:17,077] INFO in appium_device_controller: Appium server started successfully
[0] 
[0] [run.py] stderr: [2025-06-30 20:00:17,078] INFO in appium_device_controller: Appium server status: {'value': {'ready': True, 'message': 'The server is ready to accept new connections', 'build': {'version': '2.17.1', 'git-sha': '80fad842b6d7017fa1e21346eddab40a1623500c', 'built': '2025-06-29 21:34:57 +1000'}}}
[0] 
[0] [run.py] stderr: [2025-06-30 20:00:17,081] INFO in appium_device_controller: Appium server started successfully
[0] 
[0] [run.py] stderr: [2025-06-30 20:00:17,081] INFO in appium_device_controller: Appium server status: {'value': {'ready': True, 'message': 'The server is ready to accept new connections', 'build': {'version': '2.17.1', 'git-sha': '80fad842b6d7017fa1e21346eddab40a1623500c', 'built': '2025-06-29 21:34:57 +1000'}}}
[0] 
[0] [run.py] stderr: [2025-06-30 20:00:18,479] INFO in directory_utils: Using reports directory from config.json: /Users/<USER>/Documents/automation-tool/reports
[0] 
[0] [run.py] stderr: [2025-06-30 20:00:18,481] INFO in _internal: 127.0.0.1 - - [30/Jun/2025 20:00:18] "GET /api/reports/latest HTTP/1.1" 200 -
[0] 
[0] [run.py] stderr: [2025-06-30 20:00:18,483] INFO in directory_utils: Using reports directory from config.json: /Users/<USER>/Documents/automation-tool/reports
[0] 
[0] [run.py] stderr: [2025-06-30 20:00:18,484] INFO in _internal: 127.0.0.1 - - [30/Jun/2025 20:00:18] "GET /api/reports/latest HTTP/1.1" 200 -
[0] 
[0] [run.py] stderr: [2025-06-30 20:00:19,074] INFO in _internal: 127.0.0.1 - - [30/Jun/2025 20:00:19] "POST /api/environments/current HTTP/1.1" 200 -
[0] 
[0] [run.py] stderr: [2025-06-30 20:00:19,082] INFO in appium_device_controller: Appium server started successfully
[0] 
[0] [run.py] stderr: [2025-06-30 20:00:19,082] INFO in appium_device_controller: Appium server status: {'value': {'ready': True, 'message': 'The server is ready to accept new connections', 'build': {'version': '2.17.1', 'git-sha': '80fad842b6d7017fa1e21346eddab40a1623500c', 'built': '2025-06-29 21:34:57 +1000'}}}
[0] 
[0] [run.py] stderr: [2025-06-30 20:00:19,089] INFO in appium_device_controller: Appium server started successfully
[0] 
[0] [run.py] stderr: [2025-06-30 20:00:19,089] INFO in appium_device_controller: Appium server status: {'value': {'ready': True, 'message': 'The server is ready to accept new connections', 'build': {'version': '2.17.1', 'git-sha': '80fad842b6d7017fa1e21346eddab40a1623500c', 'built': '2025-06-29 21:34:57 +1000'}}}
[0] 
[0] [run.py] stderr: [2025-06-30 20:00:21,088] INFO in appium_device_controller: Appium server started successfully
[0] 
[0] [run.py] stderr: [2025-06-30 20:00:21,089] INFO in appium_device_controller: Appium server status: {'value': {'ready': True, 'message': 'The server is ready to accept new connections', 'build': {'version': '2.17.1', 'git-sha': '80fad842b6d7017fa1e21346eddab40a1623500c', 'built': '2025-06-29 21:34:57 +1000'}}}
[0] 
[0] [run.py] stderr: [2025-06-30 20:00:21,093] INFO in appium_device_controller: Appium server started successfully
[0] 
[0] [run.py] stderr: [2025-06-30 20:00:21,094] INFO in appium_device_controller: Appium server status: {'value': {'ready': True, 'message': 'The server is ready to accept new connections', 'build': {'version': '2.17.1', 'git-sha': '80fad842b6d7017fa1e21346eddab40a1623500c', 'built': '2025-06-29 21:34:57 +1000'}}}
[0] 
[0] [run.py] stderr: [2025-06-30 20:00:22,413] INFO in _internal: 127.0.0.1 - - [30/Jun/2025 20:00:22] "GET /api/recording/list HTTP/1.1" 200 -
[0] 
[0] [run.py] stderr: [2025-06-30 20:00:22,437] INFO in _internal: 127.0.0.1 - - [30/Jun/2025 20:00:22] "GET /api/recording/list HTTP/1.1" 200 -
[0] 
[0] [run.py] stderr: [2025-06-30 20:00:23,094] INFO in appium_device_controller: Appium server started successfully
[0] 
[0] [run.py] stderr: [2025-06-30 20:00:23,094] INFO in appium_device_controller: Appium server status: {'value': {'ready': True, 'message': 'The server is ready to accept new connections', 'build': {'version': '2.17.1', 'git-sha': '80fad842b6d7017fa1e21346eddab40a1623500c', 'built': '2025-06-29 21:34:57 +1000'}}}
[0] 
[0] [run.py] stderr: [2025-06-30 20:00:23,100] INFO in appium_device_controller: Appium server started successfully
[0] 
[0] [run.py] stderr: [2025-06-30 20:00:23,100] INFO in appium_device_controller: Appium server status: {'value': {'ready': True, 'message': 'The server is ready to accept new connections', 'build': {'version': '2.17.1', 'git-sha': '80fad842b6d7017fa1e21346eddab40a1623500c', 'built': '2025-06-29 21:34:57 +1000'}}}
[0] 
[0] [run.py] stderr: [2025-06-30 20:00:23,479] INFO in directory_utils: Using reports directory from config.json: /Users/<USER>/Documents/automation-tool/reports
[0] 
[0] [run.py] stderr: [2025-06-30 20:00:23,480] INFO in _internal: 127.0.0.1 - - [30/Jun/2025 20:00:23] "GET /api/reports/latest HTTP/1.1" 200 -
[0] 
[0] [run.py] stderr: [2025-06-30 20:00:23,482] INFO in directory_utils: Using reports directory from config.json: /Users/<USER>/Documents/automation-tool/reports
[0] 
[0] [run.py] stderr: [2025-06-30 20:00:23,483] INFO in _internal: 127.0.0.1 - - [30/Jun/2025 20:00:23] "GET /api/reports/latest HTTP/1.1" 200 -
[0] 
[0] [run.py] stderr: [2025-06-30 20:00:25,100] INFO in appium_device_controller: Appium server started successfully
[0] 
[0] [run.py] stderr: [2025-06-30 20:00:25,100] INFO in appium_device_controller: Appium server status: {'value': {'ready': True, 'message': 'The server is ready to accept new connections', 'build': {'version': '2.17.1', 'git-sha': '80fad842b6d7017fa1e21346eddab40a1623500c', 'built': '2025-06-29 21:34:57 +1000'}}}
[0] 
[0] [run.py] stderr: [2025-06-30 20:00:25,104] INFO in appium_device_controller: Appium server started successfully
[0] 
[0] [run.py] stderr: [2025-06-30 20:00:25,104] INFO in appium_device_controller: Appium server status: {'value': {'ready': True, 'message': 'The server is ready to accept new connections', 'build': {'version': '2.17.1', 'git-sha': '80fad842b6d7017fa1e21346eddab40a1623500c', 'built': '2025-06-29 21:34:57 +1000'}}}
[0] 
[0] [API] Received GET request for /api/sessions
[0] [run.py] stderr: [2025-06-30 20:00:27,107] INFO in appium_device_controller: Appium server started successfully
[0] 
[0] [run.py] stderr: [2025-06-30 20:00:27,108] INFO in appium_device_controller: Appium server status: {'value': {'ready': True, 'message': 'The server is ready to accept new connections', 'build': {'version': '2.17.1', 'git-sha': '80fad842b6d7017fa1e21346eddab40a1623500c', 'built': '2025-06-29 21:34:57 +1000'}}}
[0] 
[0] [run.py] stderr: [2025-06-30 20:00:27,110] INFO in appium_device_controller: Appium server started successfully
[0] 
[0] [run.py] stderr: [2025-06-30 20:00:27,110] INFO in appium_device_controller: Appium server status: {'value': {'ready': True, 'message': 'The server is ready to accept new connections', 'build': {'version': '2.17.1', 'git-sha': '80fad842b6d7017fa1e21346eddab40a1623500c', 'built': '2025-06-29 21:34:57 +1000'}}}
[0] 
[0] [run.py] stderr: [2025-06-30 20:00:27,355] INFO in _internal: 127.0.0.1 - - [30/Jun/2025 20:00:27] "GET / HTTP/1.1" 200 -
[0] 
[0] [run.py] stderr: [2025-06-30 20:00:27,374] INFO in _internal: 127.0.0.1 - - [30/Jun/2025 20:00:27] "GET /static/css/modern-styles.css HTTP/1.1" 304 -
[0] 
[0] [run.py] stderr: [2025-06-30 20:00:27,377] INFO in _internal: 127.0.0.1 - - [30/Jun/2025 20:00:27] "GET /static/css/style.css HTTP/1.1" 304 -
[0] 
[0] [run.py] stderr: [2025-06-30 20:00:27,377] INFO in _internal: 127.0.0.1 - - [30/Jun/2025 20:00:27] "GET /static/css/test-cases-styles.css HTTP/1.1" 304 -
[0] 
[0] [run.py] stderr: [2025-06-30 20:00:27,377] INFO in _internal: 127.0.0.1 - - [30/Jun/2025 20:00:27] "GET /static/css/test-case.css HTTP/1.1" 304 -
[0] 
[0] [run.py] stderr: [2025-06-30 20:00:27,379] INFO in _internal: 127.0.0.1 - - [30/Jun/2025 20:00:27] "GET /static/css/test-suites-styles.css HTTP/1.1" 304 -
[0] 
[0] [run.py] stderr: [2025-06-30 20:00:27,380] INFO in _internal: 127.0.0.1 - - [30/Jun/2025 20:00:27] "GET /static/css/execution-overlay.css HTTP/1.1" 304 -
[0] 
[0] [run.py] stderr: [2025-06-30 20:00:27,382] INFO in _internal: 127.0.0.1 - - [30/Jun/2025 20:00:27] "GET /static/css/fixed-device-screen.css HTTP/1.1" 304 -
[0] 
[0] [run.py] stderr: [2025-06-30 20:00:27,385] INFO in _internal: 127.0.0.1 - - [30/Jun/2025 20:00:27] "GET /static/css/actionStyles.css HTTP/1.1" 304 -
[0] 
[0] [run.py] stderr: [2025-06-30 20:00:27,387] INFO in _internal: 127.0.0.1 - - [30/Jun/2025 20:00:27] "GET /static/js/modules/uiUtils.js HTTP/1.1" 304 -
[0] 
[0] [run.py] stderr: [2025-06-30 20:00:27,390] INFO in _internal: 127.0.0.1 - - [30/Jun/2025 20:00:27] "GET /static/js/modules/actionFormManager.js HTTP/1.1" 304 -
[0] 
[0] [run.py] stderr: [2025-06-30 20:00:27,391] INFO in _internal: 127.0.0.1 - - [30/Jun/2025 20:00:27] "GET /static/js/modules/reportAndFormUtils.js HTTP/1.1" 304 -
[0] 
[0] [run.py] stderr: [2025-06-30 20:00:27,393] INFO in _internal: 127.0.0.1 - - [30/Jun/2025 20:00:27] "GET /static/js/export-run.js HTTP/1.1" 304 -
[0] 
[0] [run.py] stderr: [2025-06-30 20:00:27,394] INFO in _internal: 127.0.0.1 - - [30/Jun/2025 20:00:27] "GET /static/img/no_device.png HTTP/1.1" 304 -
[0] 
[0] [run.py] stderr: [2025-06-30 20:00:27,395] INFO in _internal: 127.0.0.1 - - [30/Jun/2025 20:00:27] "GET /static/js/utils.js HTTP/1.1" 304 -
[0] 
[0] [run.py] stderr: [2025-06-30 20:00:27,401] INFO in _internal: 127.0.0.1 - - [30/Jun/2025 20:00:27] "GET /static/js/action-manager.js?v=1751280627 HTTP/1.1" 200 -
[0] 
[0] [run.py] stderr: [2025-06-30 20:00:27,404] INFO in _internal: 127.0.0.1 - - [30/Jun/2025 20:00:27] "GET /static/js/modules/ElementInteractions.js HTTP/1.1" 304 -
[0] 
[0] [run.py] stderr: [2025-06-30 20:00:27,405] INFO in _internal: 127.0.0.1 - - [30/Jun/2025 20:00:27] "GET /static/js/fixed-device-screen.js HTTP/1.1" 304 -
[0] 
[0] [run.py] stderr: [2025-06-30 20:00:27,408] INFO in _internal: 127.0.0.1 - - [30/Jun/2025 20:00:27] "GET /static/js/execution-overlay.js HTTP/1.1" 304 -
[0] 
[0] [run.py] stderr: [2025-06-30 20:00:27,410] INFO in _internal: 127.0.0.1 - - [30/Jun/2025 20:00:27] "GET /static/js/execution-manager.js HTTP/1.1" 304 -
[0] 
[0] [run.py] stderr: [2025-06-30 20:00:27,412] INFO in _internal: 127.0.0.1 - - [30/Jun/2025 20:00:27] "GET /static/js/modules/TestCaseManager.js HTTP/1.1" 304 -
[0] 
[0] [run.py] stderr: [2025-06-30 20:00:27,413] INFO in _internal: 127.0.0.1 - - [30/Jun/2025 20:00:27] "GET /static/js/action-description.js HTTP/1.1" 304 -
[0] 
[0] [run.py] stderr: [2025-06-30 20:00:27,416] INFO in _internal: 127.0.0.1 - - [30/Jun/2025 20:00:27] "GET /static/guide/faq_guide.html HTTP/1.1" 304 -
[0] 
[0] [run.py] stderr: [2025-06-30 20:00:27,417] INFO in _internal: 127.0.0.1 - - [30/Jun/2025 20:00:27] "GET /static/js/multi-step-action.js HTTP/1.1" 304 -
[0] 
[0] [run.py] stderr: [2025-06-30 20:00:27,419] INFO in _internal: 127.0.0.1 - - [30/Jun/2025 20:00:27] "GET /static/js/repeat-steps-action.js HTTP/1.1" 304 -
[0] 
[0] [run.py] stderr: [2025-06-30 20:00:27,421] INFO in _internal: 127.0.0.1 - - [30/Jun/2025 20:00:27] "GET /static/js/hook-action.js HTTP/1.1" 304 -
[0] 
[0] [run.py] stderr: [2025-06-30 20:00:27,424] INFO in _internal: 127.0.0.1 - - [30/Jun/2025 20:00:27] "GET /static/js/modules/fallback-locators.js HTTP/1.1" 304 -
[0] 
[0] [run.py] stderr: [2025-06-30 20:00:27,427] INFO in _internal: 127.0.0.1 - - [30/Jun/2025 20:00:27] "GET /static/js/random-data-generator.js HTTP/1.1" 304 -
[0] 
[0] [run.py] stderr: [2025-06-30 20:00:27,428] INFO in _internal: 127.0.0.1 - - [30/Jun/2025 20:00:27] "GET /static/js/modules/tap-fallback-manager.js HTTP/1.1" 304 -
[0] 
[0] [run.py] stderr: [2025-06-30 20:00:27,432] INFO in _internal: 127.0.0.1 - - [30/Jun/2025 20:00:27] "GET /static/js/main.js?v=1751280627 HTTP/1.1" 200 -
[0] 
[0] [run.py] stderr: [2025-06-30 20:00:27,436] INFO in _internal: 127.0.0.1 - - [30/Jun/2025 20:00:27] "GET /static/js/test_suites.js HTTP/1.1" 304 -
[0] 
[0] [run.py] stderr: [2025-06-30 20:00:27,439] INFO in _internal: 127.0.0.1 - - [30/Jun/2025 20:00:27] "GET /static/js/settings.js HTTP/1.1" 304 -
[0] 
[0] [run.py] stderr: [2025-06-30 20:00:27,442] INFO in _internal: 127.0.0.1 - - [30/Jun/2025 20:00:27] "GET /static/js/environment-variables.js HTTP/1.1" 304 -
[0] 
[0] [run.py] stderr: [2025-06-30 20:00:27,453] INFO in _internal: 127.0.0.1 - - [30/Jun/2025 20:00:27] "GET /api/random_data/generators HTTP/1.1" 200 -
[0] 
[0] [run.py] stderr: [2025-06-30 20:00:27,459] INFO in _internal: 127.0.0.1 - - [30/Jun/2025 20:00:27] "GET /api/environments HTTP/1.1" 200 -
[0] 
[0] [run.py] stderr: [2025-06-30 20:00:27,460] INFO in _internal: 127.0.0.1 - - [30/Jun/2025 20:00:27] "GET /api/random_data/generators HTTP/1.1" 200 -
[0] 
[0] [run.py] stderr: [2025-06-30 20:00:27,462] INFO in directory_utils: Using reports directory from config.json: /Users/<USER>/Documents/automation-tool/reports
[0] 
[0] [run.py] stderr: [2025-06-30 20:00:27,467] INFO in _internal: 127.0.0.1 - - [30/Jun/2025 20:00:27] "GET /api/reference_images HTTP/1.1" 200 -
[0] 
[0] [run.py] stderr: [2025-06-30 20:00:27,475] INFO in _internal: 127.0.0.1 - - [30/Jun/2025 20:00:27] "GET /api/settings HTTP/1.1" 200 -
[0] 
[0] [run.py] stderr: [2025-06-30 20:00:27,477] INFO in _internal: 127.0.0.1 - - [30/Jun/2025 20:00:27] "GET /api/reports/latest HTTP/1.1" 200 -
[0] 
[0] [run.py] stderr: [2025-06-30 20:00:27,481] INFO in _internal: 127.0.0.1 - - [30/Jun/2025 20:00:27] "GET /api/environment_variables HTTP/1.1" 200 -
[0] 
[0] [run.py] stderr: [2025-06-30 20:00:27,488] INFO in _internal: 127.0.0.1 - - [30/Jun/2025 20:00:27] "GET /static/img/favicon.ico HTTP/1.1" 304 -
[0] 
[0] [run.py] stderr: [2025-06-30 20:00:27,493] INFO in _internal: 127.0.0.1 - - [30/Jun/2025 20:00:27] "GET /api/reference_images HTTP/1.1" 200 -
[0] 
[0] [run.py] stderr: [2025-06-30 20:00:27,494] INFO in _internal: 127.0.0.1 - - [30/Jun/2025 20:00:27] "GET /api/environments/current HTTP/1.1" 200 -
[0] 
[0] [run.py] stderr: [2025-06-30 20:00:27,502] INFO in _internal: 127.0.0.1 - - [30/Jun/2025 20:00:27] "GET /api/reference_images HTTP/1.1" 200 -
[0] 
[0] [run.py] stderr: [2025-06-30 20:00:27,503] INFO in _internal: 127.0.0.1 - - [30/Jun/2025 20:00:27] "GET /api/test_suites/list HTTP/1.1" 200 -
[0] 
[0] [run.py] stderr: [2025-06-30 20:00:27,510] INFO in _internal: 127.0.0.1 - - [30/Jun/2025 20:00:27] "GET /api/reference_images HTTP/1.1" 200 -
[0] 
[0] [run.py] stderr: [2025-06-30 20:00:27,519] INFO in _internal: 127.0.0.1 - - [30/Jun/2025 20:00:27] "GET /api/reference_images HTTP/1.1" 200 -
[0] 
[0] [run.py] stderr: [2025-06-30 20:00:27,524] INFO in _internal: 127.0.0.1 - - [30/Jun/2025 20:00:27] "GET /api/environments/5/variables HTTP/1.1" 200 -
[0] 
[0] [run.py] stderr: [2025-06-30 20:00:27,527] INFO in _internal: 127.0.0.1 - - [30/Jun/2025 20:00:27] "GET /api/reference_images HTTP/1.1" 200 -
[0] 
[0] [run.py] stderr: [2025-06-30 20:00:27,559] INFO in _internal: 127.0.0.1 - - [30/Jun/2025 20:00:27] "GET /api/test_cases_for_multi_step HTTP/1.1" 200 -
[0] 
[0] [run.py] stderr: [2025-06-30 20:00:27,569] INFO in _internal: 127.0.0.1 - - [30/Jun/2025 20:00:27] "GET /api/recording/list HTTP/1.1" 200 -
[0] 
[0] [run.py] stderr: [2025-06-30 20:00:27,591] INFO in _internal: 127.0.0.1 - - [30/Jun/2025 20:00:27] "GET /api/tools/scan-databases HTTP/1.1" 200 -
[0] 
[0] [run.py] stderr: [2025-06-30 20:00:27,594] INFO in _internal: 127.0.0.1 - - [30/Jun/2025 20:00:27] "GET /api/test_cases_for_multi_step HTTP/1.1" 200 -
[0] 
[0] [run.py] stderr: [2025-06-30 20:00:29,116] INFO in appium_device_controller: Appium server started successfully
[0] 
[0] [run.py] stderr: [2025-06-30 20:00:29,116] INFO in appium_device_controller: Appium server status: {'value': {'ready': True, 'message': 'The server is ready to accept new connections', 'build': {'version': '2.17.1', 'git-sha': '80fad842b6d7017fa1e21346eddab40a1623500c', 'built': '2025-06-29 21:34:57 +1000'}}}
[0] 
[0] [run.py] stderr: [2025-06-30 20:00:29,117] INFO in appium_device_controller: Appium server started successfully
[0] 
[0] [run.py] stderr: [2025-06-30 20:00:29,117] INFO in appium_device_controller: Appium server status: {'value': {'ready': True, 'message': 'The server is ready to accept new connections', 'build': {'version': '2.17.1', 'git-sha': '80fad842b6d7017fa1e21346eddab40a1623500c', 'built': '2025-06-29 21:34:57 +1000'}}}
[0] 
[0] [run.py] stderr: [2025-06-30 20:00:29,117] INFO in _internal: 127.0.0.1 - - [30/Jun/2025 20:00:29] "GET /api/devices HTTP/1.1" 200 -
[0] 
[0] [run.py] stdout: Starting Mobile App Automation Tool...
[0] Configuration:
[0]   - Flask server port: 8080
[0]   - Appium server port: 4723
[0]   - WebDriverAgent port: 8100
[0] Open your web browser and navigate to: http://localhost:8080
[0]  * Serving Flask app 'app'
[0]  * Debug mode: on
[0] 
[0] [run.py] stderr: Address already in use
[0] Port 8080 is in use by another program. Either identify and stop that program, or start the server with a different port.
[0] 
[0] [run.py] stderr: [2025-06-30 20:00:29,209] INFO in _internal: 127.0.0.1 - - [30/Jun/2025 20:00:29] "GET /api/devices HTTP/1.1" 200 -
[0] 
[0] [run.py] Process exited with code: 1
[0] Failed to kill process 17848: kill ESRCH
[0] Session terminated: session_1751277591259_huiyacjp9 for device 00008120-00186C801E13C01E
[0] [run.py] stderr: [2025-06-30 20:00:32,455] INFO in directory_utils: Using reports directory from config.json: /Users/<USER>/Documents/automation-tool/reports
[0] 
[0] [run.py] stderr: [2025-06-30 20:00:32,457] INFO in _internal: 127.0.0.1 - - [30/Jun/2025 20:00:32] "GET /api/reports/latest HTTP/1.1" 200 -
[0] 
[0] [run.py] stderr: [2025-06-30 20:00:32,460] INFO in directory_utils: Using reports directory from config.json: /Users/<USER>/Documents/automation-tool/reports
[0] 
[0] [run.py] stderr: [2025-06-30 20:00:32,461] INFO in _internal: 127.0.0.1 - - [30/Jun/2025 20:00:32] "GET /api/reports/latest HTTP/1.1" 200 -
[0] 
[0] [API] Received GET request for /api/sessions
[0] [API] Received GET request for /api/sessions
[0] [run.py] stderr: [2025-06-30 20:00:37,453] INFO in directory_utils: Using reports directory from config.json: /Users/<USER>/Documents/automation-tool/reports
[0] 
[0] [run.py] stderr: [2025-06-30 20:00:37,454] INFO in _internal: 127.0.0.1 - - [30/Jun/2025 20:00:37] "GET /api/reports/latest HTTP/1.1" 200 -
[0] 
[0] [run.py] stderr: [2025-06-30 20:00:37,456] INFO in directory_utils: Using reports directory from config.json: /Users/<USER>/Documents/automation-tool/reports
[0] 
[0] [run.py] stderr: [2025-06-30 20:00:37,457] INFO in _internal: 127.0.0.1 - - [30/Jun/2025 20:00:37] "GET /api/reports/latest HTTP/1.1" 200 -
[0] 
[0] [run.py] stderr: [2025-06-30 20:00:42,455] INFO in directory_utils: Using reports directory from config.json: /Users/<USER>/Documents/automation-tool/reports
[0] 
[0] [run.py] stderr: [2025-06-30 20:00:42,457] INFO in _internal: 127.0.0.1 - - [30/Jun/2025 20:00:42] "GET /api/reports/latest HTTP/1.1" 200 -
[0] 
[0] [run.py] stderr: [2025-06-30 20:00:42,460] INFO in directory_utils: Using reports directory from config.json: /Users/<USER>/Documents/automation-tool/reports
[0] 
[0] [run.py] stderr: [2025-06-30 20:00:42,461] INFO in _internal: 127.0.0.1 - - [30/Jun/2025 20:00:42] "GET /api/reports/latest HTTP/1.1" 200 -
[0] 
[0] [API] Received GET request for /api/sessions
[0] [run.py] stderr: [2025-06-30 20:00:47,455] INFO in directory_utils: Using reports directory from config.json: /Users/<USER>/Documents/automation-tool/reports
[0] 
[0] [run.py] stderr: [2025-06-30 20:00:47,457] INFO in _internal: 127.0.0.1 - - [30/Jun/2025 20:00:47] "GET /api/reports/latest HTTP/1.1" 200 -
[0] 
[0] [run.py] stderr: [2025-06-30 20:00:47,459] INFO in directory_utils: Using reports directory from config.json: /Users/<USER>/Documents/automation-tool/reports
[0] 
[0] [run.py] stderr: [2025-06-30 20:00:47,460] INFO in _internal: 127.0.0.1 - - [30/Jun/2025 20:00:47] "GET /api/reports/latest HTTP/1.1" 200 -
[0] 
[0] [run.py] stderr: [2025-06-30 20:00:52,455] INFO in directory_utils: Using reports directory from config.json: /Users/<USER>/Documents/automation-tool/reports
[0] 
[0] [run.py] stderr: [2025-06-30 20:00:52,456] INFO in _internal: 127.0.0.1 - - [30/Jun/2025 20:00:52] "GET /api/reports/latest HTTP/1.1" 200 -
[0] 
[0] [run.py] stderr: [2025-06-30 20:00:52,459] INFO in directory_utils: Using reports directory from config.json: /Users/<USER>/Documents/automation-tool/reports
[0] 
[0] [run.py] stderr: [2025-06-30 20:00:52,460] INFO in _internal: 127.0.0.1 - - [30/Jun/2025 20:00:52] "GET /api/reports/latest HTTP/1.1" 200 -
[0] 
[0] [run.py] stderr: [2025-06-30 20:00:57,455] INFO in directory_utils: Using reports directory from config.json: /Users/<USER>/Documents/automation-tool/reports
[0] 
[0] [run.py] stderr: [2025-06-30 20:00:57,457] INFO in _internal: 127.0.0.1 - - [30/Jun/2025 20:00:57] "GET /api/reports/latest HTTP/1.1" 200 -
[0] 
[0] [run.py] stderr: [2025-06-30 20:00:57,460] INFO in directory_utils: Using reports directory from config.json: /Users/<USER>/Documents/automation-tool/reports
[0] 
[0] [run.py] stdout: === LATEST REPORT ENDPOINT CALLED ===
[0] === REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
[0] === SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===
[0] === ABOUT TO CALL getLatestReportUrl() ===
[0] === getLatestReportUrl() RETURNED: /reports/testsuite_execution_20250630_002129/mainreport.html ===
[0] === LATEST REPORT ENDPOINT CALLED ===
[0] === REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
[0] === SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===
[0] === ABOUT TO CALL getLatestReportUrl() ===
[0] === getLatestReportUrl() RETURNED: /reports/testsuite_execution_20250630_002129/mainreport.html ===
[0] === LATEST REPORT ENDPOINT CALLED ===
[0] === REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
[0] === SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===
[0] === ABOUT TO CALL getLatestReportUrl() ===
[0] === getLatestReportUrl() RETURNED: /reports/testsuite_execution_20250630_002129/mainreport.html ===
[0] === LATEST REPORT ENDPOINT CALLED ===
[0] === REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
[0] === SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===
[0] === ABOUT TO CALL getLatestReportUrl() ===
[0] === getLatestReportUrl() RETURNED: /reports/testsuite_execution_20250630_002129/mainreport.html ===
[0] === LATEST REPORT ENDPOINT CALLED ===
[0] === REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
[0] === SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===
[0] === ABOUT TO CALL getLatestReportUrl() ===
[0] === getLatestReportUrl() RETURNED: /reports/testsuite_execution_20250630_002129/mainreport.html ===
[0] === LATEST REPORT ENDPOINT CALLED ===
[0] === REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
[0] === SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===
[0] === ABOUT TO CALL getLatestReportUrl() ===
[0] === getLatestReportUrl() RETURNED: /reports/testsuite_execution_20250630_002129/mainreport.html ===
[0] === LATEST REPORT ENDPOINT CALLED ===
[0] === REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
[0] === SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===
[0] === ABOUT TO CALL getLatestReportUrl() ===
[0] === getLatestReportUrl() RETURNED: /reports/testsuite_execution_20250630_002129/mainreport.html ===
[0] === LATEST REPORT ENDPOINT CALLED ===
[0] === REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
[0] === SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===
[0] === ABOUT TO CALL getLatestReportUrl() ===
[0] === getLatestReportUrl() RETURNED: /reports/testsuite_execution_20250630_002129/mainreport.html ===
[0] === LATEST REPORT ENDPOINT CALLED ===
[0] === REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
[0] === SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===
[0] === ABOUT TO CALL getLatestReportUrl() ===
[0] === getLatestReportUrl() RETURNED: /reports/testsuite_execution_20250630_002129/mainreport.html ===
[0] === LATEST REPORT ENDPOINT CALLED ===
[0] === REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
[0] === SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===
[0] === ABOUT TO CALL getLatestReportUrl() ===
[0] === getLatestReportUrl() RETURNED: /reports/testsuite_execution_20250630_002129/mainreport.html ===
[0] === LATEST REPORT ENDPOINT CALLED ===
[0] === REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
[0] === SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===
[0] === ABOUT TO CALL getLatestReportUrl() ===
[0] === getLatestReportUrl() RETURNED: /reports/testsuite_execution_20250630_002129/mainreport.html ===
[0] === LATEST REPORT ENDPOINT CALLED ===
[0] === REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
[0] === SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===
[0] === ABOUT TO CALL getLatestReportUrl() ===
[0] === getLatestReportUrl() RETURNED: /reports/testsuite_execution_20250630_002129/mainreport.html ===
[0] === LATEST REPORT ENDPOINT CALLED ===
[0] === REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
[0] === SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===
[0] === ABOUT TO CALL getLatestReportUrl() ===
[0] === getLatestReportUrl() RETURNED: /reports/testsuite_execution_20250630_002129/mainreport.html ===
[0] === LATEST REPORT ENDPOINT CALLED ===
[0] === REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
[0] === SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===
[0] === ABOUT TO CALL getLatestReportUrl() ===
[0] === getLatestReportUrl() RETURNED: /reports/testsuite_execution_20250630_002129/mainreport.html ===
[0] === LATEST REPORT ENDPOINT CALLED ===
[0] === REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
[0] === SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===
[0] === ABOUT TO CALL getLatestReportUrl() ===
[0] === getLatestReportUrl() RETURNED: /reports/testsuite_execution_20250630_002129/mainreport.html ===
[0] === LATEST REPORT ENDPOINT CALLED ===
[0] === REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
[0] === SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===
[0] === ABOUT TO CALL getLatestReportUrl() ===
[0] === getLatestReportUrl() RETURNED: /reports/testsuite_execution_20250630_002129/mainreport.html ===
[0] === LATEST REPORT ENDPOINT CALLED ===
[0] === REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
[0] === SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===
[0] === ABOUT TO CALL getLatestReportUrl() ===
[0] === getLatestReportUrl() RETURNED: /reports/testsuite_execution_20250630_002129/mainreport.html ===
[0] === LATEST REPORT ENDPOINT CALLED ===
[0] === REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
[0] === SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===
[0] === ABOUT TO CALL getLatestReportUrl() ===
[0] === getLatestReportUrl() RETURNED: /reports/testsuite_execution_20250630_002129/mainreport.html ===
[0] === LATEST REPORT ENDPOINT CALLED ===
[0] === REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
[0] === SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===
[0] === ABOUT TO CALL getLatestReportUrl() ===
[0] === getLatestReportUrl() RETURNED: /reports/testsuite_execution_20250630_002129/mainreport.html ===
[0] === LATEST REPORT ENDPOINT CALLED ===
[0] === REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
[0] === SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===
[0] === ABOUT TO CALL getLatestReportUrl() ===
[0] === getLatestReportUrl() RETURNED: /reports/testsuite_execution_20250630_002129/mainreport.html ===
[0] === LATEST REPORT ENDPOINT CALLED ===
[0] === REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
[0] === SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===
[0] === ABOUT TO CALL getLatestReportUrl() ===
[0] === getLatestReportUrl() RETURNED: /reports/testsuite_execution_20250630_002129/mainreport.html ===
[0] === LATEST REPORT ENDPOINT CALLED ===
[0] === REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
[0] === SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===
[0] === ABOUT TO CALL getLatestReportUrl() ===
[0] === getLatestReportUrl() RETURNED: /reports/testsuite_execution_20250630_002129/mainreport.html ===
[0] === LATEST REPORT ENDPOINT CALLED ===
[0] === REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
[0] === SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===
[0] === ABOUT TO CALL getLatestReportUrl() ===
[0] === getLatestReportUrl() RETURNED: /reports/testsuite_execution_20250630_002129/mainreport.html ===
[0] === LATEST REPORT ENDPOINT CALLED ===
[0] === REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
[0] === SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===
[0] === ABOUT TO CALL getLatestReportUrl() ===
[0] === getLatestReportUrl() RETURNED: /reports/testsuite_execution_20250630_002129/mainreport.html ===
[0] === LATEST REPORT ENDPOINT CALLED ===
[0] === REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
[0] === SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===
[0] === ABOUT TO CALL getLatestReportUrl() ===
[0] === getLatestReportUrl() RETURNED: /reports/testsuite_execution_20250630_002129/mainreport.html ===
[0] === LATEST REPORT ENDPOINT CALLED ===
[0] === REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
[0] === SUCCESSFULLY IMPO
[0] [run.py] stdout: RTED getLatestReportUrl FUNCTION ===
[0] [run.py] stderr: [2025-06-30 20:00:57,461] INFO in _internal: 127.0.0.1 - - [30/Jun/2025 20:00:57] "GET /api/reports/latest HTTP/1.1" 200 -
[0] 
[0] [run.py] stderr: [2025-06-30 20:01:02,455] INFO in directory_utils: Using reports directory from config.json: /Users/<USER>/Documents/automation
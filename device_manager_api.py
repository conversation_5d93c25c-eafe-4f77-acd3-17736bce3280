#!/usr/bin/env python3
"""
Device Manager API for launching isolated automation sessions

This module provides the API endpoints for the AppiumDeviceManager interface
to launch automation apps with complete session isolation.
"""

import os
import sys
import json
import time
import logging
import subprocess
import webbrowser
from pathlib import Path
from flask import Flask, request, jsonify, send_file
from typing import Dict, Optional

# Add current directory to path for imports
sys.path.insert(0, str(Path(__file__).parent))

from session_isolation_manager import session_manager
from enhanced_device_manager import device_manager
from unified_device_discovery import get_all_devices
from unified_routes import unified_bp

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Create Flask app for device manager
app = Flask(__name__)
app.secret_key = 'device_manager_secret_key_2024'

# Register unified routes
app.register_blueprint(unified_bp)

class DeviceManagerLauncher:
    """
    Handles launching automation apps with complete session isolation
    """
    
    def __init__(self):
        """Initialize the device manager launcher"""
        self.active_launches = {}  # Track active launch processes
        self.device_sessions = {}  # Map device_id -> session_id
        
        logger.info("Device Manager Launcher initialized")
    
    def launch_automation_app(self, device_id: str, platform: str) -> Dict:
        """
        Launch automation app for a specific device with complete isolation
        
        Args:
            device_id: Device identifier
            platform: Device platform (iOS/Android)
            
        Returns:
            Dict with launch result and session information
        """
        try:
            logger.info(f"Starting automation app launch for device {device_id} ({platform})")
            
            # Check if device is already in use
            if device_id in self.device_sessions:
                existing_session = self.device_sessions[device_id]
                return {
                    "success": False,
                    "error": f"Device {device_id} is already in use by session {existing_session}"
                }
            
            # Step 1: Create isolated session
            logger.info("Step 1: Creating isolated session...")
            session_id = session_manager.create_session(device_id, platform)
            
            if not session_id:
                return {
                    "success": False,
                    "error": "Failed to create isolated session"
                }
            
            # Step 2: Get session configuration
            session_config = session_manager.get_session(session_id)
            if not session_config:
                return {
                    "success": False,
                    "error": "Failed to get session configuration"
                }
            
            # Step 3: Start the automation app process
            logger.info("Step 3: Starting automation app process...")
            success = self._start_automation_process(session_id, session_config)
            
            if not success:
                # Cleanup session on failure
                session_manager.cleanup_session(session_id, remove_data=True)
                return {
                    "success": False,
                    "error": "Failed to start automation app process"
                }
            
            # Step 4: Register device session mapping
            self.device_sessions[device_id] = session_id
            
            # Step 5: Generate session URL with pre-connected device
            session_url = self._generate_session_url(session_config)
            
            logger.info(f"Automation app launched successfully for device {device_id}")
            logger.info(f"Session ID: {session_id}")
            logger.info(f"Session URL: {session_url}")
            
            return {
                "success": True,
                "session_id": session_id,
                "session_url": session_url,
                "device_id": device_id,
                "platform": platform,
                "ports": session_config["ports"],
                "message": "Automation app launched successfully"
            }
            
        except Exception as e:
            logger.error(f"Error launching automation app for device {device_id}: {e}")
            return {
                "success": False,
                "error": str(e)
            }
    
    def _start_automation_process(self, session_id: str, session_config: Dict) -> bool:
        """
        Start the automation app process with session isolation
        
        Args:
            session_id: Session identifier
            session_config: Session configuration
            
        Returns:
            True if process started successfully
        """
        try:
            # Set up environment variables for complete isolation
            env = os.environ.copy()
            env.update(session_config["environment"])
            
            # Additional environment variables for the automation app
            env.update({
                "FLASK_ENV": "development",
                "FLASK_DEBUG": "1",
                "PYTHONPATH": str(Path(__file__).parent),
                "SESSION_ISOLATION_MODE": "true"
            })
            
            # Build command to start the automation app
            ports = session_config["ports"]
            device_id = session_config["device_id"]
            platform = session_config["platform"]
            
            # Determine which run script to use based on platform
            if platform.lower() == "android":
                run_script = "run_android.py"
            else:
                run_script = "run.py"
            
            cmd = [
                sys.executable, run_script,
                "--port", str(ports["flask"]),
                "--appium-port", str(ports["appium"]),
                "--wda-port", str(ports["wda"])
            ]
            
            logger.info(f"Starting automation process with command: {' '.join(cmd)}")
            logger.info(f"Environment variables: SESSION_ID={session_id}, DEVICE_ID={device_id}")
            
            # Start the process in the background
            process = subprocess.Popen(
                cmd,
                env=env,
                cwd=str(Path(__file__).parent),
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                start_new_session=True  # Start in new process group
            )
            
            # Store process information in session config
            session_config["process_id"] = process.pid
            session_config["status"] = "starting"
            session_config["started_at"] = time.time()
            
            # Save updated session config
            session_manager._save_session_registry()
            
            # Wait a moment to ensure process started
            time.sleep(2)
            
            # Check if process is still running
            if process.poll() is None:
                logger.info(f"Automation process started successfully with PID {process.pid}")
                
                # Wait for the Flask server to be ready
                self._wait_for_flask_server(ports["flask"], timeout=60)
                
                # Update session status
                session_config["status"] = "running"
                session_manager._save_session_registry()
                
                return True
            else:
                # Process exited immediately
                stdout, stderr = process.communicate()
                logger.error(f"Automation process exited immediately")
                logger.error(f"STDOUT: {stdout.decode()}")
                logger.error(f"STDERR: {stderr.decode()}")
                return False
                
        except Exception as e:
            logger.error(f"Error starting automation process: {e}")
            return False
    
    def _wait_for_flask_server(self, port: int, timeout: int = 60) -> bool:
        """
        Wait for Flask server to be ready
        
        Args:
            port: Flask server port
            timeout: Timeout in seconds
            
        Returns:
            True if server is ready
        """
        import socket
        import time
        
        start_time = time.time()
        
        while time.time() - start_time < timeout:
            try:
                with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
                    s.settimeout(1)
                    result = s.connect_ex(('localhost', port))
                    if result == 0:
                        logger.info(f"Flask server is ready on port {port}")
                        return True
            except Exception:
                pass
            
            time.sleep(1)
        
        logger.error(f"Timeout waiting for Flask server on port {port}")
        return False
    
    def _generate_session_url(self, session_config: Dict) -> str:
        """
        Generate session URL with pre-connected device parameters
        
        Args:
            session_config: Session configuration
            
        Returns:
            Complete session URL
        """
        flask_port = session_config["ports"]["flask"]
        device_id = session_config["device_id"]
        platform = session_config["platform"]
        session_id = session_config["session_id"]
        
        # Create URL with parameters for automatic device connection
        url = (
            f"http://localhost:{flask_port}?"
            f"deviceId={device_id}&"
            f"platform={platform}&"
            f"sessionId={session_id}&"
            f"autoConnect=true&"
            f"hideDeviceList=true"
        )
        
        return url
    
    def get_device_status(self, device_id: str) -> Dict:
        """
        Get status of a device
        
        Args:
            device_id: Device identifier
            
        Returns:
            Device status information
        """
        if device_id in self.device_sessions:
            session_id = self.device_sessions[device_id]
            session_config = session_manager.get_session(session_id)
            
            if session_config:
                return {
                    "device_id": device_id,
                    "status": "in_use",
                    "session_id": session_id,
                    "session_status": session_config.get("status", "unknown"),
                    "session_url": self._generate_session_url(session_config)
                }
        
        return {
            "device_id": device_id,
            "status": "available"
        }
    
    def terminate_device_session(self, device_id: str) -> bool:
        """
        Terminate session for a device
        
        Args:
            device_id: Device identifier
            
        Returns:
            True if session terminated successfully
        """
        if device_id not in self.device_sessions:
            return False
        
        session_id = self.device_sessions[device_id]
        
        # Stop the session
        success = session_manager.stop_session(session_id)
        
        if success:
            # Remove device mapping
            del self.device_sessions[device_id]
            logger.info(f"Terminated session {session_id} for device {device_id}")
        
        return success

# Global launcher instance
launcher = DeviceManagerLauncher()

# API Routes
@app.route('/')
def index():
    """Serve the device manager interface"""
    return send_file('appium_device_manager.html')

@app.route('/api/device-manager/launch', methods=['POST'])
def launch_device():
    """Launch automation app for a device"""
    try:
        data = request.get_json()
        device_id = data.get('device_id')
        platform = data.get('platform', 'iOS')
        
        if not device_id:
            return jsonify({
                "success": False,
                "error": "Device ID is required"
            }), 400
        
        result = launcher.launch_automation_app(device_id, platform)
        
        if result["success"]:
            return jsonify(result)
        else:
            return jsonify(result), 500
            
    except Exception as e:
        logger.error(f"Error in launch_device endpoint: {e}")
        return jsonify({
            "success": False,
            "error": str(e)
        }), 500

@app.route('/api/device-manager/status/<device_id>', methods=['GET'])
def get_device_status(device_id):
    """Get status of a specific device"""
    try:
        status = launcher.get_device_status(device_id)
        return jsonify(status)
    except Exception as e:
        logger.error(f"Error getting device status: {e}")
        return jsonify({"error": str(e)}), 500

@app.route('/api/device-manager/terminate/<device_id>', methods=['POST'])
def terminate_device_session(device_id):
    """Terminate session for a device"""
    try:
        success = launcher.terminate_device_session(device_id)
        
        if success:
            return jsonify({
                "success": True,
                "message": f"Session terminated for device {device_id}"
            })
        else:
            return jsonify({
                "success": False,
                "error": "No active session found for device"
            }), 404
            
    except Exception as e:
        logger.error(f"Error terminating device session: {e}")
        return jsonify({
            "success": False,
            "error": str(e)
        }), 500

@app.route('/api/device-manager/devices', methods=['GET'])
def list_devices_with_status():
    """List all devices with their current status"""
    try:
        devices = get_all_devices()
        
        # Add status information to each device
        for device in devices:
            device_status = launcher.get_device_status(device['id'])
            device.update(device_status)
        
        return jsonify({
            "success": True,
            "devices": devices,
            "total": len(devices)
        })
        
    except Exception as e:
        logger.error(f"Error listing devices: {e}")
        return jsonify({
            "success": False,
            "error": str(e)
        }), 500

@app.route('/api/device-manager/health', methods=['GET'])
def health_check():
    """Health check for device manager"""
    try:
        devices = get_all_devices()
        active_sessions = len(launcher.device_sessions)
        
        return jsonify({
            "success": True,
            "status": "healthy",
            "devices_connected": len(devices),
            "active_sessions": active_sessions,
            "timestamp": time.time()
        })
        
    except Exception as e:
        logger.error(f"Health check failed: {e}")
        return jsonify({
            "success": False,
            "status": "unhealthy",
            "error": str(e)
        }), 500

if __name__ == "__main__":
    print("Starting Appium Device Manager...")
    print("="*50)
    print("Device Manager Interface: http://localhost:9000")
    print("API Endpoints:")
    print("  - POST /api/device-manager/launch")
    print("  - GET  /api/device-manager/devices")
    print("  - GET  /api/device-manager/status/<device_id>")
    print("  - POST /api/device-manager/terminate/<device_id>")
    print("="*50)
    
    # Run the device manager on port 9000 to avoid conflicts
    app.run(debug=True, host='0.0.0.0', port=9000, use_reloader=False)
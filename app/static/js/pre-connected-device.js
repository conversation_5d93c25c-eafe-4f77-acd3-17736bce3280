/**
 * Pre-Connected Device Handler
 * 
 * Handles automatic device connection and simplified UI when launched from AppiumDeviceManager
 * Removes device selection dropdown and connect button when device is pre-selected
 */

class PreConnectedDeviceHandler {
    constructor() {
        this.isPreConnected = false;
        this.deviceId = null;
        this.platform = null;
        this.sessionId = null;
        this.autoConnect = false;
        this.hideDeviceList = false;
        
        this.init();
    }
    
    init() {
        console.log('Initializing Pre-Connected Device Handler...');
        
        // Check URL parameters for pre-connection info
        this.parseUrlParameters();
        
        // If pre-connected, set up the simplified UI
        if (this.isPreConnected) {
            this.setupPreConnectedUI();
            this.autoConnectDevice();
        }
        
        // Listen for DOM changes to handle dynamically loaded content
        this.observeUIChanges();
    }
    
    parseUrlParameters() {
        const urlParams = new URLSearchParams(window.location.search);
        
        this.deviceId = urlParams.get('deviceId');
        this.platform = urlParams.get('platform');
        this.sessionId = urlParams.get('sessionId');
        this.autoConnect = urlParams.get('autoConnect') === 'true';
        this.hideDeviceList = urlParams.get('hideDeviceList') === 'true';
        
        // Determine if this is a pre-connected session
        this.isPreConnected = !!(this.deviceId && (this.autoConnect || this.hideDeviceList));
        
        console.log('Pre-connection parameters:', {
            deviceId: this.deviceId,
            platform: this.platform,
            sessionId: this.sessionId,
            autoConnect: this.autoConnect,
            hideDeviceList: this.hideDeviceList,
            isPreConnected: this.isPreConnected
        });
    }
    
    setupPreConnectedUI() {
        console.log('Setting up pre-connected UI...');
        
        // Add pre-connected mode class to body
        document.body.classList.add('pre-connected-mode');
        
        // Update page title
        document.title = `Session ${this.sessionId?.substring(0, 12) || 'Unknown'} - Mobile Automation`;
        
        // Create and inject session header
        this.createSessionHeader();
        
        // Hide device selection elements
        this.hideDeviceSelectionUI();
        
        // Set up session-specific screenshot handling
        this.setupSessionScreenshots();
        
        // Add session isolation indicator
        this.addSessionIsolationIndicator();
    }
    
    createSessionHeader() {
        // Check if session header already exists
        if (document.querySelector('.pre-connected-header')) {
            return;
        }
        
        const header = document.createElement('div');
        header.className = 'pre-connected-header alert alert-info mb-3';
        header.innerHTML = `
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h6 class="mb-1">
                        <i class="bi bi-shield-check me-2"></i>
                        Isolated Session Active
                    </h6>
                    <div class="d-flex align-items-center gap-3">
                        <div>
                            <small class="text-muted">Session ID:</small>
                            <code class="ms-1">${this.sessionId || 'Unknown'}</code>
                        </div>
                        <div>
                            <small class="text-muted">Device:</small>
                            <code class="ms-1">${this.deviceId?.substring(0, 12) || 'Unknown'}...</code>
                        </div>
                        <div>
                            <small class="text-muted">Platform:</small>
                            <span class="badge bg-${this.platform?.toLowerCase() === 'ios' ? 'primary' : 'success'} ms-1">
                                ${this.platform || 'Unknown'}
                            </span>
                        </div>
                        <div id="sessionHealthStatus">
                            <span class="badge bg-warning">
                                <i class="bi bi-clock me-1"></i>
                                Connecting...
                            </span>
                        </div>
                    </div>
                </div>
                <div>
                    <button class="btn btn-sm btn-outline-primary" onclick="preConnectedHandler.refreshSession()">
                        <i class="bi bi-arrow-clockwise me-1"></i>
                        Refresh
                    </button>
                </div>
            </div>
        `;
        
        // Insert at the top of the main content
        const container = document.querySelector('.container-fluid');
        if (container) {
            container.insertBefore(header, container.firstChild);
        }
    }
    
    hideDeviceSelectionUI() {
        // Hide device selector dropdown
        const deviceSelector = document.querySelector('#device-selector');
        if (deviceSelector) {
            const inputGroup = deviceSelector.closest('.input-group');
            if (inputGroup) {
                inputGroup.style.display = 'none';
            }
        }
        
        // Hide refresh devices button
        const refreshButton = document.querySelector('#refreshDevices');
        if (refreshButton) {
            refreshButton.style.display = 'none';
        }
        
        // Hide connect button
        const connectButton = document.querySelector('#connect-button');
        if (connectButton) {
            connectButton.style.display = 'none';
        }
        
        // Update device connection card header
        const cardHeader = document.querySelector('.card-header h5');
        if (cardHeader && cardHeader.textContent.includes('Device Connection')) {
            cardHeader.innerHTML = `
                <i class="bi bi-phone-check me-2"></i>
                Pre-Connected Device
                <span class="badge bg-success ms-2">Active</span>
            `;
        }
        
        // Add device info display
        this.addDeviceInfoDisplay();
    }
    
    addDeviceInfoDisplay() {
        const deviceCard = document.querySelector('.card-body');
        if (deviceCard && !document.querySelector('.pre-connected-device-info')) {
            const deviceInfo = document.createElement('div');
            deviceInfo.className = 'pre-connected-device-info alert alert-light';
            deviceInfo.innerHTML = `
                <div class="row">
                    <div class="col-md-6">
                        <strong>Connected Device:</strong><br>
                        <code>${this.deviceId}</code>
                    </div>
                    <div class="col-md-6">
                        <strong>Platform:</strong><br>
                        <span class="badge bg-${this.platform?.toLowerCase() === 'ios' ? 'primary' : 'success'}">
                            ${this.platform}
                        </span>
                    </div>
                </div>
                <div class="mt-2">
                    <small class="text-muted">
                        <i class="bi bi-info-circle me-1"></i>
                        This device was automatically connected from the Appium Device Manager.
                        No manual connection is required.
                    </small>
                </div>
            `;
            
            deviceCard.appendChild(deviceInfo);
        }
    }
    
    setupSessionScreenshots() {
        // Override screenshot URL to use session-specific endpoint
        const deviceScreen = document.querySelector('#deviceScreen');
        if (deviceScreen) {
            this.updateScreenshotUrl();
            
            // Set up automatic screenshot refresh
            setInterval(() => {
                this.updateScreenshotUrl();
            }, 10000); // Refresh every 10 seconds
        }
    }
    
    updateScreenshotUrl() {
        const deviceScreen = document.querySelector('#deviceScreen');
        if (deviceScreen && this.deviceId && this.sessionId) {
            const timestamp = Date.now();
            deviceScreen.src = `/screenshot?deviceId=${this.deviceId}&sessionId=${this.sessionId}&t=${timestamp}`;
        }
    }
    
    addSessionIsolationIndicator() {
        // Add floating indicator for session isolation
        if (!document.querySelector('.session-isolation-indicator')) {
            const indicator = document.createElement('div');
            indicator.className = 'session-isolation-indicator position-fixed';
            indicator.style.cssText = `
                top: 10px;
                left: 10px;
                background: rgba(40, 167, 69, 0.9);
                color: white;
                padding: 0.5rem 1rem;
                border-radius: 20px;
                font-size: 0.8rem;
                font-weight: bold;
                z-index: 1000;
                box-shadow: 0 2px 10px rgba(0,0,0,0.2);
            `;
            indicator.innerHTML = `
                <i class="bi bi-shield-check me-2"></i>
                Session Isolated
            `;
            
            document.body.appendChild(indicator);
        }
    }
    
    async autoConnectDevice() {
        if (!this.autoConnect || !this.deviceId) {
            return;
        }
        
        console.log('Auto-connecting to pre-selected device...');
        
        try {
            // Wait for the app to be fully loaded
            await this.waitForAppReady();
            
            // Set the device in the selector (if it exists)
            this.setDeviceInSelector();
            
            // Trigger automatic connection
            await this.performAutoConnection();
            
            // Update health status
            this.updateHealthStatus('connected');
            
        } catch (error) {
            console.error('Auto-connection failed:', error);
            this.updateHealthStatus('error', error.message);
        }
    }
    
    async waitForAppReady() {
        // Wait for the main app to be loaded
        return new Promise((resolve) => {
            const checkReady = () => {
                if (window.app || document.querySelector('#deviceScreen')) {
                    resolve();
                } else {
                    setTimeout(checkReady, 500);
                }
            };
            checkReady();
        });
    }
    
    setDeviceInSelector() {
        const deviceSelector = document.querySelector('#device-selector');
        if (deviceSelector) {
            // Create option for pre-selected device
            const option = document.createElement('option');
            option.value = this.deviceId;
            option.textContent = `${this.platform} Device (${this.deviceId.substring(0, 12)}...)`;
            option.selected = true;
            option.dataset.platform = this.platform;
            option.dataset.sessionId = this.sessionId;
            
            deviceSelector.innerHTML = '';
            deviceSelector.appendChild(option);
            deviceSelector.disabled = true;
        }
    }
    
    async performAutoConnection() {
        // Use the global app instance if available
        if (window.app && typeof window.app.connectToDevice === 'function') {
            window.app.deviceId = this.deviceId;
            window.app.sessionId = this.sessionId;
            window.app.platform = this.platform;
            
            await window.app.connectToDevice();
        } else {
            // Fallback: trigger connect button if it exists
            const connectButton = document.querySelector('#connect-button');
            if (connectButton) {
                connectButton.click();
            }
        }
    }
    
    updateHealthStatus(status, message = '') {
        const healthElement = document.querySelector('#sessionHealthStatus');
        if (!healthElement) return;
        
        let badgeClass, icon, text;
        
        switch (status) {
            case 'connected':
                badgeClass = 'bg-success';
                icon = 'bi-check-circle';
                text = 'Connected';
                break;
            case 'connecting':
                badgeClass = 'bg-warning';
                icon = 'bi-clock';
                text = 'Connecting...';
                break;
            case 'error':
                badgeClass = 'bg-danger';
                icon = 'bi-exclamation-triangle';
                text = 'Error';
                break;
            default:
                badgeClass = 'bg-secondary';
                icon = 'bi-question-circle';
                text = 'Unknown';
        }
        
        healthElement.innerHTML = `
            <span class="badge ${badgeClass}" title="${message}">
                <i class="${icon} me-1"></i>
                ${text}
            </span>
        `;
    }
    
    observeUIChanges() {
        // Observe DOM changes to handle dynamically loaded content
        const observer = new MutationObserver((mutations) => {
            mutations.forEach((mutation) => {
                if (mutation.type === 'childList' && this.isPreConnected) {
                    // Re-apply UI modifications if new elements are added
                    setTimeout(() => {
                        this.hideDeviceSelectionUI();
                    }, 100);
                }
            });
        });
        
        observer.observe(document.body, {
            childList: true,
            subtree: true
        });
    }
    
    refreshSession() {
        console.log('Refreshing session...');
        
        // Update screenshot
        this.updateScreenshotUrl();
        
        // Check session health
        this.checkSessionHealth();
        
        // Refresh page if needed
        if (confirm('Refresh the entire session? This will reload the page.')) {
            window.location.reload();
        }
    }
    
    async checkSessionHealth() {
        if (!this.sessionId) return;
        
        try {
            const response = await fetch(`/api/session/${this.sessionId}/info`);
            if (response.ok) {
                const data = await response.json();
                if (data.success) {
                    this.updateHealthStatus('connected');
                } else {
                    this.updateHealthStatus('error', 'Session not found');
                }
            } else {
                this.updateHealthStatus('error', 'Health check failed');
            }
        } catch (error) {
            this.updateHealthStatus('error', 'Connection error');
        }
    }
}

// Global instance
let preConnectedHandler;

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    preConnectedHandler = new PreConnectedDeviceHandler();
});

// Add CSS for pre-connected mode
const style = document.createElement('style');
style.textContent = `
    .pre-connected-mode .hide-in-pre-connected {
        display: none !important;
    }
    
    .pre-connected-header {
        border-left: 4px solid #28a745;
        background: linear-gradient(135deg, #d4edda, #c3e6cb);
    }
    
    .pre-connected-device-info {
        border-left: 4px solid #007bff;
        background: #f8f9fa;
    }
    
    .session-isolation-indicator {
        animation: fadeInDown 0.5s ease-out;
    }
    
    @keyframes fadeInDown {
        from {
            opacity: 0;
            transform: translateY(-20px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }
`;
document.head.appendChild(style);
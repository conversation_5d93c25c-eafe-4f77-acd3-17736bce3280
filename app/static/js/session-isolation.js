/**
 * Session Isolation UI Manager
 * 
 * Handles the simplified UI that focuses on session IDs rather than device lists
 * Provides session-specific functionality and isolation
 */

class SessionIsolationManager {
    constructor() {
        this.currentSession = null;
        this.sessionCheckInterval = null;
        this.screenshotRefreshInterval = null;
        this.activityLog = [];
        
        this.init();
    }
    
    init() {
        console.log('Initializing Session Isolation Manager...');
        
        // Check if we're in session mode
        this.checkSessionMode();
        
        // Initialize session-specific UI
        this.initializeSessionUI();
        
        // Start monitoring
        this.startSessionMonitoring();
        
        // Bind event handlers
        this.bindEventHandlers();
        
        this.logActivity('Session Isolation Manager initialized');
    }
    
    checkSessionMode() {
        // Check URL parameters for session information
        const urlParams = new URLSearchParams(window.location.search);
        const sessionId = urlParams.get('sessionId');
        const deviceId = urlParams.get('deviceId');
        const platform = urlParams.get('platform');
        
        if (sessionId || deviceId) {
            console.log('Session mode detected:', { sessionId, deviceId, platform });
            this.enableSessionMode(sessionId, deviceId, platform);
        } else {
            // Check for server-side session information
            this.checkServerSession();
        }
    }
    
    async checkServerSession() {
        try {
            const response = await fetch('/api/session/current');
            if (response.ok) {
                const sessionData = await response.json();
                if (sessionData.success) {
                    console.log('Server session found:', sessionData);
                    this.enableSessionMode(sessionData.session_id, sessionData.device_id, sessionData.platform);
                }
            }
        } catch (error) {
            console.log('No server session found:', error.message);
        }
    }
    
    enableSessionMode(sessionId, deviceId, platform) {
        console.log('Enabling session mode...');
        
        // Add session mode class to body
        document.body.classList.add('session-mode');
        
        // Store session information
        this.currentSession = {
            sessionId: sessionId,
            deviceId: deviceId,
            platform: platform
        };
        
        // Update UI for session mode
        this.updateSessionUI();
        
        // Load session information
        if (sessionId) {
            this.loadSessionInfo(sessionId);
        }
        
        this.logActivity(`Session mode enabled: ${sessionId || 'Unknown'}`);
    }
    
    initializeSessionUI() {
        // Create session header if it doesn't exist
        this.createSessionHeader();
        
        // Create session controls
        this.createSessionControls();
        
        // Create session activity log
        this.createSessionActivityLog();
        
        // Hide device list elements
        this.hideDeviceListElements();
    }
    
    createSessionHeader() {
        const existingHeader = document.querySelector('.session-id-header');
        if (existingHeader) return;
        
        const header = document.createElement('div');
        header.className = 'session-id-header';
        header.innerHTML = `
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h5 class="mb-1">
                        <i class="bi bi-tag me-2"></i>
                        Isolated Session
                    </h5>
                    <div class="session-id-display">
                        <div>
                            <span class="session-id-label">Session ID:</span>
                            <span class="session-id-value" id="currentSessionId">Loading...</span>
                        </div>
                        <div class="session-status-indicator">
                            <span class="status-dot" id="sessionStatusDot"></span>
                            <span id="sessionStatusText">Connecting...</span>
                        </div>
                    </div>
                </div>
                <div class="session-controls">
                    <button class="btn btn-sm btn-light session-control-btn" onclick="sessionManager.refreshSession()">
                        <i class="bi bi-arrow-clockwise me-1"></i>
                        Refresh
                    </button>
                    <button class="btn btn-sm btn-outline-light session-control-btn" onclick="sessionManager.openSessionManager()">
                        <i class="bi bi-gear me-1"></i>
                        Manage
                    </button>
                </div>
            </div>
        `;
        
        // Insert at the top of the main content
        const mainContent = document.querySelector('.container-fluid');
        if (mainContent) {
            mainContent.insertBefore(header, mainContent.firstChild);
        }
    }
    
    createSessionControls() {
        // Add session-specific controls to the device connection card
        const deviceCard = document.querySelector('.card-header h5');
        if (deviceCard && !document.querySelector('.session-isolation-badge')) {
            const badge = document.createElement('span');
            badge.className = 'session-isolation-badge';
            badge.textContent = 'Session Isolated';
            deviceCard.parentElement.style.position = 'relative';
            deviceCard.parentElement.appendChild(badge);
        }
    }
    
    createSessionActivityLog() {
        // Find or create activity log container
        let logContainer = document.querySelector('.session-activity-log');
        
        if (!logContainer) {
            // Create new activity log section
            const logSection = document.createElement('div');
            logSection.className = 'card mt-3 show-in-session';
            logSection.innerHTML = `
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="bi bi-clock-history me-2"></i>
                        Session Activity
                    </h6>
                </div>
                <div class="card-body">
                    <div class="session-activity-log" id="sessionActivityLog">
                        <div class="text-muted">Session activity will appear here...</div>
                    </div>
                </div>
            `;
            
            // Insert after action log
            const actionLog = document.querySelector('#actionLog')?.closest('.card');
            if (actionLog) {
                actionLog.parentNode.insertBefore(logSection, actionLog.nextSibling);
            }
        }
    }
    
    hideDeviceListElements() {
        // Hide device selector
        const deviceSelector = document.querySelector('#device-selector');
        if (deviceSelector) {
            deviceSelector.closest('.input-group')?.classList.add('hide-in-session');
        }
        
        // Hide device refresh button
        const refreshButton = document.querySelector('#refreshDevices');
        if (refreshButton) {
            refreshButton.classList.add('hide-in-session');
        }
    }
    
    updateSessionUI() {
        if (!this.currentSession) return;
        
        // Update session ID display
        const sessionIdElement = document.querySelector('#currentSessionId');
        if (sessionIdElement) {
            sessionIdElement.textContent = this.currentSession.sessionId || 'Unknown';
        }
        
        // Update page title
        if (this.currentSession.sessionId) {
            document.title = `Session ${this.currentSession.sessionId.substring(0, 12)}... - Mobile Automation`;
        }
        
        // Update device screen for session-specific screenshots
        this.updateSessionScreenshot();
    }
    
    updateSessionScreenshot() {
        if (!this.currentSession) return;
        
        const deviceScreen = document.querySelector('#deviceScreen');
        if (deviceScreen) {
            const timestamp = Date.now();
            const sessionId = this.currentSession.sessionId;
            const deviceId = this.currentSession.deviceId;
            
            if (sessionId && deviceId) {
                deviceScreen.src = `/screenshot?sessionId=${sessionId}&deviceId=${deviceId}&t=${timestamp}`;
                console.log('Updated session screenshot:', deviceScreen.src);
            }
        }
    }
    
    async loadSessionInfo(sessionId) {
        try {
            const response = await fetch(`/api/session/${sessionId}/info`);
            if (response.ok) {
                const sessionData = await response.json();
                if (sessionData.success) {
                    this.updateSessionStatus(sessionData);
                    this.logActivity(`Session information loaded: ${sessionId}`);
                }
            }
        } catch (error) {
            console.error('Error loading session info:', error);
            this.logActivity('Failed to load session information', 'error');
        }
    }
    
    updateSessionStatus(sessionData) {
        // Update status indicator
        const statusDot = document.querySelector('#sessionStatusDot');
        const statusText = document.querySelector('#sessionStatusText');
        
        if (statusDot && statusText) {
            const status = sessionData.status || 'unknown';
            
            // Remove existing status classes
            statusDot.className = 'status-dot';
            statusDot.classList.add(`status-${status}`);
            
            statusText.textContent = status.charAt(0).toUpperCase() + status.slice(1);
        }
        
        // Update session data
        this.currentSession = {
            ...this.currentSession,
            ...sessionData
        };
    }
    
    startSessionMonitoring() {
        // Check session status every 30 seconds
        this.sessionCheckInterval = setInterval(() => {
            if (this.currentSession?.sessionId) {
                this.loadSessionInfo(this.currentSession.sessionId);
            }
        }, 30000);
        
        // Refresh screenshot every 10 seconds if session is running
        this.screenshotRefreshInterval = setInterval(() => {
            if (this.currentSession?.status === 'running') {
                this.updateSessionScreenshot();
            }
        }, 10000);
    }
    
    bindEventHandlers() {
        // Override device connection behavior in session mode
        const connectButton = document.querySelector('#connect-button');
        if (connectButton) {
            connectButton.addEventListener('click', (e) => {
                if (document.body.classList.contains('session-mode')) {
                    e.preventDefault();
                    this.handleSessionConnect();
                }
            });
        }
        
        // Override screenshot refresh in session mode
        const refreshScreenBtn = document.querySelector('#refreshScreenBtn');
        if (refreshScreenBtn) {
            refreshScreenBtn.addEventListener('click', (e) => {
                if (document.body.classList.contains('session-mode')) {
                    e.preventDefault();
                    this.updateSessionScreenshot();
                    this.logActivity('Screenshot refreshed manually');
                }
            });
        }
    }
    
    async handleSessionConnect() {
        if (!this.currentSession?.sessionId) {
            this.logActivity('No session available to connect', 'error');
            return;
        }
        
        try {
            this.logActivity('Connecting to session...');
            
            // For session mode, we're already connected - just update status
            await this.loadSessionInfo(this.currentSession.sessionId);
            
            // Update UI to show connected state
            this.updateConnectedState();
            
            this.logActivity('Session connected successfully');
            
        } catch (error) {
            console.error('Session connection error:', error);
            this.logActivity('Session connection failed', 'error');
        }
    }
    
    updateConnectedState() {
        // Update connect button
        const connectButton = document.querySelector('#connect-button');
        if (connectButton) {
            connectButton.textContent = 'Connected';
            connectButton.disabled = true;
            connectButton.classList.remove('btn-primary');
            connectButton.classList.add('btn-success');
        }
        
        // Enable action buttons
        const actionButtons = document.querySelectorAll('[data-requires-connection]');
        actionButtons.forEach(btn => {
            btn.disabled = false;
        });
    }
    
    logActivity(message, type = 'info') {
        const timestamp = new Date().toLocaleTimeString();
        
        const logEntry = {
            message: message,
            type: type,
            timestamp: timestamp
        };
        
        this.activityLog.unshift(logEntry);
        
        // Keep only last 50 entries
        if (this.activityLog.length > 50) {
            this.activityLog = this.activityLog.slice(0, 50);
        }
        
        // Update UI log
        this.updateActivityLogUI();
        
        console.log(`[Session Activity] ${message}`);
    }
    
    updateActivityLogUI() {
        const logContainer = document.querySelector('#sessionActivityLog');
        if (!logContainer) return;
        
        // Clear existing content
        logContainer.innerHTML = '';
        
        // Add recent entries
        this.activityLog.slice(0, 10).forEach(entry => {
            const entryElement = document.createElement('div');
            entryElement.className = 'session-activity-entry';
            
            const typeIcon = entry.type === 'error' ? 'bi-exclamation-triangle text-danger' :
                           entry.type === 'warning' ? 'bi-exclamation-circle text-warning' :
                           'bi-info-circle text-primary';
            
            entryElement.innerHTML = `
                <span>
                    <i class="bi ${typeIcon} me-2"></i>
                    ${entry.message}
                </span>
                <span class="session-activity-time">${entry.timestamp}</span>
            `;
            
            logContainer.appendChild(entryElement);
        });
    }
    
    refreshSession() {
        this.logActivity('Refreshing session...');
        
        if (this.currentSession?.sessionId) {
            this.loadSessionInfo(this.currentSession.sessionId);
        }
        
        this.updateSessionScreenshot();
    }
    
    openSessionManager() {
        // Open session manager in new tab
        const url = '/api/session/manager';
        window.open(url, '_blank');
        this.logActivity('Opened session manager');
    }
    
    cleanup() {
        if (this.sessionCheckInterval) {
            clearInterval(this.sessionCheckInterval);
        }
        
        if (this.screenshotRefreshInterval) {
            clearInterval(this.screenshotRefreshInterval);
        }
    }
}

// Global session manager instance
let sessionManager;

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    sessionManager = new SessionIsolationManager();
});

// Cleanup on page unload
window.addEventListener('beforeunload', function() {
    if (sessionManager) {
        sessionManager.cleanup();
    }
});
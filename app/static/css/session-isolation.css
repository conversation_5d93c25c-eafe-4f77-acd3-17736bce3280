/* Session Isolation UI Styles */

/* Hide device list by default - focus on session ID */
.device-list-container {
    display: none !important;
}

/* Prominent session ID display */
.session-id-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 1rem;
    border-radius: 8px;
    margin-bottom: 1rem;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.session-id-display {
    font-family: 'Courier New', monospace;
    font-size: 1.2rem;
    font-weight: bold;
    color: #007bff;
    background: rgba(255,255,255,0.9);
    padding: 0.75rem 1rem;
    border-radius: 6px;
    border-left: 4px solid #007bff;
    margin: 0.5rem 0;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.session-id-label {
    font-size: 0.9rem;
    color: #6c757d;
    margin-right: 0.5rem;
}

.session-id-value {
    font-family: 'Courier New', monospace;
    font-weight: bold;
    color: #007bff;
}

.session-status-indicator {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.status-dot {
    width: 10px;
    height: 10px;
    border-radius: 50%;
    display: inline-block;
}

.status-running {
    background-color: #28a745;
    animation: pulse 2s infinite;
}

.status-stopped {
    background-color: #dc3545;
}

.status-created {
    background-color: #ffc107;
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
}

/* Enhanced device connection card for session mode */
.session-mode .device-connection-card {
    border-left: 4px solid #007bff;
}

.session-mode .device-connection-card .card-header {
    background: linear-gradient(135deg, #007bff, #0056b3);
    color: white;
}

/* Hide device selector in session mode */
.session-mode .device-selector-container {
    display: none !important;
}

/* Show session info instead */
.session-info-container {
    display: none;
}

.session-mode .session-info-container {
    display: block !important;
}

/* Session-specific device screen styling */
.session-mode .device-screen-container {
    border: 2px solid #007bff;
    border-radius: 12px;
    background: linear-gradient(145deg, #f8f9fa, #e9ecef);
}

.session-mode .device-screen {
    border-radius: 8px;
    box-shadow: 0 8px 25px rgba(0,123,255,0.15);
}

/* Session controls styling */
.session-controls {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 1rem;
    margin-top: 1rem;
}

.session-control-btn {
    margin: 0.25rem;
    min-width: 120px;
}

/* Session activity log */
.session-activity-log {
    max-height: 200px;
    overflow-y: auto;
    background: #f8f9fa;
    border-radius: 6px;
    padding: 0.75rem;
    font-family: 'Courier New', monospace;
    font-size: 0.85rem;
}

.session-activity-entry {
    padding: 0.25rem 0;
    border-bottom: 1px solid #dee2e6;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.session-activity-entry:last-child {
    border-bottom: none;
}

.session-activity-time {
    color: #6c757d;
    font-size: 0.75rem;
}

/* Session isolation badge */
.session-isolation-badge {
    position: absolute;
    top: 10px;
    right: 10px;
    background: rgba(0,123,255,0.9);
    color: white;
    padding: 0.25rem 0.5rem;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: bold;
    z-index: 10;
}

/* Hide elements that are not relevant in session mode */
.session-mode .hide-in-session {
    display: none !important;
}

/* Show elements only in session mode */
.show-in-session {
    display: none !important;
}

.session-mode .show-in-session {
    display: block !important;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .session-id-display {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
    }
    
    .session-status-indicator {
        align-self: flex-end;
    }
}

/* Animation for session state changes */
.session-state-transition {
    transition: all 0.3s ease-in-out;
}

/* Session URL display */
.session-url-display {
    background: #e9ecef;
    border-radius: 6px;
    padding: 0.5rem;
    font-family: 'Courier New', monospace;
    font-size: 0.85rem;
    word-break: break-all;
    margin-top: 0.5rem;
}

.session-url-copy-btn {
    margin-left: 0.5rem;
    padding: 0.25rem 0.5rem;
    font-size: 0.75rem;
}

/* Session metrics display */
.session-metrics {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 1rem;
    margin-top: 1rem;
}

.session-metric {
    text-align: center;
    padding: 0.75rem;
    background: white;
    border-radius: 6px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.session-metric-value {
    font-size: 1.5rem;
    font-weight: bold;
    color: #007bff;
}

.session-metric-label {
    font-size: 0.8rem;
    color: #6c757d;
    margin-top: 0.25rem;
}
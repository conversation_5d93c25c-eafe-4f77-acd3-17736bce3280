<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Session Manager - Mobile App Automation Tool</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.css">
    <style>
        .session-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem 0;
            margin-bottom: 2rem;
        }
        
        .session-id-display {
            font-family: 'Courier New', monospace;
            font-size: 1.5rem;
            font-weight: bold;
            color: #007bff;
            background: #f8f9fa;
            padding: 1rem;
            border-radius: 8px;
            border-left: 4px solid #007bff;
            margin-bottom: 1rem;
        }
        
        .session-card {
            border: none;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            transition: transform 0.2s;
        }
        
        .session-card:hover {
            transform: translateY(-2px);
        }
        
        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            display: inline-block;
            margin-right: 8px;
        }
        
        .status-running {
            background-color: #28a745;
            animation: pulse 2s infinite;
        }
        
        .status-stopped {
            background-color: #dc3545;
        }
        
        .status-created {
            background-color: #ffc107;
        }
        
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }
        
        .device-screen-container {
            background: #f8f9fa;
            border-radius: 12px;
            padding: 1rem;
            text-align: center;
            min-height: 400px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .device-screen {
            max-width: 100%;
            max-height: 500px;
            border-radius: 8px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.2);
        }
        
        .session-info-badge {
            background: rgba(255,255,255,0.9);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 0.5rem 1rem;
            margin: 0.25rem;
            display: inline-block;
        }
        
        .hidden {
            display: none !important;
        }
    </style>
</head>
<body>
    <div class="session-header">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h1 class="mb-0">
                        <i class="bi bi-phone me-2"></i>
                        Mobile Automation Session
                    </h1>
                    <p class="mb-0 mt-2 opacity-75">Isolated device testing environment</p>
                </div>
                <div class="col-md-4 text-end">
                    <button class="btn btn-light btn-lg" onclick="refreshSession()">
                        <i class="bi bi-arrow-clockwise me-2"></i>
                        Refresh
                    </button>
                </div>
            </div>
        </div>
    </div>

    <div class="container">
        <!-- Session ID Display (Primary Identifier) -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="session-id-display">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <i class="bi bi-tag me-2"></i>
                            Session ID: <span id="currentSessionId">Loading...</span>
                        </div>
                        <div>
                            <span class="status-indicator" id="sessionStatusIndicator"></span>
                            <span id="sessionStatusText">Connecting...</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Device Screen and Controls -->
        <div class="row">
            <!-- Device Screen -->
            <div class="col-lg-6">
                <div class="card session-card mb-4">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0">
                            <i class="bi bi-display me-2"></i>
                            Device Screen
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="device-screen-container">
                            <img id="deviceScreen" 
                                 src="/static/img/no_device.png" 
                                 class="device-screen" 
                                 alt="Device Screen"
                                 onerror="handleScreenshotError(this)">
                        </div>
                        <div class="mt-3 text-center">
                            <button class="btn btn-outline-primary btn-sm me-2" onclick="refreshScreenshot()">
                                <i class="bi bi-arrow-clockwise me-1"></i>
                                Refresh Screen
                            </button>
                            <button class="btn btn-outline-secondary btn-sm" onclick="openWebInspector()">
                                <i class="bi bi-window me-1"></i>
                                Web Inspector
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Session Information and Controls -->
            <div class="col-lg-6">
                <!-- Session Information -->
                <div class="card session-card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="bi bi-info-circle me-2"></i>
                            Session Information
                        </h5>
                    </div>
                    <div class="card-body">
                        <div id="sessionInfo">
                            <div class="session-info-badge">
                                <strong>Platform:</strong> <span id="sessionPlatform">-</span>
                            </div>
                            <div class="session-info-badge">
                                <strong>Flask Port:</strong> <span id="sessionFlaskPort">-</span>
                            </div>
                            <div class="session-info-badge">
                                <strong>Appium Port:</strong> <span id="sessionAppiumPort">-</span>
                            </div>
                            <div class="session-info-badge">
                                <strong>WDA Port:</strong> <span id="sessionWdaPort">-</span>
                            </div>
                        </div>
                        
                        <!-- Device List Hidden by Default -->
                        <div id="deviceListContainer" class="hidden mt-3">
                            <h6>Connected Device:</h6>
                            <div class="alert alert-info">
                                <i class="bi bi-phone me-2"></i>
                                Device ID: <span id="deviceId">-</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Session Controls -->
                <div class="card session-card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="bi bi-gear me-2"></i>
                            Session Controls
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="d-grid gap-2">
                            <button class="btn btn-success" onclick="startSession()" id="startSessionBtn">
                                <i class="bi bi-play-fill me-2"></i>
                                Start Session
                            </button>
                            <button class="btn btn-warning" onclick="restartSession()" id="restartSessionBtn" disabled>
                                <i class="bi bi-arrow-clockwise me-2"></i>
                                Restart Session
                            </button>
                            <button class="btn btn-danger" onclick="stopSession()" id="stopSessionBtn" disabled>
                                <i class="bi bi-stop-fill me-2"></i>
                                Stop Session
                            </button>
                        </div>
                        
                        <hr>
                        
                        <div class="text-center">
                            <button class="btn btn-outline-primary btn-sm" onclick="openMainInterface()">
                                <i class="bi bi-box-arrow-up-right me-1"></i>
                                Open Main Interface
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Action Log (Simplified) -->
        <div class="row mt-4">
            <div class="col-12">
                <div class="card session-card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="bi bi-list-ul me-2"></i>
                            Session Activity Log
                        </h5>
                    </div>
                    <div class="card-body">
                        <div id="sessionLog" style="max-height: 300px; overflow-y: auto;">
                            <div class="text-muted">Session activity will appear here...</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Session management variables
        let currentSession = null;
        let screenshotRefreshInterval = null;
        
        // Initialize session manager
        document.addEventListener('DOMContentLoaded', function() {
            initializeSession();
            startScreenshotRefresh();
        });
        
        function initializeSession() {
            // Get session information from URL parameters or API
            const urlParams = new URLSearchParams(window.location.search);
            const sessionId = urlParams.get('sessionId');
            const deviceId = urlParams.get('deviceId');
            const platform = urlParams.get('platform');
            
            if (sessionId) {
                document.getElementById('currentSessionId').textContent = sessionId;
                loadSessionInfo(sessionId);
            } else {
                // Try to get session info from API
                fetchSessionInfo();
            }
            
            logActivity('Session manager initialized');
        }
        
        async function loadSessionInfo(sessionId) {
            try {
                const response = await fetch(`/api/session/${sessionId}/info`);
                if (response.ok) {
                    const sessionData = await response.json();
                    updateSessionDisplay(sessionData);
                } else {
                    logActivity('Failed to load session information', 'error');
                }
            } catch (error) {
                console.error('Error loading session info:', error);
                logActivity('Error loading session information', 'error');
            }
        }
        
        async function fetchSessionInfo() {
            try {
                const response = await fetch('/api/session/current');
                if (response.ok) {
                    const sessionData = await response.json();
                    updateSessionDisplay(sessionData);
                } else {
                    logActivity('No active session found', 'warning');
                }
            } catch (error) {
                console.error('Error fetching session info:', error);
                logActivity('Error fetching session information', 'error');
            }
        }
        
        function updateSessionDisplay(sessionData) {
            currentSession = sessionData;
            
            // Update session ID
            document.getElementById('currentSessionId').textContent = sessionData.session_id || 'Unknown';
            
            // Update session info
            document.getElementById('sessionPlatform').textContent = sessionData.platform || '-';
            document.getElementById('sessionFlaskPort').textContent = sessionData.ports?.flask || '-';
            document.getElementById('sessionAppiumPort').textContent = sessionData.ports?.appium || '-';
            document.getElementById('sessionWdaPort').textContent = sessionData.ports?.wda || '-';
            
            // Update device info (hidden by default)
            document.getElementById('deviceId').textContent = sessionData.device_id || '-';
            
            // Update status
            updateSessionStatus(sessionData.status || 'unknown');
            
            // Update screenshot URL
            updateScreenshotUrl();
            
            logActivity(`Session information updated: ${sessionData.session_id}`);
        }
        
        function updateSessionStatus(status) {
            const indicator = document.getElementById('sessionStatusIndicator');
            const text = document.getElementById('sessionStatusText');
            const startBtn = document.getElementById('startSessionBtn');
            const restartBtn = document.getElementById('restartSessionBtn');
            const stopBtn = document.getElementById('stopSessionBtn');
            
            // Remove all status classes
            indicator.className = 'status-indicator';
            
            // Add appropriate status class
            indicator.classList.add(`status-${status}`);
            text.textContent = status.charAt(0).toUpperCase() + status.slice(1);
            
            // Update button states
            switch (status) {
                case 'running':
                    startBtn.disabled = true;
                    restartBtn.disabled = false;
                    stopBtn.disabled = false;
                    break;
                case 'stopped':
                case 'created':
                    startBtn.disabled = false;
                    restartBtn.disabled = true;
                    stopBtn.disabled = true;
                    break;
                default:
                    startBtn.disabled = false;
                    restartBtn.disabled = true;
                    stopBtn.disabled = true;
            }
        }
        
        function updateScreenshotUrl() {
            if (!currentSession) return;
            
            const deviceScreen = document.getElementById('deviceScreen');
            const timestamp = Date.now();
            const sessionId = currentSession.session_id;
            const deviceId = currentSession.device_id;
            
            // Use session-specific screenshot endpoint
            deviceScreen.src = `/screenshot?sessionId=${sessionId}&deviceId=${deviceId}&t=${timestamp}`;
        }
        
        function startScreenshotRefresh() {
            // Refresh screenshot every 5 seconds when session is active
            screenshotRefreshInterval = setInterval(() => {
                if (currentSession && currentSession.status === 'running') {
                    updateScreenshotUrl();
                }
            }, 5000);
        }
        
        function refreshScreenshot() {
            updateScreenshotUrl();
            logActivity('Screenshot refreshed manually');
        }
        
        function handleScreenshotError(img) {
            console.error('Screenshot failed to load');
            img.src = '/static/img/no_device.png';
            logActivity('Screenshot failed to load', 'error');
        }
        
        async function startSession() {
            if (!currentSession) {
                logActivity('No session available to start', 'error');
                return;
            }
            
            try {
                const response = await fetch(`/api/session/${currentSession.session_id}/start`, {
                    method: 'POST'
                });
                
                if (response.ok) {
                    logActivity('Session start requested');
                    setTimeout(() => fetchSessionInfo(), 2000); // Refresh after 2 seconds
                } else {
                    logActivity('Failed to start session', 'error');
                }
            } catch (error) {
                console.error('Error starting session:', error);
                logActivity('Error starting session', 'error');
            }
        }
        
        async function stopSession() {
            if (!currentSession) {
                logActivity('No session available to stop', 'error');
                return;
            }
            
            try {
                const response = await fetch(`/api/session/${currentSession.session_id}/stop`, {
                    method: 'POST'
                });
                
                if (response.ok) {
                    logActivity('Session stop requested');
                    setTimeout(() => fetchSessionInfo(), 1000); // Refresh after 1 second
                } else {
                    logActivity('Failed to stop session', 'error');
                }
            } catch (error) {
                console.error('Error stopping session:', error);
                logActivity('Error stopping session', 'error');
            }
        }
        
        function restartSession() {
            logActivity('Restarting session...');
            stopSession();
            setTimeout(() => startSession(), 3000); // Start after 3 seconds
        }
        
        function refreshSession() {
            logActivity('Refreshing session information...');
            fetchSessionInfo();
        }
        
        function openMainInterface() {
            if (currentSession && currentSession.status === 'running') {
                const url = `http://localhost:${currentSession.ports.flask}?sessionId=${currentSession.session_id}&deviceId=${currentSession.device_id}&platform=${currentSession.platform}`;
                window.open(url, '_blank');
                logActivity('Opened main interface in new tab');
            } else {
                logActivity('Session must be running to open main interface', 'warning');
            }
        }
        
        function openWebInspector() {
            if (currentSession && currentSession.status === 'running') {
                const url = `http://localhost:${currentSession.ports.appium}/wd/hub/inspector`;
                window.open(url, '_blank');
                logActivity('Opened Web Inspector in new tab');
            } else {
                logActivity('Session must be running to open Web Inspector', 'warning');
            }
        }
        
        function logActivity(message, type = 'info') {
            const logContainer = document.getElementById('sessionLog');
            const timestamp = new Date().toLocaleTimeString();
            
            const logEntry = document.createElement('div');
            logEntry.className = `mb-2 p-2 border-start border-3 border-${type === 'error' ? 'danger' : type === 'warning' ? 'warning' : 'primary'}`;
            
            const icon = type === 'error' ? 'bi-exclamation-triangle' : 
                        type === 'warning' ? 'bi-exclamation-circle' : 
                        'bi-info-circle';
            
            logEntry.innerHTML = `
                <div class="d-flex justify-content-between">
                    <span><i class="bi ${icon} me-2"></i>${message}</span>
                    <small class="text-muted">${timestamp}</small>
                </div>
            `;
            
            logContainer.insertBefore(logEntry, logContainer.firstChild);
            
            // Keep only last 20 entries
            while (logContainer.children.length > 20) {
                logContainer.removeChild(logContainer.lastChild);
            }
        }
        
        // Auto-refresh session info every 30 seconds
        setInterval(() => {
            if (currentSession) {
                fetchSessionInfo();
            }
        }, 30000);
    </script>
</body>
</html>
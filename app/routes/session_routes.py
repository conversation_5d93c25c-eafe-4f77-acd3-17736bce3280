"""
Session management routes for enhanced device isolation
"""
import os
import sys
import json
import logging
from pathlib import Path
from flask import Blueprint, jsonify, request, render_template

# Add parent directories to path for imports
current_dir = Path(__file__).resolve().parent
app_dir = current_dir.parent
root_dir = app_dir.parent

sys.path.insert(0, str(root_dir))
sys.path.insert(0, str(app_dir))

from session_isolation_manager import session_manager
from enhanced_device_manager import device_manager

logger = logging.getLogger(__name__)

session_bp = Blueprint('session', __name__, url_prefix='/api/session')

@session_bp.route('/discover', methods=['POST'])
def discover_devices_and_create_sessions():
    """
    Discover connected devices and create isolated sessions for each
    """
    try:
        sessions = device_manager.discover_and_create_sessions()
        
        return jsonify({
            "success": True,
            "sessions": sessions,
            "count": len(sessions),
            "message": f"Created {len(sessions)} isolated sessions"
        })
        
    except Exception as e:
        logger.error(f"Error discovering devices and creating sessions: {e}")
        return jsonify({
            "success": False,
            "error": str(e)
        }), 500

@session_bp.route('/list', methods=['GET'])
def list_sessions():
    """
    List all active sessions
    """
    try:
        sessions = device_manager.list_sessions()
        
        return jsonify({
            "success": True,
            "sessions": sessions,
            "count": len(sessions)
        })
        
    except Exception as e:
        logger.error(f"Error listing sessions: {e}")
        return jsonify({
            "success": False,
            "error": str(e)
        }), 500

@session_bp.route('/<session_id>/info', methods=['GET'])
def get_session_info(session_id):
    """
    Get detailed information about a specific session
    """
    try:
        session_status = device_manager.get_session_status(session_id)
        
        if not session_status:
            return jsonify({
                "success": False,
                "error": "Session not found"
            }), 404
        
        # Get additional session details
        session_config = session_manager.get_session(session_id)
        
        if session_config:
            session_status.update({
                "created_at": session_config.get("created_at"),
                "directories": session_config.get("directories"),
                "databases": session_config.get("databases")
            })
        
        return jsonify({
            "success": True,
            **session_status
        })
        
    except Exception as e:
        logger.error(f"Error getting session info for {session_id}: {e}")
        return jsonify({
            "success": False,
            "error": str(e)
        }), 500

@session_bp.route('/current', methods=['GET'])
def get_current_session():
    """
    Get current session information based on environment or request context
    """
    try:
        # Try to get session ID from environment variables
        session_id = os.environ.get('SESSION_ID')
        device_id = os.environ.get('SELECTED_DEVICE_ID')
        
        if session_id:
            return get_session_info(session_id)
        elif device_id:
            # Find session by device ID
            session = session_manager.get_session_by_device(device_id)
            if session:
                return get_session_info(session['session_id'])
        
        # No current session found
        return jsonify({
            "success": False,
            "error": "No current session found"
        }), 404
        
    except Exception as e:
        logger.error(f"Error getting current session: {e}")
        return jsonify({
            "success": False,
            "error": str(e)
        }), 500

@session_bp.route('/<session_id>/start', methods=['POST'])
def start_session(session_id):
    """
    Start a specific session
    """
    try:
        success = device_manager.start_session(session_id)
        
        if success:
            return jsonify({
                "success": True,
                "message": f"Session {session_id} started successfully"
            })
        else:
            return jsonify({
                "success": False,
                "error": "Failed to start session"
            }), 500
            
    except Exception as e:
        logger.error(f"Error starting session {session_id}: {e}")
        return jsonify({
            "success": False,
            "error": str(e)
        }), 500

@session_bp.route('/<session_id>/stop', methods=['POST'])
def stop_session(session_id):
    """
    Stop a specific session
    """
    try:
        success = device_manager.stop_session(session_id)
        
        if success:
            return jsonify({
                "success": True,
                "message": f"Session {session_id} stopped successfully"
            })
        else:
            return jsonify({
                "success": False,
                "error": "Failed to stop session"
            }), 500
            
    except Exception as e:
        logger.error(f"Error stopping session {session_id}: {e}")
        return jsonify({
            "success": False,
            "error": str(e)
        }), 500

@session_bp.route('/<session_id>/restart', methods=['POST'])
def restart_session(session_id):
    """
    Restart a specific session
    """
    try:
        # Stop the session first
        stop_success = device_manager.stop_session(session_id)
        
        if stop_success:
            # Wait a moment then start again
            import time
            time.sleep(2)
            
            start_success = device_manager.start_session(session_id)
            
            if start_success:
                return jsonify({
                    "success": True,
                    "message": f"Session {session_id} restarted successfully"
                })
            else:
                return jsonify({
                    "success": False,
                    "error": "Failed to restart session - start failed"
                }), 500
        else:
            return jsonify({
                "success": False,
                "error": "Failed to restart session - stop failed"
            }), 500
            
    except Exception as e:
        logger.error(f"Error restarting session {session_id}: {e}")
        return jsonify({
            "success": False,
            "error": str(e)
        }), 500

@session_bp.route('/<session_id>/cleanup', methods=['POST'])
def cleanup_session(session_id):
    """
    Clean up a session and optionally remove its data
    """
    try:
        remove_data = request.json.get('remove_data', False) if request.json else False
        
        success = session_manager.cleanup_session(session_id, remove_data)
        
        if success:
            return jsonify({
                "success": True,
                "message": f"Session {session_id} cleaned up successfully"
            })
        else:
            return jsonify({
                "success": False,
                "error": "Failed to cleanup session"
            }), 500
            
    except Exception as e:
        logger.error(f"Error cleaning up session {session_id}: {e}")
        return jsonify({
            "success": False,
            "error": str(e)
        }), 500

@session_bp.route('/<session_id>/screenshot', methods=['GET'])
def get_session_screenshot(session_id):
    """
    Get screenshot URL for a specific session
    """
    try:
        screenshot_url = device_manager.get_session_screenshot_url(session_id)
        
        if screenshot_url:
            return jsonify({
                "success": True,
                "screenshot_url": screenshot_url
            })
        else:
            return jsonify({
                "success": False,
                "error": "Session not running or screenshot not available"
            }), 404
            
    except Exception as e:
        logger.error(f"Error getting screenshot for session {session_id}: {e}")
        return jsonify({
            "success": False,
            "error": str(e)
        }), 500

@session_bp.route('/manager', methods=['GET'])
def session_manager_ui():
    """
    Render the session manager UI
    """
    try:
        return render_template('session_manager.html')
    except Exception as e:
        logger.error(f"Error rendering session manager UI: {e}")
        return f"Error loading session manager: {str(e)}", 500

@session_bp.route('/summary', methods=['GET'])
def session_summary():
    """
    Get HTML summary of all sessions
    """
    try:
        html_content = device_manager.generate_session_summary_html()
        return html_content, 200, {'Content-Type': 'text/html'}
    except Exception as e:
        logger.error(f"Error generating session summary: {e}")
        return f"Error generating session summary: {str(e)}", 500

@session_bp.route('/cleanup-terminated', methods=['POST'])
def cleanup_terminated_sessions():
    """
    Clean up all terminated sessions
    """
    try:
        cleaned_count = device_manager.cleanup_terminated_sessions()
        
        return jsonify({
            "success": True,
            "cleaned_count": cleaned_count,
            "message": f"Cleaned up {cleaned_count} terminated sessions"
        })
        
    except Exception as e:
        logger.error(f"Error cleaning up terminated sessions: {e}")
        return jsonify({
            "success": False,
            "error": str(e)
        }), 500

@session_bp.route('/health', methods=['GET'])
def session_health_check():
    """
    Health check for session management system
    """
    try:
        sessions = device_manager.list_sessions()
        active_count = len([s for s in sessions if s['status'] == 'running'])
        
        return jsonify({
            "success": True,
            "status": "healthy",
            "total_sessions": len(sessions),
            "active_sessions": active_count,
            "session_manager": "operational"
        })
        
    except Exception as e:
        logger.error(f"Session health check failed: {e}")
        return jsonify({
            "success": False,
            "status": "unhealthy",
            "error": str(e)
        }), 500
"""
Device Manager Routes for the main automation app

These routes handle integration with the AppiumDeviceManager
and support pre-connected device functionality.
"""

import os
import sys
import json
import logging
from pathlib import Path
from flask import Blueprint, jsonify, request, render_template

# Add parent directories to path for imports
current_dir = Path(__file__).resolve().parent
app_dir = current_dir.parent
root_dir = app_dir.parent

sys.path.insert(0, str(root_dir))

logger = logging.getLogger(__name__)

device_manager_bp = Blueprint('device_manager', __name__, url_prefix='/api/device-manager')

@device_manager_bp.route('/pre-connect-info', methods=['GET'])
def get_pre_connect_info():
    """
    Get pre-connection information from environment variables
    """
    try:
        # Get pre-connection info from environment
        device_id = os.environ.get('SELECTED_DEVICE_ID')
        platform = os.environ.get('SELECTED_PLATFORM', 'iOS')
        session_id = os.environ.get('SESSION_ID')
        
        if device_id:
            return jsonify({
                "success": True,
                "pre_connected": True,
                "device_id": device_id,
                "platform": platform,
                "session_id": session_id,
                "auto_connect": True,
                "hide_device_list": True
            })
        else:
            return jsonify({
                "success": True,
                "pre_connected": False
            })
            
    except Exception as e:
        logger.error(f"Error getting pre-connect info: {e}")
        return jsonify({
            "success": False,
            "error": str(e)
        }), 500

@device_manager_bp.route('/session-status', methods=['GET'])
def get_session_status():
    """
    Get current session status for device manager integration
    """
    try:
        session_id = os.environ.get('SESSION_ID')
        device_id = os.environ.get('SELECTED_DEVICE_ID')
        platform = os.environ.get('SELECTED_PLATFORM')
        
        if session_id:
            # Try to get more detailed session info
            try:
                from session_isolation_manager import session_manager
                session_config = session_manager.get_session(session_id)
                
                if session_config:
                    return jsonify({
                        "success": True,
                        "session_active": True,
                        "session_id": session_id,
                        "device_id": device_id,
                        "platform": platform,
                        "status": session_config.get("status", "unknown"),
                        "ports": session_config.get("ports", {}),
                        "created_at": session_config.get("created_at")
                    })
            except ImportError:
                # Session manager not available, return basic info
                pass
            
            return jsonify({
                "success": True,
                "session_active": True,
                "session_id": session_id,
                "device_id": device_id,
                "platform": platform,
                "status": "running"
            })
        else:
            return jsonify({
                "success": True,
                "session_active": False
            })
            
    except Exception as e:
        logger.error(f"Error getting session status: {e}")
        return jsonify({
            "success": False,
            "error": str(e)
        }), 500

@device_manager_bp.route('/auto-connect', methods=['POST'])
def auto_connect_device():
    """
    Handle automatic device connection for pre-connected sessions
    """
    try:
        # Get device info from environment or request
        device_id = os.environ.get('SELECTED_DEVICE_ID')
        platform = os.environ.get('SELECTED_PLATFORM', 'iOS')
        session_id = os.environ.get('SESSION_ID')
        
        if not device_id:
            # Try to get from request
            data = request.get_json() or {}
            device_id = data.get('device_id')
            platform = data.get('platform', 'iOS')
            session_id = data.get('session_id')
        
        if not device_id:
            return jsonify({
                "success": False,
                "error": "No device ID available for auto-connection"
            }), 400
        
        # Set up device connection using existing connection logic
        # This will be handled by the main app's connection system
        
        return jsonify({
            "success": True,
            "message": "Auto-connection initiated",
            "device_id": device_id,
            "platform": platform,
            "session_id": session_id
        })
        
    except Exception as e:
        logger.error(f"Error in auto-connect: {e}")
        return jsonify({
            "success": False,
            "error": str(e)
        }), 500

@device_manager_bp.route('/health', methods=['GET'])
def device_manager_health():
    """
    Health check for device manager integration
    """
    try:
        # Check if this is a device manager launched session
        session_id = os.environ.get('SESSION_ID')
        device_id = os.environ.get('SELECTED_DEVICE_ID')
        
        is_device_manager_session = bool(session_id and device_id)
        
        return jsonify({
            "success": True,
            "status": "healthy",
            "device_manager_session": is_device_manager_session,
            "session_id": session_id,
            "device_id": device_id,
            "timestamp": "2024-01-01T00:00:00Z"
        })
        
    except Exception as e:
        logger.error(f"Device manager health check failed: {e}")
        return jsonify({
            "success": False,
            "status": "unhealthy",
            "error": str(e)
        }), 500
# Session Isolation Implementation

## Overview

This document describes the comprehensive session isolation implementation for the Mobile App Automation Tool. The solution addresses the session isolation issues when multiple devices are connected, ensuring each device operates independently with complete separation of resources and configuration.

## Key Features Implemented

### 1. Complete Session Isolation

Each connected device gets its own isolated session with:
- **Separate Database Files**: Each session has its own SQLite databases for test execution, global values, and settings
- **Isolated Environment Variables**: Session-specific environment variables prevent cross-contamination
- **Dedicated Port Allocation**: Unique Flask, Appium, and WebDriverAgent ports for each session
- **Session-Specific Directories**: Separate folders for screenshots, reports, and temporary files

### 2. Simplified UI Design

The interface has been redesigned to focus on session management:
- **Session ID as Primary Identifier**: Prominently displayed session IDs instead of device lists
- **Hidden Device Lists**: Device enumeration is handled automatically in the background
- **Session Status Indicators**: Real-time status display for each session
- **Clean Session Controls**: Simplified start/stop/restart controls per session

### 3. Enhanced Screenshot Accuracy

Screenshot handling has been improved for session isolation:
- **Session-Specific Screenshot URLs**: Each session has its own screenshot endpoint
- **Isolated Screenshot Storage**: Screenshots are stored in session-specific directories
- **Accurate Device Screen Display**: Each session shows the correct device screen without cross-contamination

## Architecture Components

### Core Components

1. **SessionIsolationManager** (`session_isolation_manager.py`)
   - Manages session lifecycle (create, start, stop, cleanup)
   - Handles port allocation and resource isolation
   - Maintains session registry and configuration

2. **EnhancedDeviceManager** (`enhanced_device_manager.py`)
   - Discovers devices and creates sessions automatically
   - Provides simplified device management interface
   - Generates session-focused UI components

3. **Session Routes** (`app/routes/session_routes.py`)
   - REST API endpoints for session management
   - Session information and control endpoints
   - Health monitoring and status reporting

4. **Device Manager Launcher** (`device_manager_launcher.py`)
   - Command-line interface for session management
   - Interactive menu for session control
   - Automated session discovery and startup

### UI Components

1. **Session Isolation CSS** (`app/static/css/session-isolation.css`)
   - Styles for session-focused interface
   - Hides device lists and emphasizes session IDs
   - Visual indicators for session status

2. **Session Isolation JavaScript** (`app/static/js/session-isolation.js`)
   - Client-side session management
   - Real-time session monitoring
   - Session-specific screenshot handling

3. **Session Manager Template** (`app/templates/session_manager.html`)
   - Dedicated session management interface
   - Session information display
   - Session control dashboard

## Database Isolation

### Enhanced Database Path Resolution

The database system now supports multiple levels of isolation:

```python
def get_db_path():
    # Priority 1: Session-specific database path (highest priority)
    session_data_dir = os.environ.get('SESSION_DATA_DIR')
    if session_data_dir:
        return os.path.join(session_data_dir, 'test_execution.db')
    
    # Priority 2: Instance-specific suffix for multi-instance support
    instance_suffix = os.environ.get('INSTANCE_DB_SUFFIX', '')
    if instance_suffix:
        db_filename = f'test_execution{instance_suffix}.db'
    else:
        db_filename = 'test_execution.db'
    
    return os.path.join(base_path, db_filename)
```

### Database Files Per Session

Each session creates its own set of database files:
- `test_execution.db` - Test execution tracking and results
- `global_values.db` - Session-specific global configuration values
- `settings.db` - Session-specific application settings

## Session Management API

### Core Endpoints

- `POST /api/session/discover` - Discover devices and create sessions
- `GET /api/session/list` - List all active sessions
- `GET /api/session/<session_id>/info` - Get session information
- `POST /api/session/<session_id>/start` - Start a session
- `POST /api/session/<session_id>/stop` - Stop a session
- `POST /api/session/<session_id>/restart` - Restart a session
- `GET /api/session/manager` - Session manager UI
- `GET /api/session/summary` - HTML summary of all sessions

### Session Information Structure

```json
{
  "session_id": "session_1735123456789_abc12345",
  "device_id": "00008120-00186C801E13C01E",
  "platform": "iOS",
  "status": "running",
  "ports": {
    "flask": 8080,
    "appium": 4723,
    "wda": 8100
  },
  "directories": {
    "session": "/path/to/sessions/session_id",
    "data": "/path/to/sessions/session_id/data",
    "screenshots": "/path/to/sessions/session_id/screenshots",
    "reports": "/path/to/sessions/session_id/reports"
  },
  "url": "http://localhost:8080?sessionId=session_id&deviceId=device_id"
}
```

## Usage Instructions

### 1. Automatic Session Discovery and Creation

```bash
# Launch the device manager with automatic session creation
python device_manager_launcher.py

# Auto-start all sessions without interactive menu
python device_manager_launcher.py --auto-start

# Show session summary only
python device_manager_launcher.py --summary-only
```

### 2. Manual Session Management

```python
from session_isolation_manager import session_manager
from enhanced_device_manager import device_manager

# Discover devices and create sessions
sessions = device_manager.discover_and_create_sessions()

# Start a specific session
session_id = sessions[0]['session_id']
device_manager.start_session(session_id)

# Get session status
status = device_manager.get_session_status(session_id)
print(f"Session {session_id} status: {status['status']}")
```

### 3. Testing Session Isolation

```bash
# Run comprehensive session isolation tests
python test_session_isolation.py

# This will test:
# - Device discovery
# - Session creation
# - Port allocation uniqueness
# - Database isolation
# - Environment variable isolation
# - Screenshot isolation
# - Session cleanup
```

## UI Changes

### Before (Device-Focused)
- Device list prominently displayed
- Manual device selection required
- Device IDs as primary identifiers
- Shared screenshot endpoints

### After (Session-Focused)
- Session IDs prominently displayed
- Automatic device discovery and session creation
- Session IDs as primary identifiers
- Session-specific screenshot endpoints
- Hidden device lists (managed automatically)

### Session Mode Activation

The UI automatically detects session mode when:
1. URL contains `sessionId` parameter
2. URL contains `deviceId` parameter
3. Server has active session information

When session mode is active:
- Device selector is hidden
- Session ID is prominently displayed
- Session status indicator is shown
- Session-specific controls are enabled
- Screenshots use session-specific endpoints

## Environment Variables

### Session-Specific Variables

Each session sets the following environment variables:

```bash
SESSION_ID=session_1735123456789_abc12345
SELECTED_DEVICE_ID=00008120-00186C801E13C01E
SELECTED_PLATFORM=iOS
SESSION_DATA_DIR=/path/to/sessions/session_id/data
SESSION_SCREENSHOTS_DIR=/path/to/sessions/session_id/screenshots
SESSION_REPORTS_DIR=/path/to/sessions/session_id/reports
SESSION_TEMP_DIR=/path/to/sessions/session_id/temp
INSTANCE_DB_SUFFIX=_session_session_1735123456789_abc12345
```

## Port Allocation Strategy

### Automatic Port Assignment

The system automatically allocates unique ports for each session:

- **Flask Server**: Starting from 8080, incremented for each session
- **Appium Server**: Starting from 4723, incremented for each session  
- **WebDriverAgent**: Starting from 8100, incremented for each session

### Port Conflict Resolution

- Checks for existing port usage before allocation
- Skips ports already in use by other processes
- Maintains port registry to prevent conflicts between sessions

## File Structure

```
sessions/
├── session_registry.json                    # Global session registry
├── session_1735123456789_abc12345/         # Session directory
│   ├── session_config.json                 # Session configuration
│   ├── data/                               # Session databases
│   │   ├── test_execution.db
│   │   ├── global_values.db
│   │   └── settings.db
│   ├── screenshots/                        # Session screenshots
│   ├── reports/                           # Session reports
│   └── temp/                              # Session temporary files
└── session_1735123456790_def67890/         # Another session
    └── ...
```

## Benefits

### 1. True Parallel Testing
- Multiple devices can run tests simultaneously without interference
- Each session operates completely independently
- No shared state between sessions

### 2. Simplified User Experience
- Focus on session IDs rather than complex device management
- Automatic device discovery and session creation
- Clean, intuitive interface

### 3. Reliable Screenshot Display
- Each session shows the correct device screen
- No cross-contamination between device screenshots
- Session-specific screenshot storage and retrieval

### 4. Robust Resource Management
- Automatic port allocation prevents conflicts
- Isolated databases prevent data corruption
- Clean session cleanup and resource management

## Troubleshooting

### Common Issues

1. **Port Conflicts**
   - Check if ports are already in use: `lsof -i :PORT`
   - Kill conflicting processes or use different base ports

2. **Database Lock Issues**
   - Ensure each session uses its own database files
   - Check `SESSION_DATA_DIR` environment variable

3. **Screenshot Not Updating**
   - Verify session-specific screenshot URL
   - Check if session is in running state
   - Ensure device is connected and responsive

### Debug Commands

```bash
# Check session status
curl http://localhost:8080/api/session/list

# Get specific session info
curl http://localhost:8080/api/session/SESSION_ID/info

# Check session health
curl http://localhost:8080/api/session/health
```

## Future Enhancements

1. **Session Persistence**: Save and restore session state across restarts
2. **Session Sharing**: Allow multiple users to access the same session
3. **Advanced Monitoring**: Real-time session metrics and performance monitoring
4. **Session Templates**: Pre-configured session templates for different testing scenarios
5. **Cloud Integration**: Support for cloud-based device farms and remote sessions

## Conclusion

This session isolation implementation provides a robust, scalable solution for multi-device mobile app testing. By focusing on session IDs rather than device management, the system offers a cleaner user experience while ensuring complete isolation between concurrent testing sessions.
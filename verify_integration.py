#!/usr/bin/env python3
"""
Verify AppiumDeviceManager Integration

Quick verification that the integration is working correctly.
"""

import subprocess
import sys
from pathlib import Path

def verify_integration():
    """Verify the integration is working"""
    
    print("🔍 Verifying AppiumDeviceManager Integration")
    print("="*50)
    
    appium_manager_path = Path("/Users/<USER>/Documents/automation-tool/AppiumDeviceManager")
    server_js_path = appium_manager_path / "server.js"
    
    # Check 1: server.js exists
    if not server_js_path.exists():
        print("❌ server.js not found")
        return False
    print("✅ server.js exists")
    
    # Check 2: Syntax is valid
    try:
        result = subprocess.run(['node', '-c', 'server.js'], 
                              cwd=str(appium_manager_path), 
                              capture_output=True, text=True)
        if result.returncode == 0:
            print("✅ server.js syntax is valid")
        else:
            print(f"❌ server.js syntax error: {result.stderr}")
            return False
    except Exception as e:
        print(f"❌ Error checking syntax: {e}")
        return False
    
    # Check 3: Integration features are present
    with open(server_js_path, 'r') as f:
        content = f.read()
    
    required_features = [
        "sessionId",
        "autoConnect=true",
        "hideDeviceList=true", 
        "SESSION_ID:",
        "INSTANCE_DB_SUFFIX:",
        "SESSION_ISOLATION_MODE:",
        "/api/sessions/:deviceId/status"
    ]
    
    missing_features = []
    for feature in required_features:
        if feature not in content:
            missing_features.append(feature)
    
    if missing_features:
        print(f"❌ Missing integration features: {missing_features}")
        return False
    
    print("✅ All integration features are present")
    
    # Check 4: Pre-connected device handler exists
    pre_connected_js = Path("app/static/js/pre-connected-device.js")
    if pre_connected_js.exists():
        print("✅ Pre-connected device handler exists")
    else:
        print("⚠️  Pre-connected device handler not found (may need to be created)")
    
    print("\n🎉 Integration Verification Complete!")
    print("="*50)
    print("✅ AppiumDeviceManager is ready to use")
    print("\n📋 Next Steps:")
    print("1. Start AppiumDeviceManager:")
    print("   cd AppiumDeviceManager && npm start")
    print("2. Connect your devices via USB")
    print("3. Click 'Launch Automation App' for any device")
    print("4. Automation app will open with device pre-connected")
    
    return True

if __name__ == "__main__":
    success = verify_integration()
    sys.exit(0 if success else 1)
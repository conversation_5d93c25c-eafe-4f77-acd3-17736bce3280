#!/usr/bin/env python3
"""
Test script to verify parallel device session functionality.

This script tests:
1. Device selection and test execution mapping
2. Device availability status tracking  
3. Session information display
4. Complete session isolation
5. Cross-contamination prevention

Usage:
    python test_parallel_sessions.py
"""

import os
import sys
import time
import requests
import json
import subprocess
import logging
from pathlib import Path

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class ParallelSessionTester:
    def __init__(self):
        self.base_dir = Path(__file__).parent
        self.appium_manager_url = "http://localhost:3001"
        self.ios_app_url = "http://localhost:8080"
        self.android_app_url = "http://localhost:8081"
        
        # Test devices (update these with your actual device UUIDs)
        self.test_devices = [
            {"udid": "00008030-00020C123E60402E", "platform": "iOS", "name": "iPhone12,8"},
            {"udid": "00008120-00186C801E13C01E", "platform": "iOS", "name": "iPhone15,2"}
        ]
        
    def check_appium_manager_running(self):
        """Check if AppiumDeviceManager is running"""
        try:
            response = requests.get(f"{self.appium_manager_url}/api/devices/scan", timeout=5)
            return response.status_code == 200
        except:
            return False
    
    def get_available_devices(self):
        """Get list of available devices from AppiumDeviceManager"""
        try:
            response = requests.get(f"{self.appium_manager_url}/api/devices/scan")
            if response.status_code == 200:
                data = response.json()
                return data.get('devices', [])
            return []
        except Exception as e:
            logger.error(f"Error getting devices: {e}")
            return []
    
    def get_active_sessions(self):
        """Get list of active sessions"""
        try:
            response = requests.get(f"{self.appium_manager_url}/api/sessions")
            if response.status_code == 200:
                data = response.json()
                return data.get('sessions', [])
            return []
        except Exception as e:
            logger.error(f"Error getting sessions: {e}")
            return []
    
    def launch_automation_app(self, device):
        """Launch automation app for a specific device"""
        try:
            payload = {
                "device": device,
                "platform": device["platform"]
            }
            response = requests.post(
                f"{self.appium_manager_url}/api/devices/launch-app",
                json=payload,
                timeout=30
            )
            if response.status_code == 200:
                data = response.json()
                return data
            else:
                logger.error(f"Failed to launch app: {response.status_code} - {response.text}")
                return None
        except Exception as e:
            logger.error(f"Error launching automation app: {e}")
            return None
    
    def check_device_connection(self, app_url, device_udid):
        """Check if automation app is connected to the correct device"""
        try:
            response = requests.get(f"{app_url}/api/environment/selected-device", timeout=10)
            if response.status_code == 200:
                data = response.json()
                return data.get('deviceId') == device_udid
            return False
        except Exception as e:
            logger.error(f"Error checking device connection: {e}")
            return False
    
    def check_database_isolation(self, device_udid):
        """Check if device has isolated database files"""
        device_suffix = f"_device_{device_udid.replace('-', '_')}"
        expected_files = [
            f"test_execution{device_suffix}.db",
            f"global_values{device_suffix}.db", 
            f"settings{device_suffix}.db"
        ]
        
        data_dir = self.base_dir / "data"
        isolated_files = []
        
        for filename in expected_files:
            file_path = data_dir / filename
            if file_path.exists():
                isolated_files.append(filename)
                logger.info(f"Found isolated database file: {filename}")
            else:
                logger.warning(f"Missing isolated database file: {filename}")
        
        return len(isolated_files) == len(expected_files)
    
    def terminate_session(self, device_udid):
        """Terminate session for a device"""
        try:
            payload = {"deviceId": device_udid}
            response = requests.post(
                f"{self.appium_manager_url}/api/sessions/terminate",
                json=payload
            )
            return response.status_code == 200
        except Exception as e:
            logger.error(f"Error terminating session: {e}")
            return False
    
    def run_test_1_device_selection_mapping(self):
        """Test 1: Device selection and test execution mapping"""
        logger.info("=" * 60)
        logger.info("TEST 1: Device Selection and Test Execution Mapping")
        logger.info("=" * 60)
        
        available_devices = self.get_available_devices()
        if len(available_devices) < 2:
            logger.error("Need at least 2 devices for parallel testing")
            return False
        
        test_devices = available_devices[:2]  # Use first 2 devices
        results = []
        
        for device in test_devices:
            logger.info(f"Testing device selection for: {device['name']} ({device['udid']})")
            
            # Launch automation app
            launch_result = self.launch_automation_app(device)
            if not launch_result:
                logger.error(f"Failed to launch app for device {device['udid']}")
                results.append(False)
                continue
            
            # Wait for app to start
            time.sleep(5)
            
            # Check if app connected to correct device
            app_url = self.ios_app_url if device['platform'] == 'iOS' else self.android_app_url
            connected_correctly = self.check_device_connection(app_url, device['udid'])
            
            if connected_correctly:
                logger.info(f"✅ Device {device['udid']} connected correctly")
                results.append(True)
            else:
                logger.error(f"❌ Device {device['udid']} connection mismatch")
                results.append(False)
        
        success = all(results)
        logger.info(f"Test 1 Result: {'PASS' if success else 'FAIL'}")
        return success
    
    def run_test_2_device_status_tracking(self):
        """Test 2: Device availability status tracking"""
        logger.info("=" * 60)
        logger.info("TEST 2: Device Availability Status Tracking")
        logger.info("=" * 60)
        
        # Get initial device list
        initial_devices = self.get_available_devices()
        initial_sessions = self.get_active_sessions()
        
        logger.info(f"Initial devices: {len(initial_devices)}")
        logger.info(f"Initial sessions: {len(initial_sessions)}")
        
        # Check that devices with active sessions show correct status
        session_device_ids = {session['deviceId'] for session in initial_sessions}
        
        status_correct = True
        for device in initial_devices:
            device_id = device.get('udid') or device.get('id')
            has_session = device_id in session_device_ids
            
            # Device status should reflect session state
            if has_session:
                logger.info(f"Device {device_id} has active session - status should be 'Connected'")
            else:
                logger.info(f"Device {device_id} has no session - status should be 'Available'")
        
        logger.info(f"Test 2 Result: {'PASS' if status_correct else 'FAIL'}")
        return status_correct

    def run_test_3_session_information_display(self):
        """Test 3: Session information display"""
        logger.info("=" * 60)
        logger.info("TEST 3: Session Information Display")
        logger.info("=" * 60)

        sessions = self.get_active_sessions()

        if not sessions:
            logger.warning("No active sessions found for testing")
            return True

        for session in sessions:
            required_fields = ['sessionId', 'deviceId', 'port', 'startTime', 'status']
            missing_fields = [field for field in required_fields if field not in session]

            if missing_fields:
                logger.error(f"Session {session.get('sessionId', 'unknown')} missing fields: {missing_fields}")
                return False
            else:
                logger.info(f"✅ Session {session['sessionId']} has all required fields")

        logger.info("Test 3 Result: PASS")
        return True

    def run_test_4_session_isolation(self):
        """Test 4: Complete session isolation"""
        logger.info("=" * 60)
        logger.info("TEST 4: Complete Session Isolation")
        logger.info("=" * 60)

        sessions = self.get_active_sessions()

        if len(sessions) < 2:
            logger.warning("Need at least 2 active sessions to test isolation")
            return True

        isolation_results = []

        for session in sessions:
            device_udid = session['deviceId']
            isolated = self.check_database_isolation(device_udid)
            isolation_results.append(isolated)

            if isolated:
                logger.info(f"✅ Session for device {device_udid} has proper database isolation")
            else:
                logger.error(f"❌ Session for device {device_udid} lacks proper database isolation")

        success = all(isolation_results)
        logger.info(f"Test 4 Result: {'PASS' if success else 'FAIL'}")
        return success

    def run_test_5_cross_contamination_prevention(self):
        """Test 5: Cross-contamination prevention"""
        logger.info("=" * 60)
        logger.info("TEST 5: Cross-Contamination Prevention")
        logger.info("=" * 60)

        sessions = self.get_active_sessions()

        if len(sessions) < 2:
            logger.warning("Need at least 2 active sessions to test cross-contamination prevention")
            return True

        # Check that each session has unique database files
        device_suffixes = []
        for session in sessions:
            device_udid = session['deviceId']
            device_suffix = f"_device_{device_udid.replace('-', '_')}"
            device_suffixes.append(device_suffix)

        # All suffixes should be unique
        unique_suffixes = set(device_suffixes)
        if len(unique_suffixes) == len(device_suffixes):
            logger.info("✅ All sessions have unique database suffixes")
            logger.info("Test 5 Result: PASS")
            return True
        else:
            logger.error("❌ Some sessions share database suffixes")
            logger.info("Test 5 Result: FAIL")
            return False

    def cleanup_sessions(self):
        """Clean up all active sessions"""
        logger.info("Cleaning up active sessions...")
        sessions = self.get_active_sessions()

        for session in sessions:
            device_udid = session['deviceId']
            if self.terminate_session(device_udid):
                logger.info(f"Terminated session for device {device_udid}")
            else:
                logger.warning(f"Failed to terminate session for device {device_udid}")

    def run_all_tests(self):
        """Run all parallel session tests"""
        logger.info("Starting Parallel Session Tests")
        logger.info("=" * 80)

        # Check prerequisites
        if not self.check_appium_manager_running():
            logger.error("AppiumDeviceManager is not running. Please start it first.")
            return False

        test_results = []

        try:
            # Run all tests
            test_results.append(self.run_test_1_device_selection_mapping())
            test_results.append(self.run_test_2_device_status_tracking())
            test_results.append(self.run_test_3_session_information_display())
            test_results.append(self.run_test_4_session_isolation())
            test_results.append(self.run_test_5_cross_contamination_prevention())

        except KeyboardInterrupt:
            logger.info("Tests interrupted by user")
        except Exception as e:
            logger.error(f"Test execution error: {e}")
        finally:
            # Always cleanup
            self.cleanup_sessions()

        # Summary
        logger.info("=" * 80)
        logger.info("TEST SUMMARY")
        logger.info("=" * 80)

        test_names = [
            "Device Selection and Test Execution Mapping",
            "Device Availability Status Tracking",
            "Session Information Display",
            "Complete Session Isolation",
            "Cross-Contamination Prevention"
        ]

        for i, (name, result) in enumerate(zip(test_names, test_results), 1):
            status = "PASS" if result else "FAIL"
            logger.info(f"Test {i}: {name} - {status}")

        overall_success = all(test_results)
        logger.info(f"\nOverall Result: {'ALL TESTS PASSED' if overall_success else 'SOME TESTS FAILED'}")

        return overall_success

if __name__ == "__main__":
    tester = ParallelSessionTester()
    success = tester.run_all_tests()
    sys.exit(0 if success else 1)

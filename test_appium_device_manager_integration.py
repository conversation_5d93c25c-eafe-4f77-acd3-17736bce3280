#!/usr/bin/env python3
"""
Test AppiumDeviceManager Integration

This script tests the integration between the existing AppiumDeviceManager
and the automation app with session isolation.
"""

import os
import sys
import json
import time
import logging
import requests
import subprocess
from pathlib import Path
from urllib.parse import urlparse, parse_qs

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class AppiumDeviceManagerIntegrationTester:
    """
    Tests the integration between AppiumDeviceManager and automation app
    """
    
    def __init__(self):
        """Initialize the tester"""
        self.device_manager_url = "http://localhost:3001"  # AppiumDeviceManager backend
        self.test_results = []
        self.launched_sessions = []
        
    def log_test_result(self, test_name: str, passed: bool, message: str = ""):
        """Log a test result"""
        status = "PASS" if passed else "FAIL"
        result = {
            "test": test_name,
            "status": status,
            "message": message,
            "timestamp": time.time()
        }
        self.test_results.append(result)
        
        logger.info(f"[{status}] {test_name}: {message}")
    
    def test_device_manager_health(self) -> bool:
        """Test AppiumDeviceManager health"""
        logger.info("Testing AppiumDeviceManager health...")
        
        try:
            response = requests.get(f"{self.device_manager_url}/api/health", timeout=10)
            
            if response.status_code == 200:
                self.log_test_result("Device Manager Health", True, "AppiumDeviceManager is healthy")
                return True
            else:
                self.log_test_result("Device Manager Health", False, f"HTTP {response.status_code}")
                return False
                
        except Exception as e:
            self.log_test_result("Device Manager Health", False, f"Connection error: {str(e)}")
            return False
    
    def test_device_discovery(self) -> list:
        """Test device discovery through AppiumDeviceManager"""
        logger.info("Testing device discovery...")
        
        try:
            response = requests.get(f"{self.device_manager_url}/api/devices", timeout=10)
            
            if response.status_code == 200:
                data = response.json()
                devices = data.get("devices", [])
                
                if len(devices) > 0:
                    self.log_test_result("Device Discovery", True, f"Found {len(devices)} devices")
                    
                    # Log device details
                    for device in devices:
                        logger.info(f"  Device: {device.get('name')} ({device.get('udid', device.get('id'))}) - {device.get('platform')}")
                    
                    return devices
                else:
                    self.log_test_result("Device Discovery", False, "No devices found")
                    return []
            else:
                self.log_test_result("Device Discovery", False, f"HTTP {response.status_code}")
                return []
                
        except Exception as e:
            self.log_test_result("Device Discovery", False, f"Error: {str(e)}")
            return []
    
    def test_launch_automation_app(self, device: dict) -> dict:
        """Test launching automation app through AppiumDeviceManager"""
        device_id = device.get('udid') or device.get('id')
        platform = device.get('platform')
        
        logger.info(f"Testing automation app launch for {device_id} ({platform})...")
        
        try:
            launch_data = {
                "device": device,
                "platform": platform
            }
            
            response = requests.post(
                f"{self.device_manager_url}/api/devices/launch-app",
                json=launch_data,
                timeout=120  # Allow up to 2 minutes for launch
            )
            
            if response.status_code == 200:
                data = response.json()
                
                if data.get("success"):
                    session_id = data.get("sessionId")
                    session_url = data.get("url")
                    port = data.get("port")
                    
                    self.log_test_result("Launch Automation App", True, f"Session {session_id} launched on port {port}")
                    
                    # Store launched session for testing
                    self.launched_sessions.append({
                        "session_id": session_id,
                        "device_id": device_id,
                        "session_url": session_url,
                        "port": port
                    })
                    
                    return data
                else:
                    error = data.get("error", "Unknown error")
                    self.log_test_result("Launch Automation App", False, f"Launch failed: {error}")
                    return {}
            else:
                self.log_test_result("Launch Automation App", False, f"HTTP {response.status_code}")
                return {}
                
        except Exception as e:
            self.log_test_result("Launch Automation App", False, f"Error: {str(e)}")
            return {}
    
    def test_pre_connected_url_parameters(self, session_data: dict) -> bool:
        """Test that the session URL contains correct pre-connected parameters"""
        session_url = session_data.get("url")
        device_id = session_data.get("device_id")
        session_id = session_data.get("session_id")
        
        logger.info(f"Testing pre-connected URL parameters for session {session_id}...")
        
        if not session_url:
            self.log_test_result("Pre-Connected URL Parameters", False, "No session URL provided")
            return False
        
        try:
            # Parse URL parameters
            parsed_url = urlparse(session_url)
            params = parse_qs(parsed_url.query)
            
            # Check required parameters
            required_params = {
                'deviceId': device_id,
                'sessionId': session_id,
                'autoConnect': 'true',
                'hideDeviceList': 'true'
            }
            
            missing_params = []
            incorrect_params = []
            
            for param, expected_value in required_params.items():
                if param not in params:
                    missing_params.append(param)
                elif params[param][0] != expected_value:
                    incorrect_params.append(f"{param}={params[param][0]} (expected {expected_value})")
            
            if missing_params:
                self.log_test_result("Pre-Connected URL Parameters", False, f"Missing parameters: {missing_params}")
                return False
            
            if incorrect_params:
                self.log_test_result("Pre-Connected URL Parameters", False, f"Incorrect parameters: {incorrect_params}")
                return False
            
            self.log_test_result("Pre-Connected URL Parameters", True, "All pre-connection parameters are correct")
            return True
            
        except Exception as e:
            self.log_test_result("Pre-Connected URL Parameters", False, f"Error: {str(e)}")
            return False
    
    def test_automation_app_accessibility(self, session_data: dict) -> bool:
        """Test that the automation app is accessible and contains expected content"""
        session_url = session_data.get("url")
        session_id = session_data.get("session_id")
        
        logger.info(f"Testing automation app accessibility for session {session_id}...")
        
        if not session_url:
            self.log_test_result("Automation App Accessibility", False, "No session URL provided")
            return False
        
        try:
            # Wait a moment for the app to fully start
            time.sleep(5)
            
            # Test if the automation app is accessible
            response = requests.get(session_url, timeout=30)
            
            if response.status_code == 200:
                content = response.text
                
                # Check for expected content
                expected_content = [
                    "Mobile App Automation Tool",
                    "Device Control",
                    "Action Builder"
                ]
                
                missing_content = []
                for expected in expected_content:
                    if expected not in content:
                        missing_content.append(expected)
                
                if missing_content:
                    self.log_test_result("Automation App Accessibility", False, f"Missing content: {missing_content}")
                    return False
                else:
                    self.log_test_result("Automation App Accessibility", True, "Automation app is accessible with expected content")
                    return True
            else:
                self.log_test_result("Automation App Accessibility", False, f"HTTP {response.status_code}")
                return False
                
        except Exception as e:
            self.log_test_result("Automation App Accessibility", False, f"Error: {str(e)}")
            return False
    
    def test_session_isolation(self) -> bool:
        """Test that sessions are properly isolated"""
        logger.info("Testing session isolation...")
        
        if len(self.launched_sessions) < 1:
            self.log_test_result("Session Isolation", False, "No sessions available for isolation testing")
            return False
        
        try:
            # Check that each session has unique ports and session IDs
            session_ids = set()
            ports = set()
            
            for session in self.launched_sessions:
                session_id = session.get("session_id")
                port = session.get("port")
                
                if session_id in session_ids:
                    self.log_test_result("Session Isolation", False, f"Duplicate session ID: {session_id}")
                    return False
                
                if port in ports:
                    self.log_test_result("Session Isolation", False, f"Duplicate port: {port}")
                    return False
                
                session_ids.add(session_id)
                ports.add(port)
            
            self.log_test_result("Session Isolation", True, f"All {len(self.launched_sessions)} sessions are properly isolated")
            return True
            
        except Exception as e:
            self.log_test_result("Session Isolation", False, f"Error: {str(e)}")
            return False
    
    def test_device_status_update(self, device_id: str) -> bool:
        """Test device status update to 'In Use'"""
        logger.info(f"Testing device status update for {device_id}...")
        
        try:
            # Check if device status is updated through sessions endpoint
            response = requests.get(f"{self.device_manager_url}/api/sessions", timeout=10)
            
            if response.status_code == 200:
                data = response.json()
                sessions = data.get("sessions", [])
                
                # Find session for this device
                device_session = None
                for session in sessions:
                    if session.get("deviceId") == device_id:
                        device_session = session
                        break
                
                if device_session:
                    self.log_test_result("Device Status Update", True, f"Device {device_id} shows as 'In Use' with active session")
                    return True
                else:
                    self.log_test_result("Device Status Update", False, f"No active session found for device {device_id}")
                    return False
            else:
                self.log_test_result("Device Status Update", False, f"HTTP {response.status_code}")
                return False
                
        except Exception as e:
            self.log_test_result("Device Status Update", False, f"Error: {str(e)}")
            return False
    
    def cleanup_sessions(self):
        """Clean up launched sessions"""
        logger.info("Cleaning up launched sessions...")
        
        for session in self.launched_sessions:
            session_id = session.get("session_id")
            device_id = session.get("device_id")
            
            try:
                # Try to terminate session through AppiumDeviceManager
                response = requests.post(
                    f"{self.device_manager_url}/api/sessions/{device_id}/terminate",
                    timeout=30
                )
                
                if response.status_code == 200:
                    logger.info(f"Successfully terminated session {session_id}")
                else:
                    logger.warning(f"Failed to terminate session {session_id}: HTTP {response.status_code}")
                    
            except Exception as e:
                logger.error(f"Error terminating session {session_id}: {e}")
    
    def run_all_tests(self) -> dict:
        """Run all integration tests"""
        logger.info("Starting AppiumDeviceManager Integration Tests...")
        
        # Test 1: Device Manager Health
        if not self.test_device_manager_health():
            logger.error("AppiumDeviceManager is not healthy - cannot continue tests")
            return self.generate_test_report()
        
        # Test 2: Device Discovery
        devices = self.test_device_discovery()
        if not devices:
            logger.error("No devices found - cannot continue with launch tests")
            return self.generate_test_report()
        
        # Test 3: Launch Automation App (test with first available device)
        test_device = devices[0]
        session_data = self.test_launch_automation_app(test_device)
        
        if session_data:
            device_id = test_device.get('udid') or test_device.get('id')
            
            # Test 4: Pre-Connected URL Parameters
            self.test_pre_connected_url_parameters(session_data)
            
            # Test 5: Automation App Accessibility
            self.test_automation_app_accessibility(session_data)
            
            # Test 6: Device Status Update
            self.test_device_status_update(device_id)
        
        # Test 7: Session Isolation (if multiple devices available)
        if len(devices) > 1:
            # Launch second device for isolation testing
            second_device = devices[1]
            second_session = self.test_launch_automation_app(second_device)
            
            if second_session:
                self.test_session_isolation()
        
        # Cleanup
        self.cleanup_sessions()
        
        return self.generate_test_report()
    
    def generate_test_report(self) -> dict:
        """Generate comprehensive test report"""
        total_tests = len(self.test_results)
        passed_tests = len([r for r in self.test_results if r['status'] == 'PASS'])
        failed_tests = total_tests - passed_tests
        
        report = {
            "summary": {
                "total_tests": total_tests,
                "passed": passed_tests,
                "failed": failed_tests,
                "success_rate": (passed_tests / total_tests * 100) if total_tests > 0 else 0
            },
            "results": self.test_results,
            "launched_sessions": len(self.launched_sessions),
            "timestamp": time.time()
        }
        
        return report
    
    def print_test_report(self, report: dict):
        """Print formatted test report"""
        print("\n" + "="*80)
        print("APPIUM DEVICE MANAGER INTEGRATION TEST REPORT")
        print("="*80)
        
        summary = report['summary']
        print(f"\nSummary:")
        print(f"  Total Tests: {summary['total_tests']}")
        print(f"  Passed: {summary['passed']}")
        print(f"  Failed: {summary['failed']}")
        print(f"  Success Rate: {summary['success_rate']:.1f}%")
        print(f"  Sessions Launched: {report['launched_sessions']}")
        
        print(f"\nDetailed Results:")
        for result in report['results']:
            status_symbol = "✅" if result['status'] == 'PASS' else "❌"
            print(f"  {status_symbol} {result['test']}: {result['message']}")
        
        print("\n" + "="*80)
        
        if summary['failed'] == 0:
            print("🎉 ALL TESTS PASSED - AppiumDeviceManager integration is working correctly!")
        else:
            print(f"⚠️  {summary['failed']} TESTS FAILED - Please review the issues above")
        
        print("="*80)

def main():
    """Main test runner"""
    tester = AppiumDeviceManagerIntegrationTester()
    
    try:
        print("🧪 AppiumDeviceManager Integration Tester")
        print("="*60)
        print("This test verifies the integration between:")
        print("1. Existing AppiumDeviceManager (React app)")
        print("2. Automation app with session isolation")
        print("3. Pre-connected device state")
        print("4. Simplified UI with session focus")
        print("="*60)
        
        # Check if AppiumDeviceManager is running
        print("\n📡 Checking if AppiumDeviceManager is running...")
        print("   If not running, start it with: cd AppiumDeviceManager && npm start")
        
        report = tester.run_all_tests()
        tester.print_test_report(report)
        
        # Save report to file
        report_file = f"appium_device_manager_integration_test_report_{int(time.time())}.json"
        with open(report_file, 'w') as f:
            json.dump(report, f, indent=2)
        
        logger.info(f"Test report saved to: {report_file}")
        
        # Exit with appropriate code
        if report['summary']['failed'] == 0:
            sys.exit(0)
        else:
            sys.exit(1)
            
    except Exception as e:
        logger.error(f"Test runner failed: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
#!/usr/bin/env python3
"""
Test Session Isolation System

This script tests the complete session isolation implementation to ensure:
1. Each device gets its own isolated session
2. Database files are properly separated
3. Screenshots are session-specific
4. Port allocation works correctly
5. UI shows session IDs instead of device lists
"""

import os
import sys
import json
import time
import logging
import tempfile
import subprocess
from pathlib import Path
from typing import List, Dict

# Add current directory to path for imports
sys.path.insert(0, str(Path(__file__).parent))

from session_isolation_manager import session_manager
from enhanced_device_manager import device_manager
from unified_device_discovery import get_all_devices

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class SessionIsolationTester:
    """
    Comprehensive tester for session isolation functionality
    """
    
    def __init__(self):
        """Initialize the tester"""
        self.test_results = []
        self.temp_dirs = []
        
    def log_test_result(self, test_name: str, passed: bool, message: str = ""):
        """Log a test result"""
        status = "PASS" if passed else "FAIL"
        result = {
            "test": test_name,
            "status": status,
            "message": message,
            "timestamp": time.time()
        }
        self.test_results.append(result)
        
        logger.info(f"[{status}] {test_name}: {message}")
    
    def test_device_discovery(self) -> bool:
        """Test device discovery functionality"""
        logger.info("Testing device discovery...")
        
        try:
            devices = get_all_devices()
            
            if len(devices) == 0:
                self.log_test_result("Device Discovery", False, "No devices found - connect at least one device for testing")
                return False
            
            # Verify device structure
            for device in devices:
                required_fields = ['id', 'platform', 'name']
                for field in required_fields:
                    if field not in device:
                        self.log_test_result("Device Discovery", False, f"Device missing required field: {field}")
                        return False
            
            self.log_test_result("Device Discovery", True, f"Found {len(devices)} devices with correct structure")
            return True
            
        except Exception as e:
            self.log_test_result("Device Discovery", False, f"Exception: {str(e)}")
            return False
    
    def test_session_creation(self, devices: List[Dict]) -> List[str]:
        """Test session creation for devices"""
        logger.info("Testing session creation...")
        
        created_sessions = []
        
        try:
            for device in devices:
                device_id = device['id']
                platform = device['platform']
                
                # Create session
                session_id = session_manager.create_session(device_id, platform)
                
                if session_id:
                    created_sessions.append(session_id)
                    
                    # Verify session configuration
                    session_config = session_manager.get_session(session_id)
                    
                    if not session_config:
                        self.log_test_result("Session Creation", False, f"Session config not found for {session_id}")
                        continue
                    
                    # Check required fields
                    required_fields = ['session_id', 'device_id', 'platform', 'ports', 'directories', 'databases']
                    for field in required_fields:
                        if field not in session_config:
                            self.log_test_result("Session Creation", False, f"Session missing field: {field}")
                            continue
                    
                    # Verify directories exist
                    for dir_name, dir_path in session_config['directories'].items():
                        if not os.path.exists(dir_path):
                            self.log_test_result("Session Creation", False, f"Session directory not created: {dir_path}")
                            continue
                    
                    # Verify databases exist
                    for db_name, db_path in session_config['databases'].items():
                        if not os.path.exists(db_path):
                            self.log_test_result("Session Creation", False, f"Session database not created: {db_path}")
                            continue
                    
                    self.log_test_result("Session Creation", True, f"Session {session_id} created successfully for device {device_id}")
                else:
                    self.log_test_result("Session Creation", False, f"Failed to create session for device {device_id}")
            
            return created_sessions
            
        except Exception as e:
            self.log_test_result("Session Creation", False, f"Exception: {str(e)}")
            return created_sessions
    
    def test_port_allocation(self, session_ids: List[str]) -> bool:
        """Test port allocation uniqueness"""
        logger.info("Testing port allocation...")
        
        try:
            used_ports = set()
            
            for session_id in session_ids:
                session_config = session_manager.get_session(session_id)
                if not session_config:
                    continue
                
                ports = session_config.get('ports', {})
                
                for port_type, port in ports.items():
                    if port in used_ports:
                        self.log_test_result("Port Allocation", False, f"Port {port} ({port_type}) already in use")
                        return False
                    used_ports.add(port)
            
            self.log_test_result("Port Allocation", True, f"All ports unique across {len(session_ids)} sessions")
            return True
            
        except Exception as e:
            self.log_test_result("Port Allocation", False, f"Exception: {str(e)}")
            return False
    
    def test_database_isolation(self, session_ids: List[str]) -> bool:
        """Test database isolation between sessions"""
        logger.info("Testing database isolation...")
        
        try:
            db_paths = set()
            
            for session_id in session_ids:
                session_config = session_manager.get_session(session_id)
                if not session_config:
                    continue
                
                databases = session_config.get('databases', {})
                
                for db_name, db_path in databases.items():
                    if db_path in db_paths:
                        self.log_test_result("Database Isolation", False, f"Database path {db_path} shared between sessions")
                        return False
                    db_paths.add(db_path)
                    
                    # Test database write isolation
                    if self.test_database_write_isolation(db_path, session_id):
                        continue
                    else:
                        self.log_test_result("Database Isolation", False, f"Database write test failed for {db_path}")
                        return False
            
            self.log_test_result("Database Isolation", True, f"All databases isolated across {len(session_ids)} sessions")
            return True
            
        except Exception as e:
            self.log_test_result("Database Isolation", False, f"Exception: {str(e)}")
            return False
    
    def test_database_write_isolation(self, db_path: str, session_id: str) -> bool:
        """Test that database writes are isolated"""
        try:
            import sqlite3
            
            # Write session-specific data
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()
            
            # Insert test data
            test_value = f"test_session_{session_id}"
            cursor.execute(
                "INSERT OR REPLACE INTO global_values (name, value, type) VALUES (?, ?, ?)",
                (f"test_isolation_{session_id}", test_value, "string")
            )
            
            conn.commit()
            
            # Verify data exists
            cursor.execute(
                "SELECT value FROM global_values WHERE name = ?",
                (f"test_isolation_{session_id}",)
            )
            
            result = cursor.fetchone()
            conn.close()
            
            if result and result[0] == test_value:
                return True
            else:
                return False
                
        except Exception as e:
            logger.error(f"Database write test failed: {e}")
            return False
    
    def test_environment_isolation(self, session_ids: List[str]) -> bool:
        """Test environment variable isolation"""
        logger.info("Testing environment isolation...")
        
        try:
            for session_id in session_ids:
                session_config = session_manager.get_session(session_id)
                if not session_config:
                    continue
                
                environment = session_config.get('environment', {})
                
                # Check required environment variables
                required_env_vars = ['SESSION_ID', 'SELECTED_DEVICE_ID', 'SESSION_DATA_DIR']
                
                for env_var in required_env_vars:
                    if env_var not in environment:
                        self.log_test_result("Environment Isolation", False, f"Missing environment variable: {env_var}")
                        return False
                
                # Verify session-specific values
                if environment['SESSION_ID'] != session_id:
                    self.log_test_result("Environment Isolation", False, f"Incorrect SESSION_ID in environment")
                    return False
            
            self.log_test_result("Environment Isolation", True, f"Environment variables properly isolated for {len(session_ids)} sessions")
            return True
            
        except Exception as e:
            self.log_test_result("Environment Isolation", False, f"Exception: {str(e)}")
            return False
    
    def test_session_manager_ui(self) -> bool:
        """Test session manager UI generation"""
        logger.info("Testing session manager UI...")
        
        try:
            # Generate session summary HTML
            html_content = device_manager.generate_session_summary_html()
            
            if not html_content or len(html_content) < 100:
                self.log_test_result("Session Manager UI", False, "Generated HTML content too short")
                return False
            
            # Check for required elements
            required_elements = ['session-card', 'session-id', 'Mobile Automation Sessions']
            
            for element in required_elements:
                if element not in html_content:
                    self.log_test_result("Session Manager UI", False, f"Missing required element: {element}")
                    return False
            
            self.log_test_result("Session Manager UI", True, "Session manager UI generated successfully")
            return True
            
        except Exception as e:
            self.log_test_result("Session Manager UI", False, f"Exception: {str(e)}")
            return False
    
    def test_screenshot_isolation(self, session_ids: List[str]) -> bool:
        """Test screenshot path isolation"""
        logger.info("Testing screenshot isolation...")
        
        try:
            screenshot_dirs = set()
            
            for session_id in session_ids:
                session_config = session_manager.get_session(session_id)
                if not session_config:
                    continue
                
                screenshot_dir = session_config['directories']['screenshots']
                
                if screenshot_dir in screenshot_dirs:
                    self.log_test_result("Screenshot Isolation", False, f"Screenshot directory shared: {screenshot_dir}")
                    return False
                
                screenshot_dirs.add(screenshot_dir)
                
                # Verify directory exists and is writable
                if not os.path.exists(screenshot_dir):
                    self.log_test_result("Screenshot Isolation", False, f"Screenshot directory doesn't exist: {screenshot_dir}")
                    return False
                
                if not os.access(screenshot_dir, os.W_OK):
                    self.log_test_result("Screenshot Isolation", False, f"Screenshot directory not writable: {screenshot_dir}")
                    return False
            
            self.log_test_result("Screenshot Isolation", True, f"Screenshot directories isolated for {len(session_ids)} sessions")
            return True
            
        except Exception as e:
            self.log_test_result("Screenshot Isolation", False, f"Exception: {str(e)}")
            return False
    
    def test_session_cleanup(self, session_ids: List[str]) -> bool:
        """Test session cleanup functionality"""
        logger.info("Testing session cleanup...")
        
        try:
            # Test cleanup without removing data
            for session_id in session_ids:
                success = session_manager.cleanup_session(session_id, remove_data=False)
                
                if not success:
                    self.log_test_result("Session Cleanup", False, f"Failed to cleanup session {session_id}")
                    return False
                
                # Verify session is marked as terminated
                session_config = session_manager.get_session(session_id)
                if session_config and session_config.get('status') != 'terminated':
                    self.log_test_result("Session Cleanup", False, f"Session {session_id} not marked as terminated")
                    return False
            
            self.log_test_result("Session Cleanup", True, f"Successfully cleaned up {len(session_ids)} sessions")
            return True
            
        except Exception as e:
            self.log_test_result("Session Cleanup", False, f"Exception: {str(e)}")
            return False
    
    def run_all_tests(self) -> Dict:
        """Run all session isolation tests"""
        logger.info("Starting comprehensive session isolation tests...")
        
        # Test 1: Device Discovery
        devices = []
        if self.test_device_discovery():
            devices = get_all_devices()
        
        if not devices:
            logger.error("No devices found - cannot continue with session tests")
            return self.generate_test_report()
        
        # Test 2: Session Creation
        session_ids = self.test_session_creation(devices)
        
        if not session_ids:
            logger.error("No sessions created - cannot continue with isolation tests")
            return self.generate_test_report()
        
        # Test 3: Port Allocation
        self.test_port_allocation(session_ids)
        
        # Test 4: Database Isolation
        self.test_database_isolation(session_ids)
        
        # Test 5: Environment Isolation
        self.test_environment_isolation(session_ids)
        
        # Test 6: Screenshot Isolation
        self.test_screenshot_isolation(session_ids)
        
        # Test 7: Session Manager UI
        self.test_session_manager_ui()
        
        # Test 8: Session Cleanup
        self.test_session_cleanup(session_ids)
        
        return self.generate_test_report()
    
    def generate_test_report(self) -> Dict:
        """Generate comprehensive test report"""
        total_tests = len(self.test_results)
        passed_tests = len([r for r in self.test_results if r['status'] == 'PASS'])
        failed_tests = total_tests - passed_tests
        
        report = {
            "summary": {
                "total_tests": total_tests,
                "passed": passed_tests,
                "failed": failed_tests,
                "success_rate": (passed_tests / total_tests * 100) if total_tests > 0 else 0
            },
            "results": self.test_results,
            "timestamp": time.time()
        }
        
        return report
    
    def print_test_report(self, report: Dict):
        """Print formatted test report"""
        print("\n" + "="*80)
        print("SESSION ISOLATION TEST REPORT")
        print("="*80)
        
        summary = report['summary']
        print(f"\nSummary:")
        print(f"  Total Tests: {summary['total_tests']}")
        print(f"  Passed: {summary['passed']}")
        print(f"  Failed: {summary['failed']}")
        print(f"  Success Rate: {summary['success_rate']:.1f}%")
        
        print(f"\nDetailed Results:")
        for result in report['results']:
            status_symbol = "✅" if result['status'] == 'PASS' else "❌"
            print(f"  {status_symbol} {result['test']}: {result['message']}")
        
        print("\n" + "="*80)
        
        if summary['failed'] == 0:
            print("🎉 ALL TESTS PASSED - Session isolation is working correctly!")
        else:
            print(f"⚠️  {summary['failed']} TESTS FAILED - Please review the issues above")
        
        print("="*80)

def main():
    """Main test runner"""
    tester = SessionIsolationTester()
    
    try:
        report = tester.run_all_tests()
        tester.print_test_report(report)
        
        # Save report to file
        report_file = f"session_isolation_test_report_{int(time.time())}.json"
        with open(report_file, 'w') as f:
            json.dump(report, f, indent=2)
        
        logger.info(f"Test report saved to: {report_file}")
        
        # Exit with appropriate code
        if report['summary']['failed'] == 0:
            sys.exit(0)
        else:
            sys.exit(1)
            
    except Exception as e:
        logger.error(f"Test runner failed: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
// Integration patch for existing AppiumDeviceManager server.js
// This file contains the modifications needed for the existing server.js

// Replace the existing /api/devices/launch-app endpoint with this enhanced version:

app.post('/api/devices/launch-app', async (req, res) => {
  console.log('[API] Received POST request for /api/devices/launch-app');
  const { device, platform } = req.body;

  if (!device || !platform) {
    return res.status(400).json({
      success: false,
      error: 'Missing required parameters',
      details: 'Device UDID and platform are required'
    });
  }

  try {
    const deviceId = device.udid || device.id;

    // Check if device already has an active session
    const existingSession = sessionManager.getSession(deviceId);
    if (existingSession) {
      console.log(`Device ${deviceId} already has an active session on port ${existingSession.port}`);
      
      // Generate URL with pre-connected device parameters
      const sessionUrl = `http://localhost:${existingSession.port}?deviceId=${deviceId}&platform=${platform}&sessionId=${existingSession.sessionId}&autoConnect=true&hideDeviceList=true`;
      
      return res.json({
        success: true,
        message: 'Automation app is already running for this device',
        url: sessionUrl,
        port: existingSession.port,
        session: existingSession,
        alreadyRunning: true
      });
    }

    // Determine the script to run based on platform
    let scriptName;
    if (platform.toLowerCase() === 'ios') {
      scriptName = 'run.py';
    } else if (platform.toLowerCase() === 'android') {
      scriptName = 'run_android.py';
    } else {
      return res.status(400).json({
        success: false,
        error: 'Invalid platform',
        details: 'Platform must be either iOS or Android'
      });
    }

    // Get next available port for this platform
    const port = await sessionManager.getNextAvailablePort(platform);
    
    // Get the project root directory (parent of AppiumDeviceManager, then into MobileApp-AutoTest)
    const projectRoot = path.join(__dirname, '..', 'MobileApp-AutoTest');
    const scriptPath = path.join(projectRoot, scriptName);

    // Check if the script exists
    if (!fs.existsSync(scriptPath)) {
      return res.status(404).json({
        success: false,
        error: 'Automation script not found',
        details: `The script ${scriptName} does not exist at ${scriptPath}`
      });
    }

    // Double-check port availability
    const portAvailable = await sessionManager.isPortAvailable(port);
    if (!portAvailable) {
      return res.status(409).json({
        success: false,
        error: 'Port conflict',
        details: `Port ${port} is already in use`
      });
    }

    // Generate unique session ID
    const sessionId = `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

    // Enhanced environment variables for complete session isolation
    const env = {
      ...process.env,
      // Original variables (maintain compatibility)
      SELECTED_DEVICE_UDID: deviceId,
      SELECTED_DEVICE_PLATFORM: platform,
      
      // Enhanced session isolation variables
      SELECTED_DEVICE_ID: deviceId,
      SELECTED_PLATFORM: platform,
      SESSION_ID: sessionId,
      INSTANCE_DB_SUFFIX: `_device_${deviceId.replace(/-/g, '_')}`,
      SESSION_ISOLATION_MODE: 'true',
      
      // Session-specific directories (if needed)
      SESSION_DATA_DIR: path.join(projectRoot, 'sessions', sessionId, 'data'),
      SESSION_SCREENSHOTS_DIR: path.join(projectRoot, 'sessions', sessionId, 'screenshots'),
      SESSION_REPORTS_DIR: path.join(projectRoot, 'sessions', sessionId, 'reports'),
      SESSION_TEMP_DIR: path.join(projectRoot, 'sessions', sessionId, 'temp')
    };

    // Create session directories
    const sessionDirs = [
      env.SESSION_DATA_DIR,
      env.SESSION_SCREENSHOTS_DIR,
      env.SESSION_REPORTS_DIR,
      env.SESSION_TEMP_DIR
    ];

    for (const dir of sessionDirs) {
      if (!fs.existsSync(dir)) {
        fs.mkdirSync(dir, { recursive: true });
      }
    }

    // Launch the automation app with enhanced environment
    console.log(`Launching ${scriptName} for ${platform} device ${deviceId} on port ${port} with session ${sessionId}`);
    const automationProcess = spawn('bash', ['-c', `source venv/bin/activate && python3 ${scriptName} --port ${port}`], {
      cwd: projectRoot,
      env: env,
      detached: true,
      stdio: ['ignore', 'pipe', 'pipe']
    });

    // Create enhanced session with session ID
    const session = sessionManager.createSession(deviceId, platform, port, automationProcess.pid);
    session.sessionId = sessionId; // Add session ID to the session object

    // Log output for debugging
    automationProcess.stdout.on('data', (data) => {
      console.log(`[${sessionId}] stdout: ${data}`);
      sessionManager.updateActivity(deviceId);
    });

    automationProcess.stderr.on('data', (data) => {
      console.error(`[${sessionId}] stderr: ${data}`);
      sessionManager.updateActivity(deviceId);
    });

    automationProcess.on('exit', (code) => {
      console.log(`[${sessionId}] Process exited with code: ${code}`);
      // Clean up session when process exits
      try {
        const existingSession = sessionManager.getSession(deviceId);
        if (existingSession) {
          sessionManager.terminateSession(deviceId);
        }
      } catch (error) {
        console.warn(`Failed to cleanup session for device ${deviceId}:`, error.message);
      }
    });

    automationProcess.on('error', (error) => {
      console.error(`[${sessionId}] Process error:`, error);
      // Clean up session on error
      try {
        const existingSession = sessionManager.getSession(deviceId);
        if (existingSession) {
          sessionManager.terminateSession(deviceId);
        }
      } catch (cleanupError) {
        console.warn(`Failed to cleanup session for device ${deviceId}:`, cleanupError.message);
      }
    });

    // Detach the process so it continues running independently
    automationProcess.unref();

    console.log(`Automation app launched with PID: ${automationProcess.pid}, Session: ${sessionId}`);

    // Generate URL with pre-connected device parameters for simplified UI
    const sessionUrl = `http://localhost:${port}?deviceId=${deviceId}&platform=${platform}&sessionId=${sessionId}&autoConnect=true&hideDeviceList=true`;

    // Give the app a moment to start up
    setTimeout(() => {
      res.json({
        success: true,
        message: 'Automation app launched successfully',
        url: sessionUrl,
        port: port,
        pid: automationProcess.pid,
        session: session,
        sessionId: sessionId,
        alreadyRunning: false
      });
    }, 2000);

  } catch (error) {
    console.error('[API] Error launching automation app:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to launch automation app',
      details: error.message
    });
  }
});

// Add new endpoint to get session status
app.get('/api/sessions/:deviceId/status', (req, res) => {
  try {
    const { deviceId } = req.params;
    const session = sessionManager.getSession(deviceId);
    
    if (session) {
      res.json({
        success: true,
        session: session,
        status: 'active',
        url: `http://localhost:${session.port}?deviceId=${deviceId}&sessionId=${session.sessionId}&autoConnect=true&hideDeviceList=true`
      });
    } else {
      res.json({
        success: true,
        session: null,
        status: 'inactive'
      });
    }
  } catch (error) {
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// Add endpoint to update device status to "In Use"
app.post('/api/devices/:deviceId/status', (req, res) => {
  try {
    const { deviceId } = req.params;
    const { status } = req.body;
    
    // Update device status in session manager
    const session = sessionManager.getSession(deviceId);
    if (session) {
      session.deviceStatus = status;
      res.json({
        success: true,
        message: `Device ${deviceId} status updated to ${status}`
      });
    } else {
      res.status(404).json({
        success: false,
        error: 'No active session found for device'
      });
    }
  } catch (error) {
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});
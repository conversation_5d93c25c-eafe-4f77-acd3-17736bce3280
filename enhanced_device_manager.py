#!/usr/bin/env python3
"""
Enhanced Device Manager with Session Isolation

This module provides a simplified device management interface that:
- Hides device lists from the UI
- Shows only session IDs as primary identifiers
- Ensures proper session isolation for each device
- Manages screenshot accuracy per session
"""

import os
import sys
import json
import logging
import subprocess
import webbrowser
from pathlib import Path
from typing import Dict, List, Optional
from session_isolation_manager import session_manager
from unified_device_discovery import get_all_devices

logger = logging.getLogger(__name__)

class EnhancedDeviceManager:
    """
    Enhanced device manager with session isolation and simplified UI
    """
    
    def __init__(self):
        """Initialize the enhanced device manager"""
        self.active_sessions = {}
        self.device_sessions = {}  # Map device_id -> session_id
        
        logger.info("Enhanced Device Manager initialized")
    
    def discover_and_create_sessions(self) -> List[Dict]:
        """
        Discover all connected devices and create isolated sessions for each
        
        Returns:
            List of session information dictionaries
        """
        logger.info("Discovering devices and creating isolated sessions...")
        
        # Get all connected devices
        devices = get_all_devices()
        logger.info(f"Found {len(devices)} connected devices")
        
        sessions_created = []
        
        for device in devices:
            device_id = device['id']
            platform = device['platform']
            
            # Check if device already has an active session
            existing_session = session_manager.get_session_by_device(device_id)
            
            if existing_session and existing_session.get('status') != 'terminated':
                logger.info(f"Device {device_id} already has active session: {existing_session['session_id']}")
                session_info = self._create_session_info(existing_session, device)
                sessions_created.append(session_info)
                self.device_sessions[device_id] = existing_session['session_id']
            else:
                # Create new session for this device
                session_id = session_manager.create_session(device_id, platform)
                session_config = session_manager.get_session(session_id)
                
                if session_config:
                    logger.info(f"Created new session {session_id} for device {device_id}")
                    session_info = self._create_session_info(session_config, device)
                    sessions_created.append(session_info)
                    self.device_sessions[device_id] = session_id
                    self.active_sessions[session_id] = session_config
        
        return sessions_created
    
    def _create_session_info(self, session_config: Dict, device: Dict) -> Dict:
        """Create session information for UI display"""
        return {
            'session_id': session_config['session_id'],
            'device_id': session_config['device_id'],
            'device_name': device.get('name', f"{device.get('platform', 'Unknown')} Device"),
            'platform': session_config['platform'],
            'status': session_config.get('status', 'created'),
            'ports': session_config['ports'],
            'url': session_manager.get_session_url(session_config['session_id']),
            'created_at': session_config.get('created_at'),
            'device_info': {
                'model': device.get('model', 'Unknown'),
                'osVersion': device.get('osVersion', 'Unknown'),
                'type': device.get('type', 'real')
            }
        }
    
    def start_session(self, session_id: str) -> bool:
        """
        Start an isolated session for a device
        
        Args:
            session_id: Session identifier
            
        Returns:
            True if session started successfully
        """
        logger.info(f"Starting session {session_id}")
        
        session_config = session_manager.get_session(session_id)
        if not session_config:
            logger.error(f"Session {session_id} not found")
            return False
        
        # Start the session using the session manager
        if session_manager.start_session(session_id):
            # Update local tracking
            self.active_sessions[session_id] = session_config
            
            # Wait a moment for the server to start
            import time
            time.sleep(2)
            
            # Open the session URL in browser
            session_url = session_manager.get_session_url(session_id)
            if session_url:
                logger.info(f"Opening session URL: {session_url}")
                webbrowser.open(session_url)
            
            return True
        
        return False
    
    def stop_session(self, session_id: str) -> bool:
        """
        Stop a running session
        
        Args:
            session_id: Session identifier
            
        Returns:
            True if session stopped successfully
        """
        logger.info(f"Stopping session {session_id}")
        
        if session_manager.stop_session(session_id):
            # Remove from local tracking
            self.active_sessions.pop(session_id, None)
            
            # Remove device mapping
            for device_id, sid in list(self.device_sessions.items()):
                if sid == session_id:
                    del self.device_sessions[device_id]
                    break
            
            return True
        
        return False
    
    def get_session_status(self, session_id: str) -> Optional[Dict]:
        """Get the current status of a session"""
        session_config = session_manager.get_session(session_id)
        if not session_config:
            return None
        
        # Check if the process is actually running
        if session_config.get('status') == 'running' and 'process_id' in session_config:
            try:
                # Check if process exists
                os.kill(session_config['process_id'], 0)
                actual_status = 'running'
            except ProcessLookupError:
                actual_status = 'stopped'
                # Update the session status
                session_config['status'] = 'stopped'
                session_manager._save_session_registry()
        else:
            actual_status = session_config.get('status', 'unknown')
        
        return {
            'session_id': session_id,
            'status': actual_status,
            'device_id': session_config.get('device_id'),
            'platform': session_config.get('platform'),
            'ports': session_config.get('ports', {}),
            'url': session_manager.get_session_url(session_id) if actual_status == 'running' else None
        }
    
    def list_sessions(self) -> List[Dict]:
        """List all sessions with their current status"""
        sessions = []
        
        for session_id in session_manager.sessions:
            status_info = self.get_session_status(session_id)
            if status_info and status_info['status'] != 'terminated':
                sessions.append(status_info)
        
        return sessions
    
    def cleanup_terminated_sessions(self) -> int:
        """Clean up terminated sessions and return count of cleaned sessions"""
        cleaned_count = 0
        
        for session_id in list(session_manager.sessions.keys()):
            session = session_manager.get_session(session_id)
            if session and session.get('status') == 'terminated':
                session_manager.cleanup_session(session_id, remove_data=True)
                cleaned_count += 1
        
        return cleaned_count
    
    def get_session_screenshot_url(self, session_id: str) -> Optional[str]:
        """
        Get the session-specific screenshot URL
        
        Args:
            session_id: Session identifier
            
        Returns:
            Screenshot URL for the session
        """
        session_config = session_manager.get_session(session_id)
        if not session_config or session_config.get('status') != 'running':
            return None
        
        flask_port = session_config['ports']['flask']
        device_id = session_config['device_id']
        
        return f"http://localhost:{flask_port}/screenshot?deviceId={device_id}&sessionId={session_id}"
    
    def generate_session_summary_html(self) -> str:
        """Generate HTML summary of all active sessions"""
        sessions = self.list_sessions()
        
        html = """
        <!DOCTYPE html>
        <html>
        <head>
            <title>Mobile Automation Sessions</title>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
            <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.css">
            <style>
                .session-card {
                    transition: transform 0.2s;
                    border-left: 4px solid #007bff;
                }
                .session-card:hover {
                    transform: translateY(-2px);
                    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
                }
                .session-id {
                    font-family: 'Courier New', monospace;
                    font-weight: bold;
                    color: #007bff;
                }
                .status-running {
                    color: #28a745;
                }
                .status-stopped {
                    color: #dc3545;
                }
                .status-created {
                    color: #ffc107;
                }
                .device-info {
                    font-size: 0.9em;
                    color: #6c757d;
                }
            </style>
        </head>
        <body>
            <div class="container mt-4">
                <div class="row">
                    <div class="col-12">
                        <h1 class="mb-4">
                            <i class="bi bi-phone me-2"></i>
                            Mobile Automation Sessions
                        </h1>
                        <p class="text-muted">Manage your isolated device testing sessions</p>
                    </div>
                </div>
        """
        
        if not sessions:
            html += """
                <div class="row">
                    <div class="col-12">
                        <div class="alert alert-info">
                            <i class="bi bi-info-circle me-2"></i>
                            No active sessions found. Connect devices to create new sessions.
                        </div>
                    </div>
                </div>
            """
        else:
            html += '<div class="row">'
            
            for session in sessions:
                status_class = f"status-{session['status']}"
                status_icon = {
                    'running': 'bi-play-circle-fill',
                    'stopped': 'bi-stop-circle',
                    'created': 'bi-clock'
                }.get(session['status'], 'bi-question-circle')
                
                html += f"""
                    <div class="col-md-6 col-lg-4 mb-4">
                        <div class="card session-card h-100">
                            <div class="card-body">
                                <h5 class="card-title">
                                    <span class="session-id">{session['session_id']}</span>
                                </h5>
                                <div class="device-info mb-3">
                                    <i class="bi bi-phone me-1"></i>
                                    Device: {session['device_id'][:12]}...
                                    <br>
                                    <i class="bi bi-gear me-1"></i>
                                    Platform: {session['platform']}
                                </div>
                                <div class="mb-3">
                                    <span class="badge bg-secondary">
                                        Flask: {session['ports']['flask']}
                                    </span>
                                    <span class="badge bg-secondary">
                                        Appium: {session['ports']['appium']}
                                    </span>
                                </div>
                                <div class="d-flex justify-content-between align-items-center">
                                    <span class="{status_class}">
                                        <i class="{status_icon} me-1"></i>
                                        {session['status'].title()}
                                    </span>
                """
                
                if session['status'] == 'running' and session.get('url'):
                    html += f"""
                                    <a href="{session['url']}" class="btn btn-primary btn-sm" target="_blank">
                                        <i class="bi bi-box-arrow-up-right me-1"></i>
                                        Open
                                    </a>
                    """
                
                html += """
                                </div>
                            </div>
                        </div>
                    </div>
                """
            
            html += '</div>'
        
        html += """
                <div class="row mt-4">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-body">
                                <h5 class="card-title">Session Management</h5>
                                <p class="card-text">
                                    Each session provides complete isolation with separate databases, 
                                    configuration, and port allocations. Sessions are automatically 
                                    created when devices are connected.
                                </p>
                                <button class="btn btn-success me-2" onclick="location.reload()">
                                    <i class="bi bi-arrow-clockwise me-1"></i>
                                    Refresh Sessions
                                </button>
                                <button class="btn btn-outline-secondary" onclick="discoverDevices()">
                                    <i class="bi bi-search me-1"></i>
                                    Discover Devices
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
            <script>
                function discoverDevices() {
                    // This would trigger device discovery and session creation
                    alert('Device discovery would be triggered here');
                }
                
                // Auto-refresh every 30 seconds
                setTimeout(() => location.reload(), 30000);
            </script>
        </body>
        </html>
        """
        
        return html

# Global instance
device_manager = EnhancedDeviceManager()
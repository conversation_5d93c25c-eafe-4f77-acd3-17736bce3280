#!/usr/bin/env python3
"""
Fix AppiumDeviceManager Integration

This script properly patches the AppiumDeviceManager server.js file
by manually replacing specific sections to avoid regex issues.
"""

import os
import logging
from pathlib import Path

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def fix_appium_device_manager():
    """Fix the AppiumDeviceManager server.js file"""
    
    appium_manager_path = Path("/Users/<USER>/Documents/automation-tool/AppiumDeviceManager")
    server_js_path = appium_manager_path / "server.js"
    
    if not server_js_path.exists():
        logger.error(f"AppiumDeviceManager server.js not found at {server_js_path}")
        return False
    
    logger.info("Reading current server.js...")
    
    # Read the current server.js
    with open(server_js_path, 'r') as f:
        content = f.read()
    
    # Find the launch-app endpoint and replace it
    logger.info("Replacing /api/devices/launch-app endpoint...")
    
    # Find the start of the launch-app endpoint
    start_marker = "app.post('/api/devices/launch-app', async (req, res) => {"
    end_marker = "});"
    
    start_index = content.find(start_marker)
    if start_index == -1:
        logger.error("Could not find launch-app endpoint")
        return False
    
    # Find the end of this specific endpoint by counting braces
    brace_count = 0
    current_index = start_index + len(start_marker)
    end_index = -1
    
    while current_index < len(content):
        char = content[current_index]
        if char == '{':
            brace_count += 1
        elif char == '}':
            brace_count -= 1
            if brace_count == -1:  # Found the closing brace for the function
                end_index = current_index + 2  # Include the "};" 
                break
        current_index += 1
    
    if end_index == -1:
        logger.error("Could not find end of launch-app endpoint")
        return False
    
    # Extract the part before and after the endpoint
    before_endpoint = content[:start_index]
    after_endpoint = content[end_index:]
    
    # New enhanced launch-app endpoint
    new_endpoint = '''app.post('/api/devices/launch-app', async (req, res) => {
  console.log('[API] Received POST request for /api/devices/launch-app');
  const { device, platform } = req.body;

  if (!device || !platform) {
    return res.status(400).json({
      success: false,
      error: 'Missing required parameters',
      details: 'Device UDID and platform are required'
    });
  }

  try {
    const deviceId = device.udid || device.id;

    // Check if device already has an active session
    const existingSession = sessionManager.getSession(deviceId);
    if (existingSession) {
      console.log(`Device ${deviceId} already has an active session on port ${existingSession.port}`);
      
      // Generate URL with pre-connected device parameters
      const sessionUrl = `http://localhost:${existingSession.port}?deviceId=${deviceId}&platform=${platform}&sessionId=${existingSession.sessionId}&autoConnect=true&hideDeviceList=true`;
      
      return res.json({
        success: true,
        message: 'Automation app is already running for this device',
        url: sessionUrl,
        port: existingSession.port,
        session: existingSession,
        alreadyRunning: true
      });
    }

    // Determine the script to run based on platform
    let scriptName;
    if (platform.toLowerCase() === 'ios') {
      scriptName = 'run.py';
    } else if (platform.toLowerCase() === 'android') {
      scriptName = 'run_android.py';
    } else {
      return res.status(400).json({
        success: false,
        error: 'Invalid platform',
        details: 'Platform must be either iOS or Android'
      });
    }

    // Get next available port for this platform
    const port = await sessionManager.getNextAvailablePort(platform);
    
    // Get the project root directory (parent of AppiumDeviceManager, then into MobileApp-AutoTest)
    const projectRoot = path.join(__dirname, '..', 'MobileApp-AutoTest');
    const scriptPath = path.join(projectRoot, scriptName);

    // Check if the script exists
    if (!fs.existsSync(scriptPath)) {
      return res.status(404).json({
        success: false,
        error: 'Automation script not found',
        details: `The script ${scriptName} does not exist at ${scriptPath}`
      });
    }

    // Double-check port availability
    const portAvailable = await sessionManager.isPortAvailable(port);
    if (!portAvailable) {
      return res.status(409).json({
        success: false,
        error: 'Port conflict',
        details: `Port ${port} is already in use`
      });
    }

    // Generate unique session ID
    const sessionId = `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

    // Enhanced environment variables for complete session isolation
    const env = {
      ...process.env,
      // Original variables (maintain compatibility)
      SELECTED_DEVICE_UDID: deviceId,
      SELECTED_DEVICE_PLATFORM: platform,
      
      // Enhanced session isolation variables
      SELECTED_DEVICE_ID: deviceId,
      SELECTED_PLATFORM: platform,
      SESSION_ID: sessionId,
      INSTANCE_DB_SUFFIX: `_device_${deviceId.replace(/-/g, '_')}`,
      SESSION_ISOLATION_MODE: 'true'
    };

    // Launch the automation app with enhanced environment
    console.log(`Launching ${scriptName} for ${platform} device ${deviceId} on port ${port} with session ${sessionId}`);
    const automationProcess = spawn('bash', ['-c', `source venv/bin/activate && python3 ${scriptName} --port ${port}`], {
      cwd: projectRoot,
      env: env,
      detached: true,
      stdio: ['ignore', 'pipe', 'pipe']
    });

    // Create enhanced session with session ID
    const session = sessionManager.createSession(deviceId, platform, port, automationProcess.pid);
    session.sessionId = sessionId; // Add session ID to the session object

    // Log output for debugging
    automationProcess.stdout.on('data', (data) => {
      console.log(`[${sessionId}] stdout: ${data}`);
      sessionManager.updateActivity(deviceId);
    });

    automationProcess.stderr.on('data', (data) => {
      console.error(`[${sessionId}] stderr: ${data}`);
      sessionManager.updateActivity(deviceId);
    });

    automationProcess.on('exit', (code) => {
      console.log(`[${sessionId}] Process exited with code: ${code}`);
      // Clean up session when process exits
      try {
        const existingSession = sessionManager.getSession(deviceId);
        if (existingSession) {
          sessionManager.terminateSession(deviceId);
        }
      } catch (error) {
        console.warn(`Failed to cleanup session for device ${deviceId}:`, error.message);
      }
    });

    automationProcess.on('error', (error) => {
      console.error(`[${sessionId}] Process error:`, error);
      // Clean up session on error
      try {
        const existingSession = sessionManager.getSession(deviceId);
        if (existingSession) {
          sessionManager.terminateSession(deviceId);
        }
      } catch (cleanupError) {
        console.warn(`Failed to cleanup session for device ${deviceId}:`, cleanupError.message);
      }
    });

    // Detach the process so it continues running independently
    automationProcess.unref();

    console.log(`Automation app launched with PID: ${automationProcess.pid}, Session: ${sessionId}`);

    // Generate URL with pre-connected device parameters for simplified UI
    const sessionUrl = `http://localhost:${port}?deviceId=${deviceId}&platform=${platform}&sessionId=${sessionId}&autoConnect=true&hideDeviceList=true`;

    // Give the app a moment to start up
    setTimeout(() => {
      res.json({
        success: true,
        message: 'Automation app launched successfully',
        url: sessionUrl,
        port: port,
        pid: automationProcess.pid,
        session: session,
        sessionId: sessionId,
        alreadyRunning: false
      });
    }, 2000);

  } catch (error) {
    console.error('[API] Error launching automation app:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to launch automation app',
      details: error.message
    });
  }
});'''
    
    # Add new endpoints before the app.listen section
    logger.info("Adding new session management endpoints...")
    
    # Find where to insert new endpoints (before app.listen)
    app_listen_index = after_endpoint.find('app.listen(')
    if app_listen_index == -1:
        logger.error("Could not find app.listen in remaining content")
        return False
    
    before_listen = after_endpoint[:app_listen_index]
    listen_and_after = after_endpoint[app_listen_index:]
    
    new_endpoints = '''
// Enhanced session management endpoints for device manager integration

// Get session status for a device
app.get('/api/sessions/:deviceId/status', (req, res) => {
  try {
    const { deviceId } = req.params;
    const session = sessionManager.getSession(deviceId);
    
    if (session) {
      const sessionUrl = `http://localhost:${session.port}?deviceId=${deviceId}&sessionId=${session.sessionId}&autoConnect=true&hideDeviceList=true`;
      res.json({
        success: true,
        session: session,
        status: 'active',
        url: sessionUrl
      });
    } else {
      res.json({
        success: true,
        session: null,
        status: 'inactive'
      });
    }
  } catch (error) {
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// Update device status
app.post('/api/devices/:deviceId/status', (req, res) => {
  try {
    const { deviceId } = req.params;
    const { status } = req.body;
    
    // Update device status in session manager
    const session = sessionManager.getSession(deviceId);
    if (session) {
      session.deviceStatus = status;
      res.json({
        success: true,
        message: `Device ${deviceId} status updated to ${status}`
      });
    } else {
      res.status(404).json({
        success: false,
        error: 'No active session found for device'
      });
    }
  } catch (error) {
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

'''
    
    # Reconstruct the file
    new_content = before_endpoint + new_endpoint + before_listen + new_endpoints + listen_and_after
    
    # Write the new content
    with open(server_js_path, 'w') as f:
        f.write(new_content)
    
    logger.info("AppiumDeviceManager server.js fixed successfully!")
    return True

def main():
    """Main function"""
    print("AppiumDeviceManager Fix Script")
    print("="*40)
    
    try:
        success = fix_appium_device_manager()
        
        if success:
            print("\n✅ AppiumDeviceManager Fixed Successfully!")
            print("="*50)
            print("🔧 Changes Applied:")
            print("   ✓ Fixed syntax error in server.js")
            print("   ✓ Enhanced launch-app endpoint with session isolation")
            print("   ✓ Added session management endpoints")
            print("   ✓ Pre-connected device URL parameters")
            
            print("\n🚀 Next Steps:")
            print("   1. Try starting AppiumDeviceManager again:")
            print("      cd AppiumDeviceManager && npm start")
            print("   2. Connect your devices via USB")
            print("   3. Click 'Launch Automation App' for any device")
            
        else:
            print("❌ Fix failed - check logs for details")
            return 1
            
    except Exception as e:
        print(f"❌ Fix failed: {e}")
        return 1
    
    return 0

if __name__ == "__main__":
    exit(main())
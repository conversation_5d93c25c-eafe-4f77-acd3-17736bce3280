#!/usr/bin/env python3
"""
Device Manager Launcher with Session Isolation

This script provides a simplified device management interface that:
1. Discovers connected devices automatically
2. Creates isolated sessions for each device
3. Launches automation tools with session isolation
4. Provides a clean UI focused on session IDs rather than device lists
"""

import os
import sys
import json
import time
import logging
import argparse
import webbrowser
from pathlib import Path
from typing import List, Dict

# Add current directory to path for imports
sys.path.insert(0, str(Path(__file__).parent))

from session_isolation_manager import session_manager
from enhanced_device_manager import device_manager
from unified_device_discovery import get_all_devices

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('device_manager.log'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

class DeviceManagerLauncher:
    """
    Main launcher for the enhanced device manager with session isolation
    """
    
    def __init__(self):
        """Initialize the device manager launcher"""
        self.sessions = {}
        self.running = True
        
        logger.info("Device Manager Launcher initialized")
    
    def discover_devices(self) -> List[Dict]:
        """Discover all connected devices"""
        logger.info("Discovering connected devices...")
        
        devices = get_all_devices()
        logger.info(f"Found {len(devices)} connected devices:")
        
        for device in devices:
            logger.info(f"  - {device['name']} ({device['id']}) - {device['platform']}")
        
        return devices
    
    def create_sessions_for_devices(self, devices: List[Dict]) -> List[Dict]:
        """Create isolated sessions for all devices"""
        logger.info("Creating isolated sessions for devices...")
        
        sessions_info = device_manager.discover_and_create_sessions()
        
        logger.info(f"Created {len(sessions_info)} isolated sessions:")
        for session in sessions_info:
            logger.info(f"  - Session: {session['session_id']}")
            logger.info(f"    Device: {session['device_name']} ({session['device_id']})")
            logger.info(f"    Platform: {session['platform']}")
            logger.info(f"    Ports: Flask:{session['ports']['flask']}, Appium:{session['ports']['appium']}, WDA:{session['ports']['wda']}")
            logger.info(f"    URL: {session['url']}")
        
        return sessions_info
    
    def start_session(self, session_id: str) -> bool:
        """Start a specific session"""
        logger.info(f"Starting session: {session_id}")
        
        success = device_manager.start_session(session_id)
        
        if success:
            logger.info(f"Session {session_id} started successfully")
            
            # Wait for the server to start
            time.sleep(3)
            
            # Get session URL and open in browser
            session_url = session_manager.get_session_url(session_id)
            if session_url:
                logger.info(f"Opening session URL: {session_url}")
                webbrowser.open(session_url)
            
            return True
        else:
            logger.error(f"Failed to start session {session_id}")
            return False
    
    def start_all_sessions(self, sessions_info: List[Dict]) -> int:
        """Start all sessions"""
        logger.info("Starting all sessions...")
        
        started_count = 0
        
        for session in sessions_info:
            session_id = session['session_id']
            
            if self.start_session(session_id):
                started_count += 1
                # Small delay between starts to avoid conflicts
                time.sleep(2)
        
        logger.info(f"Started {started_count}/{len(sessions_info)} sessions")
        return started_count
    
    def show_session_summary(self, sessions_info: List[Dict]):
        """Display a summary of all sessions"""
        print("\n" + "="*80)
        print("MOBILE AUTOMATION SESSIONS SUMMARY")
        print("="*80)
        
        if not sessions_info:
            print("No sessions available.")
            return
        
        for i, session in enumerate(sessions_info, 1):
            print(f"\n{i}. Session ID: {session['session_id']}")
            print(f"   Device: {session['device_name']}")
            print(f"   Platform: {session['platform']}")
            print(f"   Status: {session['status']}")
            print(f"   Ports: Flask:{session['ports']['flask']}, Appium:{session['ports']['appium']}, WDA:{session['ports']['wda']}")
            
            if session['status'] == 'running':
                print(f"   URL: {session['url']}")
            
            print(f"   Created: {session.get('created_at', 'Unknown')}")
        
        print("\n" + "="*80)
    
    def interactive_menu(self, sessions_info: List[Dict]):
        """Show interactive menu for session management"""
        while self.running:
            print("\n" + "="*50)
            print("DEVICE MANAGER - SESSION CONTROL")
            print("="*50)
            
            print("\nAvailable Sessions:")
            for i, session in enumerate(sessions_info, 1):
                status_indicator = "🟢" if session['status'] == 'running' else "🔴"
                print(f"  {i}. {status_indicator} {session['session_id']} - {session['device_name']}")
            
            print("\nOptions:")
            print("  1. Start all sessions")
            print("  2. Start specific session")
            print("  3. Stop specific session")
            print("  4. Show session summary")
            print("  5. Open session manager UI")
            print("  6. Refresh device discovery")
            print("  0. Exit")
            
            try:
                choice = input("\nEnter your choice: ").strip()
                
                if choice == "0":
                    self.running = False
                    break
                elif choice == "1":
                    self.start_all_sessions(sessions_info)
                elif choice == "2":
                    self.handle_start_specific_session(sessions_info)
                elif choice == "3":
                    self.handle_stop_specific_session(sessions_info)
                elif choice == "4":
                    self.show_session_summary(sessions_info)
                elif choice == "5":
                    self.open_session_manager_ui()
                elif choice == "6":
                    devices = self.discover_devices()
                    sessions_info = self.create_sessions_for_devices(devices)
                else:
                    print("Invalid choice. Please try again.")
                    
            except KeyboardInterrupt:
                print("\nExiting...")
                self.running = False
                break
            except Exception as e:
                print(f"Error: {e}")
    
    def handle_start_specific_session(self, sessions_info: List[Dict]):
        """Handle starting a specific session"""
        try:
            session_num = int(input("Enter session number to start: ")) - 1
            if 0 <= session_num < len(sessions_info):
                session = sessions_info[session_num]
                self.start_session(session['session_id'])
            else:
                print("Invalid session number.")
        except ValueError:
            print("Please enter a valid number.")
    
    def handle_stop_specific_session(self, sessions_info: List[Dict]):
        """Handle stopping a specific session"""
        try:
            session_num = int(input("Enter session number to stop: ")) - 1
            if 0 <= session_num < len(sessions_info):
                session = sessions_info[session_num]
                session_id = session['session_id']
                
                if device_manager.stop_session(session_id):
                    print(f"Session {session_id} stopped successfully")
                    session['status'] = 'stopped'
                else:
                    print(f"Failed to stop session {session_id}")
            else:
                print("Invalid session number.")
        except ValueError:
            print("Please enter a valid number.")
    
    def open_session_manager_ui(self):
        """Open the session manager UI in browser"""
        # Find any running session to get a port
        sessions = device_manager.list_sessions()
        running_session = next((s for s in sessions if s['status'] == 'running'), None)
        
        if running_session:
            port = running_session['ports']['flask']
            url = f"http://localhost:{port}/api/session/manager"
            logger.info(f"Opening session manager UI: {url}")
            webbrowser.open(url)
        else:
            print("No running sessions found. Please start a session first.")
    
    def cleanup_and_exit(self):
        """Clean up resources and exit"""
        logger.info("Cleaning up and exiting...")
        
        # Optionally stop all running sessions
        sessions = device_manager.list_sessions()
        running_sessions = [s for s in sessions if s['status'] == 'running']
        
        if running_sessions:
            response = input(f"\nStop {len(running_sessions)} running sessions before exit? (y/N): ")
            if response.lower() == 'y':
                for session in running_sessions:
                    device_manager.stop_session(session['session_id'])
                    logger.info(f"Stopped session {session['session_id']}")
        
        logger.info("Device Manager Launcher exited")

def main():
    """Main entry point"""
    parser = argparse.ArgumentParser(description="Device Manager Launcher with Session Isolation")
    parser.add_argument('--auto-start', action='store_true', 
                       help='Automatically start all sessions without interactive menu')
    parser.add_argument('--no-browser', action='store_true',
                       help='Do not open browser windows automatically')
    parser.add_argument('--summary-only', action='store_true',
                       help='Show session summary and exit')
    
    args = parser.parse_args()
    
    launcher = DeviceManagerLauncher()
    
    try:
        # Discover devices and create sessions
        devices = launcher.discover_devices()
        
        if not devices:
            logger.warning("No devices found. Please connect devices and try again.")
            return
        
        sessions_info = launcher.create_sessions_for_devices(devices)
        
        if not sessions_info:
            logger.error("Failed to create sessions for devices.")
            return
        
        # Show summary
        launcher.show_session_summary(sessions_info)
        
        if args.summary_only:
            return
        
        if args.auto_start:
            # Auto-start all sessions
            launcher.start_all_sessions(sessions_info)
            
            if not args.no_browser:
                # Open session manager UI
                time.sleep(5)  # Wait for sessions to start
                launcher.open_session_manager_ui()
        else:
            # Interactive menu
            launcher.interactive_menu(sessions_info)
    
    except KeyboardInterrupt:
        print("\nInterrupted by user")
    except Exception as e:
        logger.error(f"Unexpected error: {e}")
    finally:
        launcher.cleanup_and_exit()

if __name__ == "__main__":
    main()
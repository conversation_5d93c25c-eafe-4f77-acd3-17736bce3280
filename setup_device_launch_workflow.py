#!/usr/bin/env python3
"""
Setup Device Launch Workflow

This script sets up and configures the complete device launch workflow
including the AppiumDeviceManager and automation app integration.
"""

import os
import sys
import json
import shutil
import logging
from pathlib import Path

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def setup_device_launch_workflow():
    """Set up the complete device launch workflow"""
    logger.info("Setting up Device Launch Workflow...")
    
    # Check required files
    check_required_files()
    
    # Set up directories
    setup_directories()
    
    # Verify permissions
    verify_permissions()
    
    # Test imports
    test_imports()
    
    # Create configuration files
    create_configuration_files()
    
    logger.info("Device Launch Workflow setup completed successfully!")
    
    # Print usage instructions
    print_usage_instructions()

def check_required_files():
    """Check that all required files are present"""
    logger.info("Checking required files...")
    
    required_files = [
        "appium_device_manager.html",
        "device_manager_api.py",
        "start_device_manager.py",
        "test_device_launch_workflow.py",
        "session_isolation_manager.py",
        "enhanced_device_manager.py",
        "unified_device_discovery.py",
        "app/static/js/pre-connected-device.js",
        "app/routes/device_manager_routes.py"
    ]
    
    missing_files = []
    for file_path in required_files:
        if not os.path.exists(file_path):
            missing_files.append(file_path)
        else:
            logger.info(f"✓ {file_path}")
    
    if missing_files:
        logger.error(f"Missing required files: {missing_files}")
        raise FileNotFoundError(f"Missing required files: {missing_files}")
    
    logger.info("All required files are present")

def setup_directories():
    """Create necessary directories"""
    logger.info("Setting up directories...")
    
    directories = [
        "sessions",
        "sessions/temp",
        "logs",
        "app/data",
        "data"
    ]
    
    for directory in directories:
        Path(directory).mkdir(parents=True, exist_ok=True)
        logger.info(f"Created directory: {directory}")

def verify_permissions():
    """Verify file permissions"""
    logger.info("Verifying file permissions...")
    
    executable_files = [
        "start_device_manager.py",
        "device_manager_api.py",
        "test_device_launch_workflow.py",
        "device_manager_launcher.py",
        "session_isolation_manager.py"
    ]
    
    for file_path in executable_files:
        if os.path.exists(file_path):
            os.chmod(file_path, 0o755)
            logger.info(f"Set executable permissions: {file_path}")

def test_imports():
    """Test that all required modules can be imported"""
    logger.info("Testing module imports...")
    
    try:
        # Test core modules
        from session_isolation_manager import session_manager
        logger.info("✓ session_isolation_manager imported successfully")
        
        from enhanced_device_manager import device_manager
        logger.info("✓ enhanced_device_manager imported successfully")
        
        from unified_device_discovery import get_all_devices
        logger.info("✓ unified_device_discovery imported successfully")
        
        # Test Flask and other dependencies
        import flask
        logger.info("✓ Flask imported successfully")
        
        import requests
        logger.info("✓ requests imported successfully")
        
    except ImportError as e:
        logger.error(f"Import error: {e}")
        logger.error("Please ensure all dependencies are installed")
        raise

def create_configuration_files():
    """Create configuration files"""
    logger.info("Creating configuration files...")
    
    # Device manager configuration
    device_manager_config = {
        "device_manager": {
            "port": 9000,
            "auto_open_browser": True,
            "auto_refresh_interval": 30,
            "max_concurrent_sessions": 10
        },
        "session_isolation": {
            "enabled": True,
            "auto_cleanup": True,
            "session_timeout": 3600
        },
        "ui_settings": {
            "hide_device_lists": True,
            "show_session_ids": True,
            "auto_connect_devices": True,
            "simplified_interface": True
        },
        "port_allocation": {
            "base_flask_port": 8080,
            "base_appium_port": 4723,
            "base_wda_port": 8100,
            "port_range_limit": 100
        }
    }
    
    config_file = Path("device_launch_workflow_config.json")
    if not config_file.exists():
        with open(config_file, 'w') as f:
            json.dump(device_manager_config, f, indent=2)
        logger.info(f"Created configuration file: {config_file}")

def print_usage_instructions():
    """Print usage instructions"""
    print("\n" + "="*80)
    print("DEVICE LAUNCH WORKFLOW - SETUP COMPLETE")
    print("="*80)
    
    print("\n🎉 The device launch workflow has been set up successfully!")
    
    print("\n📋 QUICK START GUIDE:")
    print("="*50)
    
    print("\n1. Start the Device Manager:")
    print("   python start_device_manager.py")
    
    print("\n2. Connect your devices:")
    print("   - iOS: Connect via USB and trust the computer")
    print("   - Android: Enable developer mode and USB debugging")
    
    print("\n3. Launch automation apps:")
    print("   - Open Device Manager interface (auto-opens in browser)")
    print("   - Click 'Launch Automation App' for any device")
    print("   - Automation app opens automatically with device pre-connected")
    
    print("\n4. Test the workflow:")
    print("   python test_device_launch_workflow.py")
    
    print("\n🔧 CONFIGURATION:")
    print("="*50)
    print("- Edit 'device_launch_workflow_config.json' to customize settings")
    print("- Device Manager runs on port 9000 by default")
    print("- Automation apps use ports 8080+ (auto-allocated)")
    
    print("\n📖 DOCUMENTATION:")
    print("="*50)
    print("- Read 'DEVICE_LAUNCH_WORKFLOW_README.md' for detailed documentation")
    print("- API endpoints and integration details included")
    
    print("\n🚀 KEY FEATURES:")
    print("="*50)
    print("✓ One-click automation app launch from device manager")
    print("✓ Complete session isolation per device")
    print("✓ Pre-connected device state (no manual connection needed)")
    print("✓ Simplified UI with session ID focus")
    print("✓ Automatic device status tracking")
    print("✓ Real-time session health monitoring")
    
    print("\n⚠️  REQUIREMENTS:")
    print("="*50)
    print("- iOS devices: Xcode and libimobiledevice installed")
    print("- Android devices: ADB installed and developer mode enabled")
    print("- Python 3.7+ with Flask, requests, and other dependencies")
    print("- Available ports: 9000 (device manager), 8080+ (automation apps)")
    
    print("\n🔍 TROUBLESHOOTING:")
    print("="*50)
    print("- If devices not found: Check USB connections and trust/authorization")
    print("- If launch fails: Check port availability and Appium installation")
    print("- If UI issues: Check browser console and JavaScript errors")
    print("- For help: Check logs in device_manager.log and session logs")
    
    print("\n" + "="*80)

def main():
    """Main setup function"""
    print("Device Launch Workflow Setup")
    print("="*40)
    
    try:
        setup_device_launch_workflow()
    except KeyboardInterrupt:
        print("\nSetup interrupted by user")
        sys.exit(1)
    except Exception as e:
        logger.error(f"Setup failed: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
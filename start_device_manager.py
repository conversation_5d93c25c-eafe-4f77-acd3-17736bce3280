#!/usr/bin/env python3
"""
Start Appium Device Manager

This script starts the complete device manager system with the AppiumDeviceManager interface.
"""

import os
import sys
import time
import logging
import webbrowser
import subprocess
from pathlib import Path

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def start_device_manager():
    """Start the Appium Device Manager system"""
    
    print("🚀 Starting Appium Device Manager System")
    print("="*60)
    
    try:
        # Check if required files exist
        required_files = [
            "device_manager_api.py",
            "appium_device_manager.html",
            "session_isolation_manager.py",
            "enhanced_device_manager.py",
            "unified_device_discovery.py"
        ]
        
        missing_files = []
        for file in required_files:
            if not os.path.exists(file):
                missing_files.append(file)
        
        if missing_files:
            logger.error(f"Missing required files: {missing_files}")
            print("❌ Missing required files. Please ensure all components are installed.")
            return False
        
        # Start the device manager API server
        logger.info("Starting Device Manager API server...")
        print("📡 Starting Device Manager API server on port 9000...")
        
        # Start the device manager in the background
        process = subprocess.Popen(
            [sys.executable, "device_manager_api.py"],
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE
        )
        
        # Wait a moment for the server to start
        time.sleep(3)
        
        # Check if process is still running
        if process.poll() is None:
            logger.info("Device Manager API server started successfully")
            print("✅ Device Manager API server started successfully")
            
            # Open the device manager interface in browser
            device_manager_url = "http://localhost:9000"
            logger.info(f"Opening Device Manager interface: {device_manager_url}")
            print(f"🌐 Opening Device Manager interface: {device_manager_url}")
            
            webbrowser.open(device_manager_url)
            
            print("\n" + "="*60)
            print("📱 APPIUM DEVICE MANAGER READY")
            print("="*60)
            print(f"🔗 Interface URL: {device_manager_url}")
            print("📋 Features:")
            print("   ✓ Automatic device discovery")
            print("   ✓ One-click automation app launch")
            print("   ✓ Complete session isolation")
            print("   ✓ Pre-connected device state")
            print("   ✓ Simplified automation UI")
            print("\n📖 Usage:")
            print("   1. Connect your iOS/Android devices")
            print("   2. Click 'Refresh Devices' if needed")
            print("   3. Click 'Launch Automation App' for any device")
            print("   4. Automation app will open automatically with device pre-connected")
            print("\n⚠️  Note: Keep this terminal open to maintain the device manager")
            print("="*60)
            
            # Keep the script running
            try:
                while True:
                    time.sleep(1)
                    
                    # Check if process is still running
                    if process.poll() is not None:
                        logger.error("Device Manager API server stopped unexpectedly")
                        break
                        
            except KeyboardInterrupt:
                print("\n🛑 Shutting down Device Manager...")
                logger.info("Shutting down Device Manager...")
                
                # Terminate the process
                process.terminate()
                try:
                    process.wait(timeout=5)
                except subprocess.TimeoutExpired:
                    process.kill()
                
                print("✅ Device Manager shut down successfully")
                return True
        else:
            # Process exited immediately
            stdout, stderr = process.communicate()
            logger.error("Device Manager API server failed to start")
            logger.error(f"STDOUT: {stdout.decode()}")
            logger.error(f"STDERR: {stderr.decode()}")
            print("❌ Device Manager API server failed to start")
            print("📋 Check the logs above for error details")
            return False
            
    except Exception as e:
        logger.error(f"Error starting Device Manager: {e}")
        print(f"❌ Error starting Device Manager: {e}")
        return False

def main():
    """Main entry point"""
    try:
        success = start_device_manager()
        if not success:
            sys.exit(1)
    except KeyboardInterrupt:
        print("\n👋 Goodbye!")
        sys.exit(0)
    except Exception as e:
        logger.error(f"Unexpected error: {e}")
        print(f"❌ Unexpected error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
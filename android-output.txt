2025-07-02 16:33:28,859 - utils.directory_paths_db - INFO - Directory paths and environments database initialized/verified
2025-07-02 16:33:28,860 - utils.directory_paths_db - INFO - Directory paths and environments database initialized/verified
2025-07-02 16:33:28,860 - config - INFO - Using database path for TEST_CASES: /Users/<USER>/Documents/automation-tool/test_cases
2025-07-02 16:33:28,860 - config - INFO - Using database path for REPORTS: /Users/<USER>/Documents/automation-tool/reports
2025-07-02 16:33:28,861 - config - INFO - Using database path for SCREENSHOTS: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/screenshots
2025-07-02 16:33:28,861 - config - INFO - Using database path for REFERENCE_IMAGES: /Users/<USER>/Documents/automation-tool/reference_images
2025-07-02 16:33:28,861 - config - INFO - Using database path for TEST_SUITES: /Users/<USER>/Documents/automation-tool/test_suites
2025-07-02 16:33:28,862 - config - INFO - Using database path for RESULTS: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/reports/suites
2025-07-02 16:33:28,862 - config - INFO - Using database path for RECORDINGS: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/recordings
2025-07-02 16:33:28,862 - config - INFO - Using database path for TEMP_FILES: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/temp
2025-07-02 16:33:28,863 - config - INFO - Using database path for FILES_TO_PUSH: /Users/<USER>/Documents/automation-tool/files_to_push
2025-07-02 16:33:28,863 - __main__ - INFO - Using instance-specific database paths with suffix: _port_8081
2025-07-02 16:33:28,863 - __main__ - INFO - Using custom ports (Flask: 8081, Appium: 4723, WDA: 8100) - preserving existing processes for multi-instance support
2025-07-02 16:33:28,863 - __main__ - INFO - Skipping process termination when using custom ports for multi-instance support
2025-07-02 16:33:30,258 - appium_device_controller - WARNING - TouchAction not available in this Appium Python Client version - using W3C Actions fallback
2025-07-02 16:33:30,309 - AppiumDeviceController - INFO - Successfully imported Airtest library.
2025-07-02 16:33:30,830 - app_android.utils.directory_paths_db - INFO - Directory paths and environments database initialized/verified
2025-07-02 16:33:30,850 - app - INFO - Using directories from config.py:
2025-07-02 16:33:30,850 - app - INFO -   - TEST_CASES_DIR: /Users/<USER>/Documents/automation-tool/test_cases
2025-07-02 16:33:30,851 - app - INFO -   - REFERENCE_IMAGES_DIR: /Users/<USER>/Documents/automation-tool/reference_images
2025-07-02 16:33:30,851 - app - INFO -   - SCREENSHOTS_DIR: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/screenshots
[2025-07-02 16:33:30,854] INFO in database: === UPDATING TEST_STEPS TABLE SCHEMA ===
[2025-07-02 16:33:30,855] INFO in database: Test_steps table schema updated successfully
[2025-07-02 16:33:30,855] INFO in database: === UPDATING SCREENSHOTS TABLE SCHEMA ===
[2025-07-02 16:33:30,855] INFO in database: Screenshots table schema updated successfully
[2025-07-02 16:33:30,855] INFO in database: === UPDATING EXECUTION_TRACKING TABLE SCHEMA ===
[2025-07-02 16:33:30,856] INFO in database: step_idx column already exists in execution_tracking table
[2025-07-02 16:33:30,856] INFO in database: action_type column already exists in execution_tracking table
[2025-07-02 16:33:30,856] INFO in database: action_params column already exists in execution_tracking table
[2025-07-02 16:33:30,856] INFO in database: action_id column already exists in execution_tracking table
[2025-07-02 16:33:30,856] INFO in database: Successfully updated execution_tracking table schema
[2025-07-02 16:33:30,856] INFO in database: Database initialized successfully
[2025-07-02 16:33:30,856] INFO in database: Checking initial database state...
[2025-07-02 16:33:30,857] INFO in database: Database state: 0 suites, 0 cases, 1199 steps, 1 screenshots, 1199 tracking entries
[2025-07-02 16:33:30,857] INFO in database: === UPDATING TEST_STEPS TABLE SCHEMA ===
[2025-07-02 16:33:30,858] INFO in database: Test_steps table schema updated successfully
[2025-07-02 16:33:30,858] INFO in database: === UPDATING SCREENSHOTS TABLE SCHEMA ===
[2025-07-02 16:33:30,858] INFO in database: Screenshots table schema updated successfully
[2025-07-02 16:33:30,858] INFO in database: === UPDATING EXECUTION_TRACKING TABLE SCHEMA ===
[2025-07-02 16:33:30,859] INFO in database: step_idx column already exists in execution_tracking table
[2025-07-02 16:33:30,859] INFO in database: action_type column already exists in execution_tracking table
[2025-07-02 16:33:30,859] INFO in database: action_params column already exists in execution_tracking table
[2025-07-02 16:33:30,859] INFO in database: action_id column already exists in execution_tracking table
[2025-07-02 16:33:30,859] INFO in database: Successfully updated execution_tracking table schema
[2025-07-02 16:33:30,859] INFO in database: Database initialized successfully
[2025-07-02 16:33:30,859] INFO in database: === UPDATING EXECUTION_TRACKING TABLE SCHEMA ===
[2025-07-02 16:33:30,859] INFO in database: step_idx column already exists in execution_tracking table
[2025-07-02 16:33:30,860] INFO in database: action_type column already exists in execution_tracking table
[2025-07-02 16:33:30,860] INFO in database: action_params column already exists in execution_tracking table
[2025-07-02 16:33:30,860] INFO in database: action_id column already exists in execution_tracking table
[2025-07-02 16:33:30,860] INFO in database: Successfully updated execution_tracking table schema
[2025-07-02 16:33:30,860] INFO in database: === UPDATING SCREENSHOTS TABLE SCHEMA ===
[2025-07-02 16:33:30,860] INFO in database: Screenshots table schema updated successfully
[2025-07-02 16:33:30,928] INFO in global_values_db: Using global values database at: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app/data/global_values_port_8081.db
[2025-07-02 16:33:30,928] INFO in global_values_db: Global values database initialized successfully
[2025-07-02 16:33:30,928] INFO in global_values_db: Using global values from config.py
[2025-07-02 16:33:30,928] INFO in global_values_db: Updated default values from config.py: {'default_element_timeout': 60, 'Test Run Retry': 2, 'Auto Rerun Failed': False, 'Test Case Delay': 15, 'Max Step Execution Time': 300}
[2025-07-02 16:33:30,985] INFO in appium_device_controller: Initialized AppiumDeviceController with Appium port: 4723, WDA port: 8100
[2025-07-02 16:33:31,001] INFO in appium_device_controller: Appium server is running and ready
[2025-07-02 16:33:31,001] INFO in appium_device_controller: Appium server is already running and responsive
Starting Mobile App Automation Tool (Android)...
Configuration:
  - Flask server port: 8081
  - Appium server port: 4723
  - WebDriverAgent port: 8100
Open your web browser and navigate to: http://localhost:8081
 * Serving Flask app 'app'
 * Debug mode: on
[2025-07-02 16:33:31,024] INFO in _internal: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:8081
 * Running on http://************:8081
[2025-07-02 16:33:31,024] INFO in _internal: [33mPress CTRL+C to quit[0m
[2025-07-02 16:33:32,330] INFO in directory_paths_db: Directory paths and environments database initialized/verified
[2025-07-02 16:33:32,330] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-07-02 16:33:32,331] INFO in _internal: 127.0.0.1 - - [02/Jul/2025 16:33:32] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-07-02 16:33:35,404] INFO in _internal: 127.0.0.1 - - [02/Jul/2025 16:33:35] "GET / HTTP/1.1" 200 -
[2025-07-02 16:33:35,425] INFO in _internal: 127.0.0.1 - - [02/Jul/2025 16:33:35] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
[2025-07-02 16:33:35,426] INFO in _internal: 127.0.0.1 - - [02/Jul/2025 16:33:35] "[36mGET /static/css/test-cases-styles.css HTTP/1.1[0m" 304 -
[2025-07-02 16:33:35,428] INFO in _internal: 127.0.0.1 - - [02/Jul/2025 16:33:35] "[36mGET /static/css/test-suites-styles.css HTTP/1.1[0m" 304 -
[2025-07-02 16:33:35,430] INFO in _internal: 127.0.0.1 - - [02/Jul/2025 16:33:35] "[36mGET /static/css/test-case.css HTTP/1.1[0m" 304 -
[2025-07-02 16:33:35,434] INFO in _internal: 127.0.0.1 - - [02/Jul/2025 16:33:35] "[36mGET /static/css/execution-overlay.css HTTP/1.1[0m" 304 -
[2025-07-02 16:33:35,438] INFO in _internal: 127.0.0.1 - - [02/Jul/2025 16:33:35] "[36mGET /static/css/modern-styles.css HTTP/1.1[0m" 304 -
[2025-07-02 16:33:35,438] INFO in _internal: 127.0.0.1 - - [02/Jul/2025 16:33:35] "[36mGET /static/css/fixed-device-screen.css HTTP/1.1[0m" 304 -
[2025-07-02 16:33:35,440] INFO in _internal: 127.0.0.1 - - [02/Jul/2025 16:33:35] "[36mGET /static/css/actionStyles.css HTTP/1.1[0m" 304 -
[2025-07-02 16:33:35,442] INFO in _internal: 127.0.0.1 - - [02/Jul/2025 16:33:35] "[36mGET /static/js/modules/uiUtils.js HTTP/1.1[0m" 304 -
[2025-07-02 16:33:35,444] INFO in _internal: 127.0.0.1 - - [02/Jul/2025 16:33:35] "[36mGET /static/js/modules/actionFormManager.js HTTP/1.1[0m" 304 -
[2025-07-02 16:33:35,445] INFO in _internal: 127.0.0.1 - - [02/Jul/2025 16:33:35] "[36mGET /static/js/modules/reportAndFormUtils.js HTTP/1.1[0m" 304 -
[2025-07-02 16:33:35,448] INFO in _internal: 127.0.0.1 - - [02/Jul/2025 16:33:35] "[36mGET /static/js/export-run.js HTTP/1.1[0m" 304 -
[2025-07-02 16:33:35,449] INFO in _internal: 127.0.0.1 - - [02/Jul/2025 16:33:35] "[36mGET /static/img/no_device.png HTTP/1.1[0m" 304 -
[2025-07-02 16:33:35,450] INFO in _internal: 127.0.0.1 - - [02/Jul/2025 16:33:35] "[36mGET /static/js/utils.js HTTP/1.1[0m" 304 -
[2025-07-02 16:33:35,460] INFO in _internal: 127.0.0.1 - - [02/Jul/2025 16:33:35] "[36mGET /static/js/modules/ElementInteractions.js HTTP/1.1[0m" 304 -
[2025-07-02 16:33:35,460] INFO in _internal: 127.0.0.1 - - [02/Jul/2025 16:33:35] "[36mGET /static/js/fixed-device-screen.js HTTP/1.1[0m" 304 -
[2025-07-02 16:33:35,460] INFO in _internal: 127.0.0.1 - - [02/Jul/2025 16:33:35] "[36mGET /static/js/action-manager.js HTTP/1.1[0m" 304 -
[2025-07-02 16:33:35,462] INFO in _internal: 127.0.0.1 - - [02/Jul/2025 16:33:35] "[36mGET /static/js/execution-overlay.js HTTP/1.1[0m" 304 -
[2025-07-02 16:33:35,464] INFO in _internal: 127.0.0.1 - - [02/Jul/2025 16:33:35] "[36mGET /static/js/modules/TestCaseManager.js HTTP/1.1[0m" 304 -
[2025-07-02 16:33:35,464] INFO in _internal: 127.0.0.1 - - [02/Jul/2025 16:33:35] "[36mGET /static/js/execution-manager.js HTTP/1.1[0m" 304 -
[2025-07-02 16:33:35,470] INFO in _internal: 127.0.0.1 - - [02/Jul/2025 16:33:35] "[36mGET /static/guide/faq_guide.html HTTP/1.1[0m" 304 -
[2025-07-02 16:33:35,471] INFO in _internal: 127.0.0.1 - - [02/Jul/2025 16:33:35] "[36mGET /static/js/action-description.js HTTP/1.1[0m" 304 -
[2025-07-02 16:33:35,473] INFO in _internal: 127.0.0.1 - - [02/Jul/2025 16:33:35] "[36mGET /static/js/multi-step-action.js HTTP/1.1[0m" 304 -
[2025-07-02 16:33:35,476] INFO in _internal: 127.0.0.1 - - [02/Jul/2025 16:33:35] "[36mGET /static/js/repeat-steps-action.js HTTP/1.1[0m" 304 -
[2025-07-02 16:33:35,477] INFO in _internal: 127.0.0.1 - - [02/Jul/2025 16:33:35] "[36mGET /static/js/hook-action.js HTTP/1.1[0m" 304 -
[2025-07-02 16:33:35,479] INFO in _internal: 127.0.0.1 - - [02/Jul/2025 16:33:35] "[36mGET /static/js/modules/fallback-locators.js HTTP/1.1[0m" 304 -
[2025-07-02 16:33:35,483] INFO in _internal: 127.0.0.1 - - [02/Jul/2025 16:33:35] "[36mGET /static/js/modules/tap-fallback-manager.js HTTP/1.1[0m" 304 -
[2025-07-02 16:33:35,484] INFO in _internal: 127.0.0.1 - - [02/Jul/2025 16:33:35] "[36mGET /static/js/random-data-generator.js HTTP/1.1[0m" 304 -
[2025-07-02 16:33:35,487] INFO in _internal: 127.0.0.1 - - [02/Jul/2025 16:33:35] "[36mGET /static/js/main.js HTTP/1.1[0m" 304 -
[2025-07-02 16:33:35,491] INFO in _internal: 127.0.0.1 - - [02/Jul/2025 16:33:35] "[36mGET /static/js/settings.js HTTP/1.1[0m" 304 -
[2025-07-02 16:33:35,492] INFO in _internal: 127.0.0.1 - - [02/Jul/2025 16:33:35] "[36mGET /static/js/test_suites.js HTTP/1.1[0m" 304 -
[2025-07-02 16:33:35,493] INFO in _internal: 127.0.0.1 - - [02/Jul/2025 16:33:35] "[36mGET /static/js/environment-variables.js HTTP/1.1[0m" 304 -
[2025-07-02 16:33:35,523] INFO in _internal: 127.0.0.1 - - [02/Jul/2025 16:33:35] "GET /api/random_data/generators HTTP/1.1" 200 -
[2025-07-02 16:33:35,527] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-07-02 16:33:35,528] INFO in _internal: 127.0.0.1 - - [02/Jul/2025 16:33:35] "GET /api/environments HTTP/1.1" 200 -
[2025-07-02 16:33:35,534] INFO in _internal: 127.0.0.1 - - [02/Jul/2025 16:33:35] "GET /api/reference_images HTTP/1.1" 200 -
[2025-07-02 16:33:35,550] INFO in _internal: 127.0.0.1 - - [02/Jul/2025 16:33:35] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-07-02 16:33:35,554] INFO in _internal: 127.0.0.1 - - [02/Jul/2025 16:33:35] "GET /api/environment_variables HTTP/1.1" 200 -
[2025-07-02 16:33:35,558] INFO in _internal: 127.0.0.1 - - [02/Jul/2025 16:33:35] "GET /api/random_data/generators HTTP/1.1" 200 -
[2025-07-02 16:33:35,559] INFO in _internal: 127.0.0.1 - - [02/Jul/2025 16:33:35] "GET /api/settings HTTP/1.1" 200 -
[2025-07-02 16:33:35,568] INFO in _internal: 127.0.0.1 - - [02/Jul/2025 16:33:35] "[36mGET /static/img/favicon.ico HTTP/1.1[0m" 304 -
[2025-07-02 16:33:35,571] INFO in _internal: 127.0.0.1 - - [02/Jul/2025 16:33:35] "GET /api/reference_images HTTP/1.1" 200 -
[2025-07-02 16:33:35,578] INFO in _internal: 127.0.0.1 - - [02/Jul/2025 16:33:35] "GET /api/test_suites/list HTTP/1.1" 200 -
[2025-07-02 16:33:35,579] INFO in _internal: 127.0.0.1 - - [02/Jul/2025 16:33:35] "GET /api/environments/current HTTP/1.1" 200 -
[2025-07-02 16:33:35,583] INFO in _internal: 127.0.0.1 - - [02/Jul/2025 16:33:35] "GET /api/reference_images HTTP/1.1" 200 -
[2025-07-02 16:33:35,595] INFO in _internal: 127.0.0.1 - - [02/Jul/2025 16:33:35] "GET /api/reference_images HTTP/1.1" 200 -
[2025-07-02 16:33:35,602] INFO in _internal: 127.0.0.1 - - [02/Jul/2025 16:33:35] "GET /api/reference_images HTTP/1.1" 200 -
[2025-07-02 16:33:35,603] INFO in _internal: 127.0.0.1 - - [02/Jul/2025 16:33:35] "GET /api/environments/2/variables HTTP/1.1" 200 -
[2025-07-02 16:33:35,613] INFO in _internal: 127.0.0.1 - - [02/Jul/2025 16:33:35] "GET /api/reference_images HTTP/1.1" 200 -
[2025-07-02 16:33:35,660] INFO in _internal: 127.0.0.1 - - [02/Jul/2025 16:33:35] "GET /api/test_cases_for_multi_step HTTP/1.1" 200 -
[2025-07-02 16:33:35,672] INFO in _internal: 127.0.0.1 - - [02/Jul/2025 16:33:35] "GET /api/recording/list HTTP/1.1" 200 -
[2025-07-02 16:33:35,689] INFO in _internal: 127.0.0.1 - - [02/Jul/2025 16:33:35] "GET /api/tools/scan-databases HTTP/1.1" 200 -
[2025-07-02 16:33:35,703] INFO in _internal: 127.0.0.1 - - [02/Jul/2025 16:33:35] "GET /api/test_cases_for_multi_step HTTP/1.1" 200 -
[2025-07-02 16:33:36,772] INFO in _internal: 127.0.0.1 - - [02/Jul/2025 16:33:36] "GET /api/devices HTTP/1.1" 200 -
[2025-07-02 16:33:38,578] INFO in appium_device_controller: Initialized AppiumDeviceController with Appium port: 4723, WDA port: 8100
[2025-07-02 16:33:38,585] INFO in appium_device_controller: Appium server is running and ready
[2025-07-02 16:33:38,585] INFO in appium_device_controller: Appium server is already running and responsive
[2025-07-02 16:33:38,585] INFO in appium_device_controller: Connecting to device: PJTCI7EMSSONYPU8 with options: None, platform hint: Android
[2025-07-02 16:33:38,585] INFO in appium_device_controller: Connection attempt 1/3
[2025-07-02 16:33:38,585] INFO in appium_device_controller: Using provided platform hint: Android
[2025-07-02 16:33:38,585] INFO in appium_device_controller: Added Android-specific UiAutomator2 capabilities
[2025-07-02 16:33:38,585] INFO in appium_device_controller: Desired capabilities: {'platformName': 'Android', 'deviceName': 'PJTCI7EMSSONYPU8', 'udid': 'PJTCI7EMSSONYPU8', 'newCommandTimeout': 300, 'noReset': True, 'automationName': 'UiAutomator2', 'uiautomator2ServerLaunchTimeout': 60000, 'uiautomator2ServerInstallTimeout': 60000, 'adbExecTimeout': 60000, 'skipServerInstallation': False, 'skipDeviceInitialization': False, 'ignoreHiddenApiPolicyError': True, 'disableWindowAnimation': True, 'autoGrantPermissions': True, 'dontStopAppOnReset': True}
[2025-07-02 16:33:38,586] INFO in appium_device_controller: Connecting to Appium server with options: {'platformName': 'Android', 'appium:deviceName': 'PJTCI7EMSSONYPU8', 'appium:udid': 'PJTCI7EMSSONYPU8', 'appium:newCommandTimeout': 300, 'appium:noReset': True, 'appium:automationName': 'UiAutomator2', 'appium:uiautomator2ServerLaunchTimeout': 60000, 'appium:uiautomator2ServerInstallTimeout': 60000, 'appium:adbExecTimeout': 60000, 'appium:skipServerInstallation': False, 'appium:skipDeviceInitialization': False, 'appium:ignoreHiddenApiPolicyError': True, 'appium:disableWindowAnimation': True, 'appium:autoGrantPermissions': True, 'appium:dontStopAppOnReset': True}
[2025-07-02 16:33:38,586] INFO in appium_device_controller: Connection attempt 1/3
[2025-07-02 16:33:40,519] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-07-02 16:33:40,521] INFO in _internal: 127.0.0.1 - - [02/Jul/2025 16:33:40] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-07-02 16:33:40,524] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-07-02 16:33:40,525] INFO in _internal: 127.0.0.1 - - [02/Jul/2025 16:33:40] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-07-02 16:33:42,461] INFO in appium_device_controller: Connection verified with capabilities: Android
[2025-07-02 16:33:42,461] INFO in appium_device_controller: Initializing platform helpers for Android
[2025-07-02 16:33:42,461] INFO in appium_device_controller: Getting device dimensions
[2025-07-02 16:33:42,481] INFO in appium_device_controller: Got device dimensions from Appium: 1080x2400
[2025-07-02 16:33:42,481] INFO in appium_device_controller: Device dimensions: (1080, 2400)
[2025-07-02 16:33:42,488] INFO in appium_device_controller: Initialized ImageMatcher for Android device: PJTCI7EMSSONYPU8
[2025-07-02 16:33:42,488] INFO in appium_device_controller: Initializing Android-specific helpers
[2025-07-02 16:33:42,488] INFO in appium_device_controller: Android version: 12.0
[2025-07-02 16:33:42,488] INFO in appium_device_controller: Setting up UiAutomator2 support
[2025-07-02 16:33:42,489] ERROR in appium_device_controller: Error running ADB command: can only concatenate list (not "str") to list
[2025-07-02 16:33:42,489] WARNING in appium_device_controller: Error setting up ADB: argument of type 'NoneType' is not iterable
[2025-07-02 16:33:42,489] INFO in appium_device_controller: Platform helpers initialization completed
[2025-07-02 16:33:42,489] INFO in appium_device_controller: Successfully connected to device on attempt 1
[2025-07-02 16:33:42,489] INFO in action_factory: Registered basic actions: tap, wait
[2025-07-02 16:33:42,491] INFO in action_factory: Special case: Registering tap_if_image_exists_action.py as 'tapIfImageExists'
[2025-07-02 16:33:42,491] INFO in action_factory: Registered action handler for 'tapIfImageExists'
[2025-07-02 16:33:42,492] INFO in action_factory: Registered action handler for 'multiStep'
[2025-07-02 16:33:42,492] INFO in action_factory: Special case: Registering cleanup_steps_action.py as 'cleanupSteps'
[2025-07-02 16:33:42,492] INFO in action_factory: Registered action handler for 'cleanupSteps'
[2025-07-02 16:33:42,493] INFO in action_factory: Registered action handler for 'swipe'
[2025-07-02 16:33:42,494] INFO in action_factory: Registered action handler for 'getParam'
[2025-07-02 16:33:42,494] INFO in action_factory: Registered action handler for 'wait'
[2025-07-02 16:33:42,495] INFO in action_factory: Registered action handler for 'terminateApp'
[2025-07-02 16:33:42,496] INFO in action_factory: Registered action handler for 'doubleClickImage'
[2025-07-02 16:33:42,496] INFO in action_factory: Registered action handler for 'uninstallApp'
[2025-07-02 16:33:42,497] INFO in action_factory: Registered action handler for 'text'
[2025-07-02 16:33:42,499] ERROR in action_factory: Failed to import module for tap_if_text_exists_action
[2025-07-02 16:33:42,501] INFO in action_factory: Registered action handler for 'waitTill'
[2025-07-02 16:33:42,502] INFO in action_factory: Registered action handler for 'hookAction'
[2025-07-02 16:33:42,502] INFO in action_factory: Registered action handler for 'inputText'
[2025-07-02 16:33:42,504] INFO in global_values_db: Using global values database at: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app/data/global_values_port_8081.db
[2025-07-02 16:33:42,504] INFO in global_values_db: Global values database initialized successfully
[2025-07-02 16:33:42,504] INFO in global_values_db: Using global values from config.py
[2025-07-02 16:33:42,504] INFO in global_values_db: Updated default values from config.py: {'default_element_timeout': 60, 'Test Run Retry': 2, 'Auto Rerun Failed': False, 'Test Case Delay': 15, 'Max Step Execution Time': 300}
[2025-07-02 16:33:42,506] INFO in action_factory: Registered action handler for 'setParam'
[2025-07-02 16:33:42,507] INFO in action_factory: Special case: Registering repeat_steps_action.py as 'repeatSteps'
[2025-07-02 16:33:42,507] INFO in action_factory: Registered action handler for 'repeatSteps'
[2025-07-02 16:33:42,508] INFO in action_factory: Special case: Registering android_functions_action.py as 'androidFunctions'
[2025-07-02 16:33:42,508] INFO in action_factory: Registered action handler for 'androidFunctions'
[2025-07-02 16:33:42,509] INFO in action_factory: Registered action handler for 'swipeTillVisible'
[2025-07-02 16:33:42,509] INFO in action_factory: Registered action handler for 'clickImage'
[2025-07-02 16:33:42,510] INFO in action_factory: Registered action handler for 'tap'
[2025-07-02 16:33:42,511] INFO in action_factory: Registered action handler for 'ifElseSteps'
[2025-07-02 16:33:42,512] INFO in action_factory: Special case: Registering take_screenshot_action.py as 'takeScreenshot'
[2025-07-02 16:33:42,512] INFO in action_factory: Registered action handler for 'takeScreenshot'
[2025-07-02 16:33:42,512] INFO in action_factory: Special case: Registering tap_if_locator_exists_action.py as 'tapIfLocatorExists'
[2025-07-02 16:33:42,512] INFO in action_factory: Registered action handler for 'tapIfLocatorExists'
[2025-07-02 16:33:42,513] INFO in action_factory: Registered action handler for 'tapAndType'
[2025-07-02 16:33:42,514] INFO in action_factory: Special case: Registering tap_on_text_action.py as 'tapOnText'
[2025-07-02 16:33:42,514] INFO in action_factory: Registered action handler for 'tapOnText'
[2025-07-02 16:33:42,515] INFO in action_factory: Registered action handler for 'launchApp'
[2025-07-02 16:33:42,515] INFO in action_factory: Special case: Registering info_action.py as 'info'
[2025-07-02 16:33:42,515] INFO in action_factory: Registered action handler for 'info'
[2025-07-02 16:33:42,516] INFO in action_factory: Registered action handler for 'waitElement'
[2025-07-02 16:33:42,517] INFO in action_factory: Registered action handler for 'compareValue'
[2025-07-02 16:33:42,518] INFO in action_factory: Registered action handler for 'deviceBack'
[2025-07-02 16:33:42,518] INFO in action_factory: Special case: Registering check_if_exists_action.py as 'exists'
[2025-07-02 16:33:42,518] INFO in action_factory: Registered action handler for 'exists'
[2025-07-02 16:33:42,519] INFO in action_factory: Registered action handler for 'clickElement'
[2025-07-02 16:33:42,520] INFO in action_factory: Registered action handler for 'randomData'
[2025-07-02 16:33:42,521] INFO in action_factory: Registered action handler for 'getValue'
[2025-07-02 16:33:42,521] INFO in action_factory: Registered action handler for 'test'
[2025-07-02 16:33:42,522] INFO in action_factory: Registered action handler for 'restartApp'
[2025-07-02 16:33:42,523] INFO in action_factory: Special case: Registering double_tap_action.py as 'doubleTap'
[2025-07-02 16:33:42,523] INFO in action_factory: Registered action handler for 'doubleTap'
[2025-07-02 16:33:42,523] INFO in action_factory: Registered action types: ['tap', 'wait', 'tapIfImageExists', 'multiStep', 'cleanupSteps', 'swipe', 'getParam', 'terminateApp', 'doubleClickImage', 'uninstallApp', 'text', 'waitTill', 'hookAction', 'inputText', 'setParam', 'repeatSteps', 'androidFunctions', 'swipeTillVisible', 'clickImage', 'ifElseSteps', 'takeScreenshot', 'tapIfLocatorExists', 'tapAndType', 'tapOnText', 'launchApp', 'info', 'waitElement', 'compareValue', 'deviceBack', 'exists', 'clickElement', 'randomData', 'getValue', 'test', 'restartApp', 'doubleTap']
[2025-07-02 16:33:42,523] INFO in action_factory: Handler for 'tap': TapAction
[2025-07-02 16:33:42,523] INFO in action_factory: Handler for 'wait': WaitAction
[2025-07-02 16:33:42,523] INFO in action_factory: Handler for 'tapIfImageExists': TapIfImageExistsAction
[2025-07-02 16:33:42,523] INFO in action_factory: Handler for 'multiStep': MultiStepAction
[2025-07-02 16:33:42,523] INFO in action_factory: Handler for 'cleanupSteps': CleanupStepsAction
[2025-07-02 16:33:42,523] INFO in action_factory: Handler for 'swipe': SwipeAction
[2025-07-02 16:33:42,523] INFO in action_factory: Handler for 'getParam': GetParamAction
[2025-07-02 16:33:42,523] INFO in action_factory: Handler for 'terminateApp': TerminateAppAction
[2025-07-02 16:33:42,523] INFO in action_factory: Handler for 'doubleClickImage': DoubleClickImageAction
[2025-07-02 16:33:42,523] INFO in action_factory: Handler for 'uninstallApp': UninstallAppAction
[2025-07-02 16:33:42,523] INFO in action_factory: Handler for 'text': TextAction
[2025-07-02 16:33:42,523] INFO in action_factory: Handler for 'waitTill': WaitTillAction
[2025-07-02 16:33:42,523] INFO in action_factory: Handler for 'hookAction': HookAction
[2025-07-02 16:33:42,523] INFO in action_factory: Handler for 'inputText': InputTextAction
[2025-07-02 16:33:42,523] INFO in action_factory: Handler for 'setParam': SetParamAction
[2025-07-02 16:33:42,524] INFO in action_factory: Handler for 'repeatSteps': RepeatStepsAction
[2025-07-02 16:33:42,524] INFO in action_factory: Handler for 'androidFunctions': AndroidFunctionsAction
[2025-07-02 16:33:42,524] INFO in action_factory: Handler for 'swipeTillVisible': SwipeTillVisibleAction
[2025-07-02 16:33:42,524] INFO in action_factory: Handler for 'clickImage': ClickImageAction
[2025-07-02 16:33:42,524] INFO in action_factory: Handler for 'ifElseSteps': IfElseStepsAction
[2025-07-02 16:33:42,524] INFO in action_factory: Handler for 'takeScreenshot': TakeScreenshotAction
[2025-07-02 16:33:42,524] INFO in action_factory: Handler for 'tapIfLocatorExists': TapIfLocatorExistsAction
[2025-07-02 16:33:42,524] INFO in action_factory: Handler for 'tapAndType': TapAndTypeAction
[2025-07-02 16:33:42,524] INFO in action_factory: Handler for 'tapOnText': TapOnTextAction
[2025-07-02 16:33:42,524] INFO in action_factory: Handler for 'launchApp': LaunchAppAction
[2025-07-02 16:33:42,524] INFO in action_factory: Handler for 'info': InfoAction
[2025-07-02 16:33:42,524] INFO in action_factory: Handler for 'waitElement': WaitElementAction
[2025-07-02 16:33:42,524] INFO in action_factory: Handler for 'compareValue': CompareValueAction
[2025-07-02 16:33:42,524] INFO in action_factory: Handler for 'deviceBack': DeviceBackAction
[2025-07-02 16:33:42,524] INFO in action_factory: Handler for 'exists': CheckIfExistsAction
[2025-07-02 16:33:42,524] INFO in action_factory: Handler for 'clickElement': ClickElementAction
[2025-07-02 16:33:42,524] INFO in action_factory: Handler for 'randomData': RandomDataAction
[2025-07-02 16:33:42,524] INFO in action_factory: Handler for 'getValue': GetValueAction
[2025-07-02 16:33:42,524] INFO in action_factory: Handler for 'test': TestAction
[2025-07-02 16:33:42,524] INFO in action_factory: Handler for 'restartApp': RestartAppAction
[2025-07-02 16:33:42,524] INFO in action_factory: Handler for 'doubleTap': DoubleTapAction
[2025-07-02 16:33:42,524] INFO in appium_device_controller: Initializing Airtest connection for device: PJTCI7EMSSONYPU8...
[2025-07-02 16:33:42,524] INFO in appium_device_controller: Connecting to Android device with Airtest using URI: android://127.0.0.1:5037/PJTCI7EMSSONYPU8?cap_method=JAVACAP&ori_method=ADBORI
[2025-07-02 16:33:42,669] ERROR in appium_device_controller: Failed to connect to Android device with Airtest: device idx not found in: ['PJTCI7EMSSONYPU8'] or [0]
[2025-07-02 16:33:42,670] WARNING in appium_device_controller: No action_id provided for screenshot - this should not happen
[2025-07-02 16:33:42,670] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/temp/screenshots/placeholder.png (save_debug=False)
[2025-07-02 16:33:42,670] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
2025-07-02 16:33:42,670 - image_matcher - INFO - Taking ADB screenshot for device: PJTCI7EMSSONYPU8
[2025-07-02 16:33:42,670] INFO in image_matcher: Taking ADB screenshot for device: PJTCI7EMSSONYPU8
2025-07-02 16:33:43,719 - image_matcher - INFO - Android screenshot saved to: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/temp/debug_images/screenshot_1751438022.png
[2025-07-02 16:33:43,719] INFO in image_matcher: Android screenshot saved to: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/temp/debug_images/screenshot_1751438022.png
[2025-07-02 16:33:43,720] INFO in appium_device_controller: Screenshot taken with ImageMatcher and copied to /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/temp/screenshots/placeholder.png
[2025-07-02 16:33:43,721] INFO in appium_device_controller: Screenshot with action_id placeholder already exists in database, skipping save
[2025-07-02 16:33:43,721] INFO in appium_device_controller: Generated screenshot URL: /screenshots/placeholder.png
[2025-07-02 16:33:43,752] INFO in _internal: 127.0.0.1 - - [02/Jul/2025 16:33:43] "POST /api/device/connect HTTP/1.1" 200 -
[2025-07-02 16:33:44,763] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png (save_debug=False)
[2025-07-02 16:33:44,764] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
2025-07-02 16:33:44,764 - image_matcher - INFO - Taking ADB screenshot for device: PJTCI7EMSSONYPU8
[2025-07-02 16:33:44,764] INFO in image_matcher: Taking ADB screenshot for device: PJTCI7EMSSONYPU8
[2025-07-02 16:33:45,520] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-07-02 16:33:45,522] INFO in _internal: 127.0.0.1 - - [02/Jul/2025 16:33:45] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-07-02 16:33:45,524] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-07-02 16:33:45,525] INFO in _internal: 127.0.0.1 - - [02/Jul/2025 16:33:45] "GET /api/reports/latest HTTP/1.1" 200 -
2025-07-02 16:33:45,590 - image_matcher - INFO - Android screenshot saved to: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/temp/debug_images/screenshot_1751438024.png
[2025-07-02 16:33:45,590] INFO in image_matcher: Android screenshot saved to: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/temp/debug_images/screenshot_1751438024.png
[2025-07-02 16:33:45,591] INFO in appium_device_controller: Screenshot taken with ImageMatcher and copied to /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-07-02 16:33:45,591] WARNING in appium_device_controller: Not saving screenshot to database because action_id is missing
[2025-07-02 16:33:45,591] INFO in appium_device_controller: Generated screenshot URL: /screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-07-02 16:33:45,592] INFO in _internal: 127.0.0.1 - - [02/Jul/2025 16:33:45] "GET /screenshot?deviceId=PJTCI7EMSSONYPU8&clientSessionId=client_1751438015515_6u2y6m90p_1751435869923_j6gjdnok3&t=1751438024759 HTTP/1.1" 200 -
[2025-07-02 16:33:49,419] INFO in _internal: 127.0.0.1 - - [02/Jul/2025 16:33:49] "GET /api/recording/list HTTP/1.1" 200 -
[2025-07-02 16:33:49,444] INFO in _internal: 127.0.0.1 - - [02/Jul/2025 16:33:49] "GET /api/recording/list HTTP/1.1" 200 -
[2025-07-02 16:33:50,519] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-07-02 16:33:50,520] INFO in _internal: 127.0.0.1 - - [02/Jul/2025 16:33:50] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-07-02 16:33:50,523] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-07-02 16:33:50,524] INFO in _internal: 127.0.0.1 - - [02/Jul/2025 16:33:50] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-07-02 16:33:50,909] INFO in _internal: 127.0.0.1 - - [02/Jul/2025 16:33:50] "GET /api/test_cases/load/Postcode_Flow_AU_ANDROID_20250702070922.json HTTP/1.1" 200 -
[2025-07-02 16:33:50,920] INFO in _internal: 127.0.0.1 - - [02/Jul/2025 16:33:50] "POST /api/screenshots/delete_all HTTP/1.1" 200 -
[2025-07-02 16:33:50,931] INFO in _internal: 127.0.0.1 - - [02/Jul/2025 16:33:50] "GET /api/test_cases/load/Kmart_AU_Cleanup_20250626204013.json HTTP/1.1" 200 -
[2025-07-02 16:33:55,021] INFO in player: Executing action: {'action_id': 'H9fy9qcFbZ', 'executionTime': '3237ms', 'package_id': 'au.com.kmart', 'timestamp': 1746597492636, 'type': 'restartApp'}
[2025-07-02 16:33:55,045] INFO in app: Using directories from config.py:
[2025-07-02 16:33:55,045] INFO in app:   - TEST_CASES_DIR: /Users/<USER>/Documents/automation-tool/test_cases
[2025-07-02 16:33:55,045] INFO in app:   - REFERENCE_IMAGES_DIR: /Users/<USER>/Documents/automation-tool/reference_images
[2025-07-02 16:33:55,045] INFO in app:   - SCREENSHOTS_DIR: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/screenshots
[2025-07-02 16:33:58,211] WARNING in appium_device_controller: No action_id provided for screenshot - this should not happen
[2025-07-02 16:33:58,212] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/temp/screenshots/placeholder.png (save_debug=False)
[2025-07-02 16:33:58,212] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-07-02 16:33:58,786] INFO in appium_device_controller: Screenshot taken with ImageMatcher and copied to /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/temp/screenshots/placeholder.png
[2025-07-02 16:33:58,788] INFO in appium_device_controller: Screenshot with action_id placeholder already exists in database, skipping save
[2025-07-02 16:33:58,788] INFO in appium_device_controller: Generated screenshot URL: /screenshots/placeholder.png
[2025-07-02 16:33:58,797] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png (save_debug=False)
[2025-07-02 16:33:58,798] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-07-02 16:33:59,446] INFO in appium_device_controller: Screenshot taken with ImageMatcher and copied to /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-07-02 16:33:59,447] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/reports/testsuite_execution_20250702_163358/screenshots/latest.png
[2025-07-02 16:33:59,447] WARNING in appium_device_controller: Not saving screenshot to database because action_id is missing
[2025-07-02 16:33:59,447] INFO in appium_device_controller: Generated screenshot URL: /screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-07-02 16:34:13,762] WARNING in appium_device_controller: Session appears to be inactive: 'WebDriver' object has no attribute 'status'
[2025-07-02 16:34:17,483] INFO in appium_device_controller: Tapping on element with accessibility_id='txtHomeAccountCtaSignIn' (timeout=10s, interval=0.5s)
[2025-07-02 16:34:17,484] INFO in appium_device_controller: Waiting for element to be clickable: accessibility_id='txtHomeAccountCtaSignIn'
[2025-07-02 16:34:18,064] INFO in appium_device_controller: Element found, tapping on it
[2025-07-02 16:34:21,541] INFO in appium_device_controller: Using current report screenshots directory: /Users/<USER>/Documents/automation-tool/reports/testsuite_execution_20250702_163358/screenshots
[2025-07-02 16:34:21,541] WARNING in appium_device_controller: No action_id provided for screenshot - this should not happen
[2025-07-02 16:34:21,542] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/reports/testsuite_execution_20250702_163358/screenshots/placeholder.png (save_debug=False)
[2025-07-02 16:34:21,542] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-07-02 16:34:22,170] INFO in appium_device_controller: Screenshot taken with ImageMatcher and copied to /Users/<USER>/Documents/automation-tool/reports/testsuite_execution_20250702_163358/screenshots/placeholder.png
[2025-07-02 16:34:22,171] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/reports/testsuite_execution_20250702_163358/screenshots/latest.png
[2025-07-02 16:34:22,172] INFO in appium_device_controller: Screenshot with action_id placeholder already exists in database, skipping save
[2025-07-02 16:34:22,172] INFO in appium_device_controller: Generated screenshot URL: /screenshots/placeholder.png
[2025-07-02 16:34:22,181] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png (save_debug=False)
[2025-07-02 16:34:22,181] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-07-02 16:34:22,790] INFO in appium_device_controller: Screenshot taken with ImageMatcher and copied to /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-07-02 16:34:22,791] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/reports/testsuite_execution_20250702_163358/screenshots/latest.png
[2025-07-02 16:34:22,791] WARNING in appium_device_controller: Not saving screenshot to database because action_id is missing
[2025-07-02 16:34:22,791] INFO in appium_device_controller: Generated screenshot URL: /screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-07-02 16:34:24,638] INFO in appium_device_controller: Using current report screenshots directory: /Users/<USER>/Documents/automation-tool/reports/testsuite_execution_20250702_163358/screenshots
[2025-07-02 16:34:24,638] WARNING in appium_device_controller: No action_id provided for screenshot - this should not happen
[2025-07-02 16:34:24,638] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/reports/testsuite_execution_20250702_163358/screenshots/placeholder.png (save_debug=False)
[2025-07-02 16:34:24,638] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-07-02 16:34:25,270] INFO in appium_device_controller: Screenshot taken with ImageMatcher and copied to /Users/<USER>/Documents/automation-tool/reports/testsuite_execution_20250702_163358/screenshots/placeholder.png
[2025-07-02 16:34:25,271] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/reports/testsuite_execution_20250702_163358/screenshots/latest.png
[2025-07-02 16:34:25,272] INFO in appium_device_controller: Screenshot with action_id placeholder already exists in database, skipping save
[2025-07-02 16:34:25,272] INFO in appium_device_controller: Generated screenshot URL: /screenshots/placeholder.png
[2025-07-02 16:34:25,281] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png (save_debug=False)
[2025-07-02 16:34:25,281] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-07-02 16:34:25,893] INFO in appium_device_controller: Screenshot taken with ImageMatcher and copied to /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-07-02 16:34:25,894] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/reports/testsuite_execution_20250702_163358/screenshots/latest.png
[2025-07-02 16:34:25,894] WARNING in appium_device_controller: Not saving screenshot to database because action_id is missing
[2025-07-02 16:34:25,894] INFO in appium_device_controller: Generated screenshot URL: /screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-07-02 16:34:27,956] INFO in appium_device_controller: Tapping on element with xpath='//android.widget.EditText[@resource-id="username"]' (timeout=10s, interval=0.5s)
[2025-07-02 16:34:27,957] INFO in appium_device_controller: Waiting for element to be clickable: xpath='//android.widget.EditText[@resource-id="username"]'
[2025-07-02 16:34:28,399] INFO in appium_device_controller: Element found, tapping on it
[2025-07-02 16:34:29,028] INFO in appium_device_controller: Inputting text '<EMAIL>' directly (no locator)
[2025-07-02 16:34:29,028] INFO in appium_device_controller: Using Airtest to input text: '<EMAIL>'
[2025-07-02 16:34:30,582] INFO in appium_device_controller: Tapping on element with xpath='//android.widget.EditText[@resource-id="password"]' (timeout=10s, interval=0.5s)
[2025-07-02 16:34:30,582] INFO in appium_device_controller: Waiting for element to be clickable: xpath='//android.widget.EditText[@resource-id="password"]'
[2025-07-02 16:34:43,760] WARNING in appium_device_controller: Session appears to be inactive: 'WebDriver' object has no attribute 'status'
[2025-07-02 16:34:52,507] INFO in appium_device_controller: Element found, tapping on it
[2025-07-02 16:34:53,136] INFO in appium_device_controller: Inputting text 'Wonderbaby@5' directly (no locator)
[2025-07-02 16:34:53,136] INFO in appium_device_controller: Using Airtest to input text: 'Wonderbaby@5'
[2025-07-02 16:34:54,864] INFO in appium_device_controller: Using current report screenshots directory: /Users/<USER>/Documents/automation-tool/reports/testsuite_execution_20250702_163358/screenshots
[2025-07-02 16:34:54,865] WARNING in appium_device_controller: No action_id provided for screenshot - this should not happen
[2025-07-02 16:34:54,865] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/reports/testsuite_execution_20250702_163358/screenshots/placeholder.png (save_debug=False)
[2025-07-02 16:34:54,865] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-07-02 16:34:55,715] INFO in appium_device_controller: Screenshot taken with ImageMatcher and copied to /Users/<USER>/Documents/automation-tool/reports/testsuite_execution_20250702_163358/screenshots/placeholder.png
[2025-07-02 16:34:55,717] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/reports/testsuite_execution_20250702_163358/screenshots/latest.png
[2025-07-02 16:34:55,718] INFO in appium_device_controller: Screenshot with action_id placeholder already exists in database, skipping save
[2025-07-02 16:34:55,718] INFO in appium_device_controller: Generated screenshot URL: /screenshots/placeholder.png
[2025-07-02 16:34:55,728] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png (save_debug=False)
[2025-07-02 16:34:55,728] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-07-02 16:34:56,864] INFO in appium_device_controller: Screenshot taken with ImageMatcher and copied to /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-07-02 16:34:56,865] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/reports/testsuite_execution_20250702_163358/screenshots/latest.png
[2025-07-02 16:34:56,866] WARNING in appium_device_controller: Not saving screenshot to database because action_id is missing
[2025-07-02 16:34:56,866] INFO in appium_device_controller: Generated screenshot URL: /screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-07-02 16:35:06,770] INFO in appium_device_controller: Using current report screenshots directory: /Users/<USER>/Documents/automation-tool/reports/testsuite_execution_20250702_163358/screenshots
[2025-07-02 16:35:06,770] WARNING in appium_device_controller: No action_id provided for screenshot - this should not happen
[2025-07-02 16:35:06,770] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/reports/testsuite_execution_20250702_163358/screenshots/placeholder.png (save_debug=False)
[2025-07-02 16:35:06,770] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-07-02 16:35:07,812] INFO in appium_device_controller: Screenshot taken with ImageMatcher and copied to /Users/<USER>/Documents/automation-tool/reports/testsuite_execution_20250702_163358/screenshots/placeholder.png
[2025-07-02 16:35:07,812] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/reports/testsuite_execution_20250702_163358/screenshots/latest.png
[2025-07-02 16:35:07,813] INFO in appium_device_controller: Screenshot with action_id placeholder already exists in database, skipping save
[2025-07-02 16:35:07,813] INFO in appium_device_controller: Generated screenshot URL: /screenshots/placeholder.png
[2025-07-02 16:35:07,823] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png (save_debug=False)
[2025-07-02 16:35:07,823] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-07-02 16:35:08,785] INFO in appium_device_controller: Using current report screenshots directory: /Users/<USER>/Documents/automation-tool/reports/testsuite_execution_20250702_163358/screenshots
[2025-07-02 16:35:08,785] WARNING in appium_device_controller: No action_id provided for screenshot - this should not happen
[2025-07-02 16:35:08,785] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/reports/testsuite_execution_20250702_163358/screenshots/placeholder.png (save_debug=False)
[2025-07-02 16:35:08,785] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-07-02 16:35:08,873] INFO in appium_device_controller: Screenshot taken with ImageMatcher and copied to /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-07-02 16:35:08,874] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/reports/testsuite_execution_20250702_163358/screenshots/latest.png
[2025-07-02 16:35:08,874] WARNING in appium_device_controller: Not saving screenshot to database because action_id is missing
[2025-07-02 16:35:08,874] INFO in appium_device_controller: Generated screenshot URL: /screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-07-02 16:35:09,779] INFO in appium_device_controller: Screenshot taken with ImageMatcher and copied to /Users/<USER>/Documents/automation-tool/reports/testsuite_execution_20250702_163358/screenshots/placeholder.png
[2025-07-02 16:35:09,780] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/reports/testsuite_execution_20250702_163358/screenshots/latest.png
[2025-07-02 16:35:09,781] INFO in appium_device_controller: Screenshot with action_id placeholder already exists in database, skipping save
[2025-07-02 16:35:09,781] INFO in appium_device_controller: Generated screenshot URL: /screenshots/placeholder.png
[2025-07-02 16:35:12,065] INFO in appium_device_controller: Using current report screenshots directory: /Users/<USER>/Documents/automation-tool/reports/testsuite_execution_20250702_163358/screenshots
[2025-07-02 16:35:12,065] WARNING in appium_device_controller: No action_id provided for screenshot - this should not happen
[2025-07-02 16:35:12,066] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/reports/testsuite_execution_20250702_163358/screenshots/placeholder.png (save_debug=False)
[2025-07-02 16:35:12,066] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-07-02 16:35:13,770] WARNING in appium_device_controller: Session appears to be inactive: 'WebDriver' object has no attribute 'status'
[2025-07-02 16:35:13,905] INFO in appium_device_controller: Screenshot taken with ImageMatcher and copied to /Users/<USER>/Documents/automation-tool/reports/testsuite_execution_20250702_163358/screenshots/placeholder.png
[2025-07-02 16:35:13,906] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/reports/testsuite_execution_20250702_163358/screenshots/latest.png
[2025-07-02 16:35:13,906] INFO in appium_device_controller: Screenshot with action_id placeholder already exists in database, skipping save
[2025-07-02 16:35:13,906] INFO in appium_device_controller: Generated screenshot URL: /screenshots/placeholder.png
[2025-07-02 16:35:13,916] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png (save_debug=False)
[2025-07-02 16:35:13,916] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-07-02 16:35:14,753] INFO in appium_device_controller: Screenshot taken with ImageMatcher and copied to /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-07-02 16:35:14,754] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/reports/testsuite_execution_20250702_163358/screenshots/latest.png
[2025-07-02 16:35:14,754] WARNING in appium_device_controller: Not saving screenshot to database because action_id is missing
[2025-07-02 16:35:14,754] INFO in appium_device_controller: Generated screenshot URL: /screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-07-02 16:35:14,916] INFO in appium_device_controller: Using current report screenshots directory: /Users/<USER>/Documents/automation-tool/reports/testsuite_execution_20250702_163358/screenshots
[2025-07-02 16:35:14,916] WARNING in appium_device_controller: No action_id provided for screenshot - this should not happen
[2025-07-02 16:35:14,916] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/reports/testsuite_execution_20250702_163358/screenshots/placeholder.png (save_debug=False)
[2025-07-02 16:35:14,917] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-07-02 16:35:15,711] INFO in appium_device_controller: Screenshot taken with ImageMatcher and copied to /Users/<USER>/Documents/automation-tool/reports/testsuite_execution_20250702_163358/screenshots/placeholder.png
[2025-07-02 16:35:15,712] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/reports/testsuite_execution_20250702_163358/screenshots/latest.png
[2025-07-02 16:35:15,712] INFO in appium_device_controller: Screenshot with action_id placeholder already exists in database, skipping save
[2025-07-02 16:35:15,712] INFO in appium_device_controller: Generated screenshot URL: /screenshots/placeholder.png
[2025-07-02 16:35:43,761] WARNING in appium_device_controller: Session appears to be inactive: 'WebDriver' object has no attribute 'status'
[2025-07-02 16:35:54,142] INFO in appium_device_controller: Using current report screenshots directory: /Users/<USER>/Documents/automation-tool/reports/testsuite_execution_20250702_163358/screenshots
[2025-07-02 16:35:54,142] WARNING in appium_device_controller: No action_id provided for screenshot - this should not happen
[2025-07-02 16:35:54,143] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/reports/testsuite_execution_20250702_163358/screenshots/placeholder.png (save_debug=False)
[2025-07-02 16:35:54,143] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-07-02 16:35:55,275] INFO in appium_device_controller: Screenshot taken with ImageMatcher and copied to /Users/<USER>/Documents/automation-tool/reports/testsuite_execution_20250702_163358/screenshots/placeholder.png
[2025-07-02 16:35:55,275] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/reports/testsuite_execution_20250702_163358/screenshots/latest.png
[2025-07-02 16:35:55,277] INFO in appium_device_controller: Screenshot with action_id placeholder already exists in database, skipping save
[2025-07-02 16:35:55,277] INFO in appium_device_controller: Generated screenshot URL: /screenshots/placeholder.png
[2025-07-02 16:35:55,288] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png (save_debug=False)
[2025-07-02 16:35:55,288] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-07-02 16:35:56,052] INFO in appium_device_controller: Screenshot taken with ImageMatcher and copied to /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-07-02 16:35:56,053] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/reports/testsuite_execution_20250702_163358/screenshots/latest.png
[2025-07-02 16:35:56,053] WARNING in appium_device_controller: Not saving screenshot to database because action_id is missing
[2025-07-02 16:35:56,053] INFO in appium_device_controller: Generated screenshot URL: /screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-07-02 16:36:13,761] WARNING in appium_device_controller: Session appears to be inactive: 'WebDriver' object has no attribute 'status'
=== LATEST REPORT ENDPOINT CALLED ===
=== REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
=== SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===
=== ABOUT TO CALL getLatestReportUrl() ===
=== getLatestReportUrl() RETURNED: /reports/testsuite_execution_20250701_105342/mainreport.html ===
=== LATEST REPORT ENDPOINT CALLED ===
=== REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
=== SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===
=== ABOUT TO CALL getLatestReportUrl() ===
=== getLatestReportUrl() RETURNED: /reports/testsuite_execution_20250701_105342/mainreport.html ===
=== LATEST REPORT ENDPOINT CALLED ===
=== REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
=== SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===
=== ABOUT TO CALL getLatestReportUrl() ===
=== getLatestReportUrl() RETURNED: /reports/testsuite_execution_20250701_105342/mainreport.html ===
=== LATEST REPORT ENDPOINT CALLED ===
=== REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
=== SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===
=== ABOUT TO CALL getLatestReportUrl() ===
=== getLatestReportUrl() RETURNED: /reports/testsuite_execution_20250701_105342/mainreport.html ===
=== LATEST REPORT ENDPOINT CALLED ===
=== REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
=== SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===
=== ABOUT TO CALL getLatestReportUrl() ===
=== getLatestReportUrl() RETURNED: /reports/testsuite_execution_20250701_105342/mainreport.html ===
=== LATEST REPORT ENDPOINT CALLED ===
=== REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
=== SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===
=== ABOUT TO CALL getLatestReportUrl() ===
=== getLatestReportUrl() RETURNED: /reports/testsuite_execution_20250701_105342/mainreport.html ===
=== LATEST REPORT ENDPOINT CALLED ===
=== REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
=== SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===
=== ABOUT TO CALL getLatestReportUrl() ===
=== getLatestReportUrl() RETURNED: /reports/testsuite_execution_20250701_105342/mainreport.html ===
=== LATEST REPORT ENDPOINT CALLED ===
=== REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
=== SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===
=== ABOUT TO CALL getLatestReportUrl() ===
=== getLatestReportUrl() RETURNED: /reports/testsuite_execution_20250701_105342/mainreport.html ===
=== LATEST REPORT ENDPOINT CALLED ===
=== REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
=== SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===
=== ABOUT TO CALL getLatestReportUrl() ===
=== getLatestReportUrl() RETURNED: /reports/testsuite_execution_20250701_105342/mainreport.html ===
=== LATEST REPORT ENDPOINT CALLED ===
=== REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
=== SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===
=== ABOUT TO CALL getLatestReportUrl() ===
=== getLatestReportUrl() RETURNED: /reports/testsuite_execution_20250701_105342/mainreport.html ===
=== LATEST REPORT ENDPOINT CALLED ===
=== REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
=== SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===
=== ABOUT TO CALL getLatestReportUrl() ===
=== getLatestReportUrl() RETURNED: /reports/testsuite_execution_20250701_105342/mainreport.html ===
=== LATEST REPORT ENDPOINT CALLED ===
=== REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
=== SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===
=== ABOUT TO CALL getLatestReportUrl() ===
=== getLatestReportUrl() RETURNED: /reports/testsuite_execution_20250701_105342/mainreport.html ===
=== LATEST REPORT ENDPOINT CALLED ===
=== REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
=== SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===
=== ABOUT TO CALL getLatestReportUrl() ===
=== getLatestReportUrl() RETURNED: /reports/testsuite_execution_20250701_105342/mainreport.html ===
=== LATEST REPORT ENDPOINT CALLED ===
=== REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
=== SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===
=== ABOUT TO CALL getLatestReportUrl() ===
=== getLatestReportUrl() RETURNED: /reports/testsuite_execution_20250701_105342/mainreport.html ===
=== LATEST REPORT ENDPOINT CALLED ===
=== REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
=== SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===
=== ABOUT TO CALL getLatestReportUrl() ===
=== getLatestReportUrl() RETURNED: /reports/testsuite_execution_20250701_105342/mainreport.html ===
=== LATEST REPORT ENDPOINT CALLED ===
=== REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
=== SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===
=== ABOUT TO CALL getLatestReportUrl() ===
=== getLatestReportUrl() RETURNED: /reports/testsuite_execution_20250701_105342/mainreport.html ===
=== LATEST REPORT ENDPOINT CALLED ===
=== REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
=== SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===
=== ABOUT TO CALL getLatestReportUrl() ===
=== getLatestReportUrl() RETURNED: /reports/testsuite_execution_20250701_105342/mainreport.html ===
=== LATEST REPORT ENDPOINT CALLED ===
=== REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
=== SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===
=== ABOUT TO CALL getLatestReportUrl() ===
=== getLatestReportUrl() RETURNED: /reports/testsuite_execution_20250701_105342/mainreport.html ===
=== LATEST REPORT ENDPOINT CALLED ===
=== REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
=== SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===
=== ABOUT TO CALL getLatestReportUrl() ===
=== getLatestReportUrl() RETURNED: /reports/testsuite_execution_20250701_105342/mainreport.html ===
=== LATEST REPORT ENDPOINT CALLED ===
=== REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
=== SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===
=== ABOUT TO CALL getLatestReportUrl() ===
=== getLatestReportUrl() RETURNED: /reports/testsuite_execution_20250701_105342/mainreport.html ===
=== LATEST REPORT ENDPOINT CALLED ===
=== REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
=== SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===
=== ABOUT TO CALL getLatestReportUrl() ===
=== getLatestReportUrl() RETURNED: /reports/testsuite_execution_20250701_105342/mainreport.html ===
=== LATEST REPORT ENDPOINT CALLED ===
=== REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
=== SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===
=== ABOUT TO CALL getLatestReportUrl() ===
=== getLatestReportUrl() RETURNED: /reports/testsuite_execution_20250701_105342/mainreport.html ===
=== LATEST REPORT ENDPOINT CALLED ===
=== REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
=== SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===
=== ABOUT TO CALL getLatestReportUrl() ===
=== getLatestReportUrl() RETURNED: /reports/testsuite_execution_20250701_105342/mainreport.html ===
=== LATEST REPORT ENDPOINT CALLED ===
=== REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
=== SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===
=== ABOUT TO CALL getLatestReportUrl() ===
=== getLatestReportUrl() RETURNED: /reports/testsuite_execution_20250701_105342/mainreport.html ===
=== LATEST REPORT ENDPOINT CALLED ===
=== REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
=== SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===
=== ABOUT TO CALL getLatestReportUrl() ===
=== getLatestReportUrl() RETURNED: /reports/testsuite_execution_20250701_105342/mainreport.html ===
=== LATEST REPORT ENDPOINT CALLED ===
=== REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
=== SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===[2025-07-02 16:36:43,759] WARNING in appium_device_controller: Session appears to be inactive: 'WebDriver' object has no attribute 'status'
[2025-07-02 16:36:51,387] INFO in appium_device_controller: Using current report screenshots directory: /Users/<USER>/Documents/automation-tool/reports/testsuite_execution_20250702_163358/screenshots
[2025-07-02 16:36:51,387] WARNING in appium_device_controller: No action_id provided for screenshot - this should not happen
[2025-07-02 16:36:51,388] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/reports/testsuite_execution_20250702_163358/screenshots/placeholder.png (save_debug=False)
[2025-07-02 16:36:51,388] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-07-02 16:36:52,582] INFO in appium_device_controller: Screenshot taken with ImageMatcher and copied to /Users/<USER>/Documents/automation-tool/reports/testsuite_execution_20250702_163358/screenshots/placeholder.png
[2025-07-02 16:36:52,583] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/reports/testsuite_execution_20250702_163358/screenshots/latest.png
[2025-07-02 16:36:52,584] INFO in appium_device_controller: Screenshot with action_id placeholder already exists in database, skipping save
[2025-07-02 16:36:52,584] INFO in appium_device_controller: Generated screenshot URL: /screenshots/placeholder.png
[2025-07-02 16:37:13,761] WARNING in appium_device_controller: Session appears to be inactive: 'WebDriver' object has no attribute 'status'
[2025-07-02 16:37:43,760] WARNING in appium_device_controller: Session appears to be inactive: 'WebDriver' object has no attribute 'status'
[2025-07-02 16:37:53,589] INFO in appium_device_controller: Using current report screenshots directory: /Users/<USER>/Documents/automation-tool/reports/testsuite_execution_20250702_163358/screenshots
[2025-07-02 16:37:53,601] WARNING in appium_device_controller: No action_id provided for screenshot - this should not happen
[2025-07-02 16:37:53,602] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/reports/testsuite_execution_20250702_163358/screenshots/placeholder.png (save_debug=False)
[2025-07-02 16:37:53,602] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-07-02 16:37:55,137] INFO in appium_device_controller: Screenshot taken with ImageMatcher and copied to /Users/<USER>/Documents/automation-tool/reports/testsuite_execution_20250702_163358/screenshots/placeholder.png
[2025-07-02 16:37:55,138] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/reports/testsuite_execution_20250702_163358/screenshots/latest.png
[2025-07-02 16:37:55,139] INFO in appium_device_controller: Screenshot with action_id placeholder already exists in database, skipping save
[2025-07-02 16:37:55,139] INFO in appium_device_controller: Generated screenshot URL: /screenshots/placeholder.png
[2025-07-02 16:37:55,148] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png (save_debug=False)
[2025-07-02 16:37:55,148] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-07-02 16:37:56,091] INFO in appium_device_controller: Screenshot taken with ImageMatcher and copied to /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-07-02 16:37:56,092] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/reports/testsuite_execution_20250702_163358/screenshots/latest.png
[2025-07-02 16:37:56,092] WARNING in appium_device_controller: Not saving screenshot to database because action_id is missing
[2025-07-02 16:37:56,092] INFO in appium_device_controller: Generated screenshot URL: /screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-07-02 16:38:13,761] WARNING in appium_device_controller: Session appears to be inactive: 'WebDriver' object has no attribute 'status'
[2025-07-02 16:38:43,759] WARNING in appium_device_controller: Session appears to be inactive: 'WebDriver' object has no attribute 'status'
[2025-07-02 16:38:45,390] INFO in appium_device_controller: Using current report screenshots directory: /Users/<USER>/Documents/automation-tool/reports/testsuite_execution_20250702_163358/screenshots
[2025-07-02 16:38:45,390] WARNING in appium_device_controller: No action_id provided for screenshot - this should not happen
[2025-07-02 16:38:45,390] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/reports/testsuite_execution_20250702_163358/screenshots/placeholder.png (save_debug=False)
[2025-07-02 16:38:45,390] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-07-02 16:38:46,227] INFO in appium_device_controller: Screenshot taken with ImageMatcher and copied to /Users/<USER>/Documents/automation-tool/reports/testsuite_execution_20250702_163358/screenshots/placeholder.png
[2025-07-02 16:38:46,228] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/reports/testsuite_execution_20250702_163358/screenshots/latest.png
[2025-07-02 16:38:46,229] INFO in appium_device_controller: Screenshot with action_id placeholder already exists in database, skipping save
[2025-07-02 16:38:46,229] INFO in appium_device_controller: Generated screenshot URL: /screenshots/placeholder.png

=== ABOUT TO CALL getLatestReportUrl() ===
=== getLatestReportUrl() RETURNED: /reports/testsuite_execution_20250701_105342/mainreport.html ===
=== LATEST REPORT ENDPOINT CALLED ===
=== REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
=== SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===
=== ABOUT TO CALL getLatestReportUrl() ===
=== getLatestReportUrl() RETURNED: /reports/testsuite_execution_20250701_105342/mainreport.html ===
=== LATEST REPORT ENDPOINT CALLED ===
=== REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
=== SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===
=== ABOUT TO CALL getLatestReportUrl() ===
=== getLatestReportUrl() RETURNED: /reports/testsuite_execution_20250701_105342/mainreport.html ===
=== LATEST REPORT ENDPOINT CALLED ===
=== REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
=== SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===
=== ABOUT TO CALL getLatestReportUrl() ===
=== getLatestReportUrl() RETURNED: /reports/testsuite_execution_20250701_105342/mainreport.html ===
=== LATEST REPORT ENDPOINT CALLED ===
=== REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
=== SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===
=== ABOUT TO CALL getLatestReportUrl() ===
=== getLatestReportUrl() RETURNED: /reports/testsuite_execution_20250701_105342/mainreport.html ===
=== LATEST REPORT ENDPOINT CALLED ===
=== REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
=== SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===
=== ABOUT TO CALL getLatestReportUrl() ===
=== getLatestReportUrl() RETURNED: /reports/testsuite_execution_20250701_105342/mainreport.html ===
=== LATEST REPORT ENDPOINT CALLED ===
=== REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
=== SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===
=== ABOUT TO CALL getLatestReportUrl() ===
=== getLatestReportUrl() RETURNED: /reports/testsuite_execution_20250701_105342/mainreport.html ===
=== LATEST REPORT ENDPOINT CALLED ===
=== REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
=== SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===
=== ABOUT TO CALL getLatestReportUrl() ===
=== getLatestReportUrl() RETURNED: /reports/testsuite_execution_20250701_105342/mainreport.html ===
=== LATEST REPORT ENDPOINT CALLED ===
=== REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
=== SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===
=== ABOUT TO CALL getLatestReportUrl() ===
=== getLatestReportUrl() RETURNED: /reports/testsuite_execution_20250701_105342/mainreport.html ===
=== LATEST REPORT ENDPOINT CALLED ===
=== REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
=== SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===
=== ABOUT TO CALL getLatestReportUrl() ===
=== getLatestReportUrl() RETURNED: /reports/testsuite_execution_20250701_105342/mainreport.html ===
=== LATEST REPORT ENDPOINT CALLED ===
=== REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
=== SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===
=== ABOUT TO CALL getLatestReportUrl() ===
=== getLatestReportUrl() RETURNED: /reports/testsuite_execution_20250701_105342/mainreport.html ===
=== LATEST REPORT ENDPOINT CALLED ===
=== REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
=== SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===
=== ABOUT TO CALL getLatestReportUrl() ===
=== getLatestReportUrl() RETURNED: /reports/testsuite_execution_20250701_105342/mainreport.html ===
=== LATEST REPORT ENDPOINT CALLED ===
=== REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
=== SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===
=== ABOUT TO CALL getLatestReportUrl() ===
=== getLatestReportUrl() RETURNED: /reports/testsuite_execution_20250701_105342/mainreport.html ===
=== LATEST REPORT ENDPOINT CALLED ===
=== REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
=== SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===
=== ABOUT TO CALL getLatestReportUrl() ===
=== getLatestReportUrl() RETURNED: /reports/testsuite_execution_20250701_105342/mainreport.html ===
=== LATEST REPORT ENDPOINT CALLED ===
=== REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
=== SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===
=== ABOUT TO CALL getLatestReportUrl() ===
=== getLatestReportUrl() RETURNED: /reports/testsuite_execution_20250701_105342/mainreport.html ===
=== LATEST REPORT ENDPOINT CALLED ===
=== REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
=== SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===
=== ABOUT TO CALL getLatestReportUrl() ===
=== getLatestReportUrl() RETURNED: /reports/testsuite_execution_20250701_105342/mainreport.html ===
=== LATEST REPORT ENDPOINT CALLED ===
=== REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
=== SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===
=== ABOUT TO CALL getLatestReportUrl() ===
=== getLatestReportUrl() RETURNED: /reports/testsuite_execution_20250701_105342/mainreport.html ===
=== LATEST REPORT ENDPOINT CALLED ===
=== REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
=== SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===
=== ABOUT TO CALL getLatestReportUrl() ===
=== getLatestReportUrl() RETURNED: /reports/testsuite_execution_20250701_105342/mainreport.html ===
=== LATEST REPORT ENDPOINT CALLED ===
=== REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
=== SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===
=== ABOUT TO CALL getLatestReportUrl() ===
=== getLatestReportUrl() RETURNED: /reports/testsuite_execution_20250701_105342/mainreport.html ===
=== LATEST REPORT ENDPOINT CALLED ===
=== REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
=== SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===
=== ABOUT TO CALL getLatestReportUrl() ===
=== getLatestReportUrl() RETURNED: /reports/testsuite_execution_20250701_105342/mainreport.html ===
=== LATEST REPORT ENDPOINT CALLED ===
=== REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
=== SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===
=== ABOUT TO CALL getLatestReportUrl() ===
=== getLatestReportUrl() RETURNED: /reports/testsuite_execution_20250701_105342/mainreport.html ===
=== LATEST REPORT ENDPOINT CALLED ===
=== REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
=== SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===
=== ABOUT TO CALL getLatestReportUrl() ===
=== getLatestReportUrl() RETURNED: /reports/testsuite_execution_20250701_105342/mainreport.html ===
=== LATEST REPORT ENDPOINT CALLED ===
=== REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
=== SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===
=== ABOUT TO CALL getLatestReportUrl() ===
=== getLatestReportUrl() RETURNED: /reports/testsuite_execution_20250701_105342/mainreport.html ===
=== LATEST REPORT ENDPOINT CALLED ===
=== REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
=== SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===
=== ABOUT TO CALL getLatestReportUrl() ===
=== getLatestReportUrl() RETURNED: /reports/testsuite_execution_20250701_105342/mainreport.html ===
=== LATEST REPORT ENDPOINT CALLED ===
=== REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
=== SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===
=== ABOUT TO CALL getLatestReportUrl() ===
=== getLatestReportUrl() RETURNED: /reports/testsuite_execution_20250701_105342/mainreport.html ===
=== LATEST REPORT ENDPOINT CALLED ===
=== REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
=== SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===
=== ABOUT TO CALL getLatestReportUrl() ===
=== getLatestReportUrl() RETURNED: /reports/testsuite_execution_20250701_105342/mainreport.html ===[2025-07-02 16:39:13,759] WARNING in appium_device_controller: Session appears to be inactive: 'WebDriver' object has no attribute 'status'
[2025-07-02 16:39:15,785] INFO in appium_device_controller: Inputting text '3000' directly (no locator)
[2025-07-02 16:39:15,785] INFO in appium_device_controller: Using Airtest to input text: '3000'
[2025-07-02 16:39:17,033] INFO in appium_device_controller: Using current report screenshots directory: /Users/<USER>/Documents/automation-tool/reports/testsuite_execution_20250702_163358/screenshots
[2025-07-02 16:39:17,033] WARNING in appium_device_controller: No action_id provided for screenshot - this should not happen
[2025-07-02 16:39:17,033] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/reports/testsuite_execution_20250702_163358/screenshots/placeholder.png (save_debug=False)
[2025-07-02 16:39:17,034] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-07-02 16:39:17,887] INFO in appium_device_controller: Screenshot taken with ImageMatcher and copied to /Users/<USER>/Documents/automation-tool/reports/testsuite_execution_20250702_163358/screenshots/placeholder.png
[2025-07-02 16:39:17,888] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/reports/testsuite_execution_20250702_163358/screenshots/latest.png
[2025-07-02 16:39:17,889] INFO in appium_device_controller: Screenshot with action_id placeholder already exists in database, skipping save
[2025-07-02 16:39:17,889] INFO in appium_device_controller: Generated screenshot URL: /screenshots/placeholder.png
[2025-07-02 16:39:17,898] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png (save_debug=False)
[2025-07-02 16:39:17,899] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-07-02 16:39:18,668] INFO in appium_device_controller: Screenshot taken with ImageMatcher and copied to /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-07-02 16:39:18,669] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/reports/testsuite_execution_20250702_163358/screenshots/latest.png
[2025-07-02 16:39:18,669] WARNING in appium_device_controller: Not saving screenshot to database because action_id is missing
[2025-07-02 16:39:18,669] INFO in appium_device_controller: Generated screenshot URL: /screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-07-02 16:39:24,771] INFO in appium_device_controller: Inputting text '3000' directly (no locator)
[2025-07-02 16:39:24,771] INFO in appium_device_controller: Using Airtest to input text: '3000'
[2025-07-02 16:39:26,034] INFO in appium_device_controller: Using current report screenshots directory: /Users/<USER>/Documents/automation-tool/reports/testsuite_execution_20250702_163358/screenshots
[2025-07-02 16:39:26,034] WARNING in appium_device_controller: No action_id provided for screenshot - this should not happen
[2025-07-02 16:39:26,035] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/reports/testsuite_execution_20250702_163358/screenshots/placeholder.png (save_debug=False)
[2025-07-02 16:39:26,035] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-07-02 16:39:26,823] INFO in appium_device_controller: Screenshot taken with ImageMatcher and copied to /Users/<USER>/Documents/automation-tool/reports/testsuite_execution_20250702_163358/screenshots/placeholder.png
[2025-07-02 16:39:26,824] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/reports/testsuite_execution_20250702_163358/screenshots/latest.png
[2025-07-02 16:39:26,824] INFO in appium_device_controller: Screenshot with action_id placeholder already exists in database, skipping save
[2025-07-02 16:39:26,824] INFO in appium_device_controller: Generated screenshot URL: /screenshots/placeholder.png
[2025-07-02 16:39:26,834] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png (save_debug=False)
[2025-07-02 16:39:26,834] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-07-02 16:39:27,608] INFO in appium_device_controller: Screenshot taken with ImageMatcher and copied to /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-07-02 16:39:27,609] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/reports/testsuite_execution_20250702_163358/screenshots/latest.png
[2025-07-02 16:39:27,609] WARNING in appium_device_controller: Not saving screenshot to database because action_id is missing
[2025-07-02 16:39:27,609] INFO in appium_device_controller: Generated screenshot URL: /screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-07-02 16:39:43,760] WARNING in appium_device_controller: Session appears to be inactive: 'WebDriver' object has no attribute 'status'
[2025-07-02 16:40:13,759] WARNING in appium_device_controller: Session appears to be inactive: 'WebDriver' object has no attribute 'status'
[2025-07-02 16:40:43,757] WARNING in appium_device_controller: Session appears to be inactive: 'WebDriver' object has no attribute 'status'

=== LATEST REPORT ENDPOINT CALLED ===
=== REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
=== SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===
=== ABOUT TO CALL getLatestReportUrl() ===
=== getLatestReportUrl() RETURNED: /reports/testsuite_execution_20250701_105342/mainreport.html ===
=== LATEST REPORT ENDPOINT CALLED ===
=== REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
=== SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===
=== ABOUT TO CALL getLatestReportUrl() ===
=== getLatestReportUrl() RETURNED: /reports/testsuite_execution_20250701_105342/mainreport.html ===
=== LATEST REPORT ENDPOINT CALLED ===
=== REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
=== SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===
=== ABOUT TO CALL getLatestReportUrl() ===
=== getLatestReportUrl() RETURNED: /reports/testsuite_execution_20250701_105342/mainreport.html ===
=== LATEST REPORT ENDPOINT CALLED ===
=== REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
=== SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===
=== ABOUT TO CALL getLatestReportUrl() ===
=== getLatestReportUrl() RETURNED: /reports/testsuite_execution_20250701_105342/mainreport.html ===
=== LATEST REPORT ENDPOINT CALLED ===
=== REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
=== SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===
=== ABOUT TO CALL getLatestReportUrl() ===
=== getLatestReportUrl() RETURNED: /reports/testsuite_execution_20250701_105342/mainreport.html ===
=== LATEST REPORT ENDPOINT CALLED ===
=== REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
=== SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===
=== ABOUT TO CALL getLatestReportUrl() ===
=== getLatestReportUrl() RETURNED: /reports/testsuite_execution_20250701_105342/mainreport.html ===
=== LATEST REPORT ENDPOINT CALLED ===
=== REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
=== SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===
=== ABOUT TO CALL getLatestReportUrl() ===
=== getLatestReportUrl() RETURNED: /reports/testsuite_execution_20250701_105342/mainreport.html ===
=== LATEST REPORT ENDPOINT CALLED ===
=== REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
=== SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===
=== ABOUT TO CALL getLatestReportUrl() ===
=== getLatestReportUrl() RETURNED: /reports/testsuite_execution_20250701_105342/mainreport.html ===
=== LATEST REPORT ENDPOINT CALLED ===
=== REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
=== SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===
=== ABOUT TO CALL getLatestReportUrl() ===
=== getLatestReportUrl() RETURNED: /reports/testsuite_execution_20250701_105342/mainreport.html ===
=== LATEST REPORT ENDPOINT CALLED ===
=== REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
=== SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===
=== ABOUT TO CALL getLatestReportUrl() ===
=== getLatestReportUrl() RETURNED: /reports/testsuite_execution_20250701_105342/mainreport.html ===
=== LATEST REPORT ENDPOINT CALLED ===
=== REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
=== SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===
=== ABOUT TO CALL getLatestReportUrl() ===
=== getLatestReportUrl() RETURNED: /reports/testsuite_execution_20250701_105342/mainreport.html ===
=== LATEST REPORT ENDPOINT CALLED ===
=== REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
=== SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===
=== ABOUT TO CALL getLatestReportUrl() ===
=== getLatestReportUrl() RETURNED: /reports/testsuite_execution_20250701_105342/mainreport.html ===
=== LATEST REPORT ENDPOINT CALLED ===
=== REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
=== SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===
=== ABOUT TO CALL getLatestReportUrl() ===
=== getLatestReportUrl() RETURNED: /reports/testsuite_execution_20250701_105342/mainreport.html ===
=== LATEST REPORT ENDPOINT CALLED ===
=== REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
=== SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===
=== ABOUT TO CALL getLatestReportUrl() ===
=== getLatestReportUrl() RETURNED: /reports/testsuite_execution_20250701_105342/mainreport.html ===
=== LATEST REPORT ENDPOINT CALLED ===
=== REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
=== SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===
=== ABOUT TO CALL getLatestReportUrl() ===
=== getLatestReportUrl() RETURNED: /reports/testsuite_execution_20250701_105342/mainreport.html ===
=== LATEST REPORT ENDPOINT CALLED ===
=== REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
=== SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===
=== ABOUT TO CALL getLatestReportUrl() ===
=== getLatestReportUrl() RETURNED: /reports/testsuite_execution_20250701_105342/mainreport.html ===
=== LATEST REPORT ENDPOINT CALLED ===
=== REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
=== SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===
=== ABOUT TO CALL getLatestReportUrl() ===
=== getLatestReportUrl() RETURNED: /reports/testsuite_execution_20250701_105342/mainreport.html ===
=== LATEST REPORT ENDPOINT CALLED ===
=== REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
=== SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===
=== ABOUT TO CALL getLatestReportUrl() ===
=== getLatestReportUrl() RETURNED: /reports/testsuite_execution_20250701_105342/mainreport.html ===
=== LATEST REPORT ENDPOINT CALLED ===
=== REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
=== SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===
=== ABOUT TO CALL getLatestReportUrl() ===
=== getLatestReportUrl() RETURNED: /reports/testsuite_execution_20250701_105342/mainreport.html ===
=== LATEST REPORT ENDPOINT CALLED ===
=== REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
=== SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===
=== ABOUT TO CALL getLatestReportUrl() ===
=== getLatestReportUrl() RETURNED: /reports/testsuite_execution_20250701_105342/mainreport.html ===
=== LATEST REPORT ENDPOINT CALLED ===
=== REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
=== SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===
=== ABOUT TO CALL getLatestReportUrl() ===
=== getLatestReportUrl() RETURNED: /reports/testsuite_execution_20250701_105342/mainreport.html ===
=== LATEST REPORT ENDPOINT CALLED ===
=== REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
=== SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===
=== ABOUT TO CALL getLatestReportUrl() ===
=== getLatestReportUrl() RETURNED: /reports/testsuite_execution_20250701_105342/mainreport.html ===
=== LATEST REPORT ENDPOINT CALLED ===
=== REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
=== SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===
=== ABOUT TO CALL getLatestReportUrl() ===
=== getLatestReportUrl() RETURNED: /reports/testsuite_execution_20250701_105342/mainreport.html ===
=== LATEST REPORT ENDPOINT CALLED ===
=== REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
=== SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===
=== ABOUT TO CALL getLatestReportUrl() ===
=== getLatestReportUrl() RETURNED: /reports/testsuite_execution_20250701_105342/mainreport.html ===
=== LATEST REPORT ENDPOINT CALLED ===
=== REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
=== SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===
=== ABOUT TO CALL getLatestReportUrl() ===
=== getLatestReportUrl() RETURNED: /reports/testsuite_execution_20250701_105342/mainreport.html ===
=== LATEST REPORT ENDPOINT CALLED ===
=== REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
=== SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===[2025-07-02 16:41:13,757] WARNING in appium_device_controller: Session appears to be inactive: 'WebDriver' object has no attribute 'status'
[2025-07-02 16:41:43,757] WARNING in appium_device_controller: Session appears to be inactive: 'WebDriver' object has no attribute 'status'

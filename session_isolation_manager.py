#!/usr/bin/env python3
"""
Session Isolation Manager for Mobile App Automation Tool

This module provides complete session isolation for multi-device testing,
ensuring each connected device operates independently with separate:
- Database connections/files
- Environment variables
- Configuration settings
- Appium session management
- Port allocations
- Screenshot handling
"""

import os
import sys
import json
import uuid
import logging
import sqlite3
import subprocess
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Optional, Tuple

logger = logging.getLogger(__name__)

class SessionIsolationManager:
    """
    Manages complete session isolation for multi-device testing
    """
    
    def __init__(self, base_dir: str = None):
        """
        Initialize the Session Isolation Manager
        
        Args:
            base_dir: Base directory for the automation tool (defaults to current directory)
        """
        self.base_dir = Path(base_dir) if base_dir else Path(__file__).parent
        self.sessions_dir = self.base_dir / "sessions"
        self.sessions_dir.mkdir(exist_ok=True)
        
        # Session registry file
        self.registry_file = self.sessions_dir / "session_registry.json"
        
        # Load existing sessions
        self.sessions = self._load_session_registry()
        
        logger.info(f"Session Isolation Manager initialized with base dir: {self.base_dir}")
    
    def _load_session_registry(self) -> Dict:
        """Load the session registry from disk"""
        if self.registry_file.exists():
            try:
                with open(self.registry_file, 'r') as f:
                    return json.load(f)
            except Exception as e:
                logger.warning(f"Failed to load session registry: {e}")
        return {}
    
    def _save_session_registry(self):
        """Save the session registry to disk"""
        try:
            with open(self.registry_file, 'w') as f:
                json.dump(self.sessions, f, indent=2)
        except Exception as e:
            logger.error(f"Failed to save session registry: {e}")
    
    def create_session(self, device_id: str, platform: str = "iOS") -> str:
        """
        Create a new isolated session for a device
        
        Args:
            device_id: Unique device identifier
            platform: Device platform (iOS/Android)
            
        Returns:
            session_id: Unique session identifier
        """
        # Generate unique session ID
        session_id = f"session_{int(datetime.now().timestamp() * 1000)}_{uuid.uuid4().hex[:8]}"
        
        # Create session directory
        session_dir = self.sessions_dir / session_id
        session_dir.mkdir(exist_ok=True)
        
        # Create isolated data directories
        data_dir = session_dir / "data"
        data_dir.mkdir(exist_ok=True)
        
        screenshots_dir = session_dir / "screenshots"
        screenshots_dir.mkdir(exist_ok=True)
        
        reports_dir = session_dir / "reports"
        reports_dir.mkdir(exist_ok=True)
        
        temp_dir = session_dir / "temp"
        temp_dir.mkdir(exist_ok=True)
        
        # Allocate ports
        flask_port, appium_port, wda_port = self._allocate_ports()
        
        # Create session configuration
        session_config = {
            "session_id": session_id,
            "device_id": device_id,
            "platform": platform,
            "created_at": datetime.now().isoformat(),
            "status": "created",
            "ports": {
                "flask": flask_port,
                "appium": appium_port,
                "wda": wda_port
            },
            "directories": {
                "session": str(session_dir),
                "data": str(data_dir),
                "screenshots": str(screenshots_dir),
                "reports": str(reports_dir),
                "temp": str(temp_dir)
            },
            "databases": {
                "test_execution": str(data_dir / "test_execution.db"),
                "global_values": str(data_dir / "global_values.db"),
                "settings": str(data_dir / "settings.db")
            },
            "environment": {
                "INSTANCE_DB_SUFFIX": f"_session_{session_id}",
                "SELECTED_DEVICE_ID": device_id,
                "SELECTED_PLATFORM": platform,
                "SESSION_ID": session_id,
                "SESSION_DATA_DIR": str(data_dir),
                "SESSION_SCREENSHOTS_DIR": str(screenshots_dir),
                "SESSION_REPORTS_DIR": str(reports_dir),
                "SESSION_TEMP_DIR": str(temp_dir)
            }
        }
        
        # Save session configuration
        session_config_file = session_dir / "session_config.json"
        with open(session_config_file, 'w') as f:
            json.dump(session_config, f, indent=2)
        
        # Initialize session databases
        self._initialize_session_databases(session_config)
        
        # Register session
        self.sessions[session_id] = session_config
        self._save_session_registry()
        
        logger.info(f"Created isolated session {session_id} for device {device_id} on ports Flask:{flask_port}, Appium:{appium_port}, WDA:{wda_port}")
        
        return session_id
    
    def _allocate_ports(self) -> Tuple[int, int, int]:
        """
        Allocate available ports for Flask, Appium, and WDA
        
        Returns:
            Tuple of (flask_port, appium_port, wda_port)
        """
        # Get currently used ports
        used_ports = set()
        for session in self.sessions.values():
            if "ports" in session:
                used_ports.update(session["ports"].values())
        
        # Find available ports
        base_flask = 8080
        base_appium = 4723
        base_wda = 8100
        
        flask_port = self._find_available_port(base_flask, used_ports)
        appium_port = self._find_available_port(base_appium, used_ports)
        wda_port = self._find_available_port(base_wda, used_ports)
        
        return flask_port, appium_port, wda_port
    
    def _find_available_port(self, base_port: int, used_ports: set) -> int:
        """Find the next available port starting from base_port"""
        port = base_port
        while port in used_ports or self._is_port_in_use(port):
            port += 1
            if port > base_port + 100:  # Safety limit
                raise RuntimeError(f"Could not find available port starting from {base_port}")
        return port
    
    def _is_port_in_use(self, port: int) -> bool:
        """Check if a port is currently in use"""
        try:
            import socket
            with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
                return s.connect_ex(('localhost', port)) == 0
        except Exception:
            return False
    
    def _initialize_session_databases(self, session_config: Dict):
        """Initialize isolated databases for the session"""
        databases = session_config["databases"]
        
        # Copy database schemas from templates or create new ones
        for db_name, db_path in databases.items():
            self._create_database(db_path, db_name)
    
    def _create_database(self, db_path: str, db_type: str):
        """Create and initialize a database file"""
        try:
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()
            
            if db_type == "test_execution":
                # Create test execution tables
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS test_cases (
                        id TEXT PRIMARY KEY,
                        name TEXT NOT NULL,
                        description TEXT,
                        created_date TEXT,
                        updated_date TEXT
                    )
                ''')
                
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS test_steps (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        test_case_id TEXT,
                        step_number INTEGER,
                        action_type TEXT,
                        action_params TEXT,
                        created_date TEXT,
                        FOREIGN KEY (test_case_id) REFERENCES test_cases (id)
                    )
                ''')
                
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS execution_tracking (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        test_execution_id TEXT,
                        test_case_id TEXT,
                        step_idx INTEGER,
                        action_id TEXT,
                        status TEXT,
                        start_time TEXT,
                        end_time TEXT,
                        retry_count INTEGER DEFAULT 0,
                        last_error TEXT
                    )
                ''')
                
            elif db_type == "global_values":
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS global_values (
                        name TEXT PRIMARY KEY,
                        value TEXT,
                        type TEXT
                    )
                ''')
                
                # Insert default values
                default_values = [
                    ('default_element_timeout', '60', 'int'),
                    ('Test Run Retry', '2', 'int'),
                    ('Auto Rerun Failed', 'False', 'bool'),
                    ('Test Case Delay', '15', 'int'),
                    ('Max Step Execution Time', '300', 'int')
                ]
                
                cursor.executemany(
                    'INSERT OR IGNORE INTO global_values (name, value, type) VALUES (?, ?, ?)',
                    default_values
                )
                
            elif db_type == "settings":
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS settings (
                        key TEXT PRIMARY KEY,
                        value TEXT,
                        category TEXT,
                        updated_at TEXT
                    )
                ''')
            
            conn.commit()
            conn.close()
            
            logger.info(f"Initialized {db_type} database at {db_path}")
            
        except Exception as e:
            logger.error(f"Failed to create database {db_path}: {e}")
    
    def get_session(self, session_id: str) -> Optional[Dict]:
        """Get session configuration by ID"""
        return self.sessions.get(session_id)
    
    def get_session_by_device(self, device_id: str) -> Optional[Dict]:
        """Get active session for a device"""
        for session in self.sessions.values():
            if session.get("device_id") == device_id and session.get("status") != "terminated":
                return session
        return None
    
    def list_active_sessions(self) -> List[Dict]:
        """List all active sessions"""
        return [s for s in self.sessions.values() if s.get("status") != "terminated"]
    
    def start_session(self, session_id: str) -> bool:
        """
        Start a session by launching the automation tool with isolated configuration
        
        Args:
            session_id: Session identifier
            
        Returns:
            True if session started successfully
        """
        session = self.get_session(session_id)
        if not session:
            logger.error(f"Session {session_id} not found")
            return False
        
        try:
            # Set environment variables for isolation
            env = os.environ.copy()
            env.update(session["environment"])
            
            # Build command to start the automation tool
            ports = session["ports"]
            cmd = [
                sys.executable, "run.py",
                "--port", str(ports["flask"]),
                "--appium-port", str(ports["appium"]),
                "--wda-port", str(ports["wda"])
            ]
            
            # Start the process
            process = subprocess.Popen(
                cmd,
                env=env,
                cwd=str(self.base_dir),
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE
            )
            
            # Update session status
            session["status"] = "running"
            session["process_id"] = process.pid
            session["started_at"] = datetime.now().isoformat()
            
            self._save_session_registry()
            
            logger.info(f"Started session {session_id} with PID {process.pid}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to start session {session_id}: {e}")
            return False
    
    def stop_session(self, session_id: str) -> bool:
        """
        Stop a running session
        
        Args:
            session_id: Session identifier
            
        Returns:
            True if session stopped successfully
        """
        session = self.get_session(session_id)
        if not session:
            logger.error(f"Session {session_id} not found")
            return False
        
        try:
            # Kill the process if it's running
            if "process_id" in session:
                try:
                    os.kill(session["process_id"], 9)  # SIGKILL
                except ProcessLookupError:
                    pass  # Process already dead
            
            # Update session status
            session["status"] = "stopped"
            session["stopped_at"] = datetime.now().isoformat()
            
            self._save_session_registry()
            
            logger.info(f"Stopped session {session_id}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to stop session {session_id}: {e}")
            return False
    
    def cleanup_session(self, session_id: str, remove_data: bool = False) -> bool:
        """
        Clean up a session and optionally remove its data
        
        Args:
            session_id: Session identifier
            remove_data: Whether to remove session data files
            
        Returns:
            True if cleanup successful
        """
        session = self.get_session(session_id)
        if not session:
            logger.error(f"Session {session_id} not found")
            return False
        
        try:
            # Stop the session first
            self.stop_session(session_id)
            
            # Remove data if requested
            if remove_data:
                session_dir = Path(session["directories"]["session"])
                if session_dir.exists():
                    import shutil
                    shutil.rmtree(session_dir)
                    logger.info(f"Removed session data directory: {session_dir}")
            
            # Mark session as terminated
            session["status"] = "terminated"
            session["terminated_at"] = datetime.now().isoformat()
            
            self._save_session_registry()
            
            logger.info(f"Cleaned up session {session_id}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to cleanup session {session_id}: {e}")
            return False
    
    def get_session_url(self, session_id: str) -> Optional[str]:
        """Get the URL for accessing a session"""
        session = self.get_session(session_id)
        if not session:
            return None
        
        flask_port = session["ports"]["flask"]
        device_id = session["device_id"]
        platform = session["platform"]
        
        return f"http://localhost:{flask_port}?deviceId={device_id}&platform={platform}&sessionId={session_id}"

# Global instance
session_manager = SessionIsolationManager()